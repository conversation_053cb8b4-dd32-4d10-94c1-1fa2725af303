**数据库账户：**
`testuser --------- StrongPassword123!`
`root --------- Str0ng_P@ssw0rd`



## 关键性能指标

在MySQL性能监控中，您应该重点关注以下几类指标：

### 1. 连接与线程指标

```bash
mysql -e "SHOW GLOBAL STATUS LIKE 'Threads_%';"
mysql -e "SHOW GLOBAL STATUS LIKE 'Connection%';"
mysql -e "SHOW GLOBAL STATUS LIKE 'Max_used_connections';"
```

关键指标解读：

- Threads_connected：当前打开的连接数

- Threads_running：当前正在运行的线程数

- Connections：尝试连接MySQL的总次数

- Max_used_connections：服务器启动后最大并发连接数

判断标准：

- 如果Max_used_connections接近max_connections（配置的50），表示连接数可能不足

- 如果Threads_running持续偏高（例如>10），表示有大量并发查询

优化建议：

- 如果连接数经常接近上限，考虑适当增加max_connections

- 如果有大量空闲连接，可以优化应用程序的连接池设置

### 2. 缓冲池使用情况

```bash
mysql -e "SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_%';"
```

关键指标解读：

- Innodb_buffer_pool_reads：从磁盘读取的页数

- Innodb_buffer_pool_read_requests：从缓冲池读取的页请求数

- Innodb_buffer_pool_pages_free：缓冲池中的空闲页数

- Innodb_buffer_pool_pages_total：缓冲池总页数

判断标准：

- **缓冲池命中率 = 1 - (Innodb_buffer_pool_reads / Innodb_buffer_pool_read_requests)**

- **理想值应大于95%，低于90%可能需要优化**

优化建议：

- 如果命中率低于90%且有足够内存，考虑增加innodb_buffer_pool_size

- **如果内存有限，考虑分析和优化频繁访问的表和查询**

### 3. 查询性能指标

```bash
mysql -e "SHOW GLOBAL STATUS LIKE 'Slow_queries';"
mysql -e "SHOW GLOBAL STATUS LIKE 'Com_%';"
```

关键指标解读：

- Slow_queries：执行时间超过long_query_time的查询数

- Com_select/insert/update/delete：各类SQL语句执行次数

判断标准：

- **Slow_queries持续增长表示存在性能问题**

- 查看慢查询日志以识别具体的问题查询

优化建议：

- **分析慢查询日志（/var/log/mysql/mysql-slow.log）**

- **为频繁查询的字段添加适当的索引**

- 重构复杂查询或分解为多个简单查询

### 4. 表锁和行锁指标

```bash
mysql -e "SHOW GLOBAL STATUS LIKE '%lock%';"
```

关键指标解读：

- Innodb_row_lock_waits：行锁等待次数

- Innodb_row_lock_time_avg：行锁平均等待时间

- Table_locks_waited：表锁等待次数

判断标准：

- 如果Innodb_row_lock_waits或Table_locks_waited持续增加，表示锁冲突严重

优化建议：

- 优化事务，减少事务持有锁的时间

- 检查是否有未使用索引的UPDATE或DELETE操作

- 考虑将长事务拆分为多个小事务

## 基于您的环境特定优化建议

针对您的2GB内存、4CPU环境：

1. 内存管理：

- 定期监控free -m命令输出，确保MySQL不会导致系统内存不足

- **如果查询缓存命中率低，优先优化查询而非增加缓冲池大小**

2. **慢查询优化**：

- 定期分析慢查询日志：`mysqldumpslow /var/log/mysql/mysql-slow.log`

- 对频繁查询的表使用EXPLAIN分析执行计划：`EXPLAIN SELECT * FROM table WHERE...`

3. 连接管理：

- 如果连接数经常超过30（50的60%），考虑实施连接池

- 监控平均查询执行时间，过长可能表示查询优化或索引问题

4. I/O优化：

- 监控iostat命令输出，关注磁盘I/O瓶颈

- 考虑将临时表tmp_table_size进一步优化以减少磁盘I/O

5. **定期执行 `OPTIMIZE TABLE` 以维护索引和表结构**
- `OPTIMIZE TABLE`：随着数据的插入、更新和删除，表中的数据可能会产生碎片，导致查询性能下降。通过 OPTIMIZE TABLE 命令，MySQL 能够重新组织表数据和索引，减少碎片并提高查询速度。
- `ANALYZE TABLE`：该命令用于更新表的统计信息，帮助数据库优化器选择更高效的查询执行计划。

6. **表的完整性检查和修复:** 
- `CHECK TABLE`：用于检查表是否存在损坏，尤其是在异常关机或服务器崩溃后。定期检查表的完整性有助于发现潜在问题，防止数据丢失或损坏。
    >使用 `mysqlcheck --all-databases --check` 检查表是否损坏
- `REPAIR TABLE`：如果发现表有问题，尤其是 MyISAM 表，可以使用此命令进行修复。