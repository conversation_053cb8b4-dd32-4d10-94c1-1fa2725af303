{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "(gdb) 远程调试",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/main", // 本地编译生成的可执行文件路径
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "miDebuggerPath": "/usr/bin/gdb-multiarch", // 使用 gdb-multiarch
            "miDebuggerServerAddress": "*************:1234", // 远程GDB服务器地址
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
            "logging": {
                "engineLogging": true,
                "trace": true,
                "traceResponse": true
            },
            // "preLaunchTask": "GDB服务: 开启",
            // "postDebugTask": "GDB服务: 关闭",
        }
    ]
}