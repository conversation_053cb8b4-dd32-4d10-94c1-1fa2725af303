{
    "files.associations": {
        "mutex": "cpp",
        "algorithm": "cpp",
        "iostream": "cpp",
        "any": "cpp",
        "array": "cpp",
        "atomic": "cpp",
        "bit": "cpp",
        "*.tcc": "cpp",
        "bitset": "cpp",
        "cctype": "cpp",
        "chrono": "cpp",
        "clocale": "cpp",
        "cmath": "cpp",
        "condition_variable": "cpp",
        "cstdarg": "cpp",
        "cstddef": "cpp",
        "cstdint": "cpp",
        "cstdio": "cpp",
        "cstdlib": "cpp",
        "cstring": "cpp",
        "ctime": "cpp",
        "cwchar": "cpp",
        "cwctype": "cpp",
        "deque": "cpp",
        "list": "cpp",
        "map": "cpp",
        "set": "cpp",
        "unordered_map": "cpp",
        "unordered_set": "cpp",
        "vector": "cpp",
        "exception": "cpp",
        "functional": "cpp",
        "iterator": "cpp",
        "memory": "cpp",
        "memory_resource": "cpp",
        "numeric": "cpp",
        "optional": "cpp",
        "random": "cpp",
        "ratio": "cpp",
        "regex": "cpp",
        "string": "cpp",
        "string_view": "cpp",
        "system_error": "cpp",
        "tuple": "cpp",
        "type_traits": "cpp",
        "utility": "cpp",
        "fstream": "cpp",
        "initializer_list": "cpp",
        "iomanip": "cpp",
        "iosfwd": "cpp",
        "istream": "cpp",
        "limits": "cpp",
        "new": "cpp",
        "ostream": "cpp",
        "shared_mutex": "cpp",
        "sstream": "cpp",
        "stdexcept": "cpp",
        "streambuf": "cpp",
        "thread": "cpp",
        "typeinfo": "cpp",
        "variant": "cpp",
        "codecvt": "cpp",
        "forward_list": "cpp",
        "valarray": "cpp",
        "charconv": "cpp",
        "cinttypes": "cpp"
    },
    // 调试器设置
    "lldb.showDisassembly": "auto",
    // 保存时自动格式化
    "lldb.dereferencePointers": true,
    // 调试器模式
    "lldb.consoleMode": "commands",
    "C_Cpp.clang_format_style": "file",
    // 默认格式化样式
    "C_Cpp.clang_format_fallbackStyle": "LLVM",
    // 保存时自动格式化
    "editor.formatOnSave": true,
    // 为C++文件指定默认格式化工具 - 使用内置clang-format
    "[cpp]": {
        "editor.defaultFormatter": "llvm-vs-code-extensions.vscode-clangd"
    },
    // 禁用C++扩展功能以避免与clangd冲突
    "C_Cpp.intelliSenseEngine": "Disabled",
    "C_Cpp.errorSquiggles": "Disabled",
    "C_Cpp.autocomplete": "Disabled",
    // clangd配置 - 使用最新的clangd-18版本
    "clangd.arguments": [
        "--log=info",
        "--background-index",
        "--clang-tidy",
        "--completion-style=detailed",
        "--function-arg-placeholders=false",
        "--header-insertion=iwyu",
        "--suggest-missing-includes",
        "--compile-commands-dir=${workspaceFolder}/build",
        "--query-driver=/usr/bin/aarch64-linux-gnu-g++,/usr/bin/aarch64-linux-gnu-gcc"
    ],
    "clangd.path": "/usr/bin/clangd",
    // CMake intellisense配置
    "cmake.exportCompileCommandsFile": true,
    "cmake.copyCompileCommands": "${workspaceFolder}/build/compile_commands.json",
    // "cmake.buildArgs": [
    //   "-j12"
    // ],
    "cmake.configureArgs": [
        "-DCMAKE_C_COMPILER_LAUNCHER=ccache",
        "-DCMAKE_CXX_COMPILER_LAUNCHER=ccache",
        "-DCMAKE_UNITY_BUILD=ON",
        "-DCMAKE_UNITY_BUILD_BATCH_SIZE=8"
    ],
    "cmake.generator": "Ninja"
}