{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "CMake: 清理",
            "type": "shell",
            "detail": "CMake 清理配置文件",
            "command": "rm -rf ${workspaceFolder}/build",
            "problemMatcher": []
        },
        {
            "label": "GDB服务: 开启",
            "type": "shell",
            "detail": "开启开发板gdb服务",
            "command": "ssh root@192.168.4.136 'gdbserver 192.168.4.136:1234 /mnt/BlackBox/main'"
        },
        {
            "label": "GDB服务: 关闭",
            "type": "shell",
            "detail": "关闭开发板gdb服务",
            "command": "ssh root@192.168.4.136 'pkill gdbserver'"
        },
    ]
}