#ifndef MDBSTCPDATAHANDLER_H
#define MDBSTCPDATAHANDLER_H

#include "ModbusDataHandler.h"

class MdbsTcpDataHandler : public ModbusDataHandler {
  public:
    MdbsTcpDataHandler(uint32_t dbAddr);

    /**
     * @brief 获取 关联的数据库地址
     * @return 返回 数据库地址
     */
    const uint32_t getDbAddr() const { return m_dbAddress; };

  protected:
    void loadConfigFromFile(const YAML::Node &config) override;

  private:
    uint8_t m_state; // 设备状态
};

#endif // MDBSTCPDATAHANDLER_H