#include "MdbsTcpDataManager.h"
#include "UdpClientDataHandler.h"
#include <algorithm>

MdbsTcpDataManager::MdbsTcpDataManager() {
    // 初始化输入寄存器汇总信息
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_PCS_POWER] = {0, 0, 1, sizeof(uint16_t) * 8, "总有功功率", true};
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_PCS_REACTIVE_POWER] = {0,   0, 1, sizeof(uint16_t) * 8, "总无功功率",
                                                                          true};
    m_inputSummaryDataMap[InputSummaryIndex::AVG_SOC] = {0, 0, 1, sizeof(uint16_t) * 8, "总SOC", true};
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_VOLTAGE] = {0, 0, 10, sizeof(uint16_t) * 8, "总电压", true};
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_CURRENT] = {0, 0, 10, sizeof(int16_t) * 8, "总电流", true};
    m_inputSummaryDataMap[InputSummaryIndex::AVG_CELL_TEMP] = {0, 0, 0.1, sizeof(uint16_t) * 8, "电池平均温度", true};
    m_inputSummaryDataMap[InputSummaryIndex::MAX_CELL_TEMP_DIFF] = {0,   0, 1, sizeof(uint16_t) * 8, "电池最大温差",
                                                                    true};
    m_inputSummaryDataMap[InputSummaryIndex::MAX_CELL_VOLTAGE] = {0,   0, 1000, sizeof(uint16_t) * 8, "最高单体电压值",
                                                                  true};
    m_inputSummaryDataMap[InputSummaryIndex::MIN_CELL_VOLTAGE] = {0,   0, 1000, sizeof(uint16_t) * 8, "最低单体电压值",
                                                                  true};
    m_inputSummaryDataMap[InputSummaryIndex::SYSTEM_STATE] = {0, 0, 1, sizeof(uint16_t) * 8, "系统状态", true};

    // 初始化保持寄存器汇总信息
    m_holdingSummaryDataMap[HoldingSummaryIndex::TOTAL_POWER] = {0, 0, 1, sizeof(uint16_t) * 8, "总有功功率", false};
    m_holdingSummaryDataMap[HoldingSummaryIndex::TOTAL_REACTIVE_POWER] = {
        0, 0, 1, sizeof(uint16_t) * 8, "总无功功率", false};
}

void MdbsTcpDataManager::addHandler(uint32_t dbAddr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (isTemp) {
        if (m_tempHandlers.find(dbAddr) == m_tempHandlers.end()) {
            m_tempHandlers[dbAddr] = std::make_shared<MdbsTcpDataHandler>(dbAddr);
        }
    } else {
        if (m_formalHandlers.find(dbAddr) == m_formalHandlers.end()) {
            m_formalHandlers[dbAddr] = std::make_shared<MdbsTcpDataHandler>(dbAddr);
        }
    }
}

std::shared_ptr<MdbsTcpDataHandler> MdbsTcpDataManager::getHandler(uint32_t dbAddr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (isTemp) {
        auto it = m_tempHandlers.find(dbAddr);
        if (it != m_tempHandlers.end()) {
            return it->second;
        }
    } else {
        auto it = m_formalHandlers.find(dbAddr);
        if (it != m_formalHandlers.end()) {
            return it->second;
        }
    }
    return nullptr;
}

void MdbsTcpDataManager::removeHandler(uint32_t dbAddr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (isTemp) {
        m_tempHandlers.erase(dbAddr);
    } else {
        m_formalHandlers.erase(dbAddr);
    }
}

void MdbsTcpDataManager::replaceTemp2Formal(uint32_t dbAddr) {
    m_formalHandlers[dbAddr] = std::move(m_tempHandlers[dbAddr]);
    m_tempHandlers.erase(dbAddr);
}

void MdbsTcpDataManager::updateSummaryInfo(const InputSummaryData &summaryData) {
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_PCS_POWER].value = summaryData.totalPcsPower;
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_PCS_REACTIVE_POWER].value = summaryData.totalPcsReactivePower;
    m_inputSummaryDataMap[InputSummaryIndex::AVG_SOC].value = summaryData.totalSoc;
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_VOLTAGE].value = summaryData.totalVoltage;
    m_inputSummaryDataMap[InputSummaryIndex::TOTAL_CURRENT].value = summaryData.totalCurrent;
    m_inputSummaryDataMap[InputSummaryIndex::AVG_CELL_TEMP].value = summaryData.avgCellTemp;
    m_inputSummaryDataMap[InputSummaryIndex::MAX_CELL_TEMP_DIFF].value = summaryData.maxCellTempDiff;
    m_inputSummaryDataMap[InputSummaryIndex::MAX_CELL_VOLTAGE].value = summaryData.maxCellVoltage;
    m_inputSummaryDataMap[InputSummaryIndex::MIN_CELL_VOLTAGE].value = summaryData.minCellVoltage;
    m_inputSummaryDataMap[InputSummaryIndex::SYSTEM_STATE].value = summaryData.systemState;
}

void MdbsTcpDataManager::updateEmsInfo() {

    int onlineCount = 0;
    double totalSocSum = 0.0;
    double totalVoltageSum = 0.0;
    double totalCurrentSum = 0.0;
    double totalAvgCellTempSum = 0.0;
    double maxCellTempDiff = 0.0;
    double maxCellVoltage = std::numeric_limits<double>::lowest();
    double minCellVoltage = std::numeric_limits<double>::max();

    std::vector<int> deviceEmsStates; // 存储各个设备的EMS系统状态

    InputSummaryData summaryData = {}; // 初始化所有成员为0

    // 定义EMS系统状态的优先级列表，越前面优先级越高
    const static std::vector<int> emsStatePriority = {
        3, // 升级中
        2, // 运行
        6, // 故障
        4, // 保护
        5, // 告警
        1, // 待机
        0, // 停止
    };

    // 定义子设备状态码到EMS系统状态码的映射
    const static std::unordered_map<int, int> subStateToEmsState = {
        {0, 2},  // 自检 -> 运行
        {1, 0},  // 停机 -> 停止
        {2, 1},  // 待机 -> 待机
        {3, 2},  // 并网充电 -> 运行
        {4, 2},  // 并网放电 -> 运行
        {5, 2},  // 离网放电 -> 运行
        {7, 4},  // 系统充电保护 -> 保护
        {8, 4},  // 系统放电保护 -> 保护
        {9, 4},  // 系统强制保电中 -> 保护
        {16, 5}, // 系统告警 -> 告警
        {17, 6}, // 系统故障 -> 故障
        {18, 6}, // 系统致命故障 -> 故障
        {20, 3}, // 系统升级中 -> 升级中
    };

    // 假设要遍历所有FORMAL设备
    auto &udpHandler = UdpClientDataHandler::getInstance();
    for (const auto &pair : m_formalHandlers) {
        uint32_t dbAddr = pair.first;
        // 先检查是否在线
        if (!udpHandler.isClientOffline(dbAddr)) {
            auto handler = pair.second;
            if (handler) {
                // 计算汇总信息

                // 累加总PCS有功功率
                std::string pcsPowerStr = handler->getDataByCategory({"输入寄存器", "PCS数据"}, "总-交流有功功率");
                if (!pcsPowerStr.empty()) {
                    summaryData.totalPcsPower += std::stod(pcsPowerStr);
                }

                // 累加总PCS无功功率
                std::string pcsReactivePowerStr =
                    handler->getDataByCategory({"输入寄存器", "PCS数据"}, "总-电网无功功率");
                if (!pcsReactivePowerStr.empty()) {
                    summaryData.totalPcsReactivePower += std::stod(pcsReactivePowerStr);
                }

                // 累加SOC
                std::string socStr = handler->getDataByCategory({"保持寄存器", "BmsBcuData1"}, "SOC");
                if (!socStr.empty()) {
                    totalSocSum += std::stod(socStr);
                }

                // 累加总电压
                std::string voltageStr =
                    handler->getDataByCategory({"保持寄存器", "BmsBcuData1"}, "BCU-Internal-Sum-Voltage");
                if (!voltageStr.empty()) {
                    totalVoltageSum += std::stod(voltageStr);
                }

                // 累加总电流
                std::string currentStr = handler->getDataByCategory({"保持寄存器", "BmsBcuData1"}, "BCU-Current");
                if (!currentStr.empty()) {
                    summaryData.totalCurrent += std::stod(currentStr);
                }

                // 计算每个设备的平均电池温度
                double deviceTempSum = 0.0;
                int tempCount = 0;

                const std::vector<std::string> bmsBmuDataList = {"BmsBmuData1", "BmsBmuData2", "BmsBmuData3",
                                                                 "BmsBmuData4"};
                for (const auto &bmsBmuData : bmsBmuDataList) {
                    for (int tempIndex = 1; tempIndex <= 32; ++tempIndex) {
                        std::string tempName = "Temperature" + std::to_string(tempIndex);
                        std::string tempValueStr = handler->getDataByCategory({"保持寄存器", bmsBmuData}, tempName);

                        if (!tempValueStr.empty()) {
                            double tempValue = std::stod(tempValueStr);
                            deviceTempSum += tempValue;
                            ++tempCount;
                        }
                    }
                }

                if (tempCount > 0) {
                    double deviceAvgTemp = deviceTempSum / tempCount;
                    totalAvgCellTempSum += deviceAvgTemp;
                }

                // 计算最大温差
                std::string maxTempStr = handler->getDataByCategory({"保持寄存器", "BmsBcuData1"}, "MaxTemperature");
                std::string minTempStr = handler->getDataByCategory({"保持寄存器", "BmsBcuData1"}, "MinTemperature");
                if (!maxTempStr.empty() && !minTempStr.empty()) {
                    double maxTemp = std::stod(maxTempStr);
                    double minTemp = std::stod(minTempStr);
                    double deviceTempDiff = maxTemp - minTemp;

                    summaryData.maxCellTempDiff =
                        (deviceTempDiff > summaryData.maxCellTempDiff) ? deviceTempDiff : summaryData.maxCellTempDiff;
                }

                // 更新最高单体电压值
                std::string deviceMaxCellVoltageStr =
                    handler->getDataByCategory({"保持寄存器", "BmsBcuData1"}, "Max-Cell-Voltage");
                if (!deviceMaxCellVoltageStr.empty()) {
                    double deviceMaxCellVoltage = std::stod(deviceMaxCellVoltageStr);

                    summaryData.maxCellVoltage = (deviceMaxCellVoltage > summaryData.maxCellVoltage)
                                                     ? deviceMaxCellVoltage
                                                     : summaryData.maxCellVoltage;
                }

                // 更新最低单体电压值
                std::string deviceMinCellVoltageStr =
                    handler->getDataByCategory({"保持寄存器", "BmsBcuData1"}, "Min-Cell-Voltage");
                if (!deviceMinCellVoltageStr.empty()) {
                    double deviceMinCellVoltage = std::stod(deviceMinCellVoltageStr);

                    if (deviceMinCellVoltage < minCellVoltage) {
                        minCellVoltage = deviceMinCellVoltage;
                    }
                    summaryData.minCellVoltage =
                        (deviceMinCellVoltage < minCellVoltage) ? deviceMinCellVoltage : minCellVoltage;
                }

                // 获取子设备的系统状态
                std::string deviceStateStr = handler->getDataByCategory({"输入寄存器", "系统数据"}, "系统状态");
                if (!deviceStateStr.empty()) {
                    int deviceState = std::stoi(deviceStateStr);

                    // 映射到EMS系统状态
                    int emsState = 0; // 默认为停止
                    auto it = subStateToEmsState.find(deviceState);
                    if (it != subStateToEmsState.end()) {
                        emsState = it->second;
                    }
                    deviceEmsStates.push_back(emsState);
                }

                onlineCount++;
            }
        }
    }

    summaryData.totalSoc = (onlineCount > 0) ? (totalSocSum / onlineCount) : 0.0;
    summaryData.totalVoltage = (onlineCount > 0) ? (totalVoltageSum / onlineCount) : 0.0;
    summaryData.avgCellTemp = (onlineCount > 0) ? (totalAvgCellTempSum / onlineCount) : 0.0;

    // 根据优先级确定EMS系统状态
    summaryData.systemState = 0; // 默认状态为停止
    for (int priorityState : emsStatePriority) {
        if (std::find(deviceEmsStates.begin(), deviceEmsStates.end(), priorityState) != deviceEmsStates.end()) {
            summaryData.systemState = priorityState;
            break; // 找到最高优先级的状态，退出循环
        }
    }

    // 更新汇总信息
    updateSummaryInfo(summaryData);
}

// std::vector<ModbusDataType> MdbsTcpDataManager::getRawSummaryData(uint16_t startAddress, uint16_t length,
//                                                                   const RegisterType registerType) {
//     std::vector<ModbusDataType> buffer;

//     if (registerType == RegisterType::InputRegister) {
//         for (uint16_t addr = startAddress; addr < startAddress + length; ++addr) {
//             InputSummaryIndex summaryIndex = static_cast<InputSummaryIndex>(addr);
//             auto it = m_inputSummaryDataMap.find(summaryIndex);

//             if (it != m_inputSummaryDataMap.end()) {
//                 const SummaryInfo &info = it->second;
//                 getDatas(addr, startAddress, length, info, buffer);
//             } else {
//                 throw std::runtime_error("找不到对应的输入寄存器汇总信息地址。");
//             }
//         }
//     } else if (registerType == RegisterType::HoldingRegister) {
//         for (uint16_t addr = startAddress; addr < startAddress + length; ++addr) {
//             HoldingSummaryIndex summaryIndex = static_cast<HoldingSummaryIndex>(addr);
//             auto it = m_holdingSummaryDataMap.find(summaryIndex);

//             if (it != m_holdingSummaryDataMap.end()) {
//                 const SummaryInfo &info = it->second;
//                 getDatas(addr, startAddress, length, info, buffer);
//             } else {
//                 throw std::runtime_error("找不到对应的保持寄存器汇总信息地址。");
//             }
//         }
//     } else {
//         throw std::runtime_error("未知的寄存器类型。");
//     }

//     return buffer;
// }

bool MdbsTcpDataManager::getRawSummaryData(uint16_t startAddress, uint16_t length, const RegisterType registerType,
                                           std::vector<ModbusDataType> &buffer) {
    if (registerType == RegisterType::InputRegister) {
        for (uint16_t addr = startAddress; addr < startAddress + length; ++addr) {
            InputSummaryIndex summaryIndex = static_cast<InputSummaryIndex>(addr);
            auto it = m_inputSummaryDataMap.find(summaryIndex);

            if (it != m_inputSummaryDataMap.end()) {
                const SummaryInfo &info = it->second;
                if (!getDatas(addr, startAddress, length, info, buffer)) {
                    return false; // 数据获取失败
                }
            } else {
                return false; // 未找到对应的汇总信息地址
            }
        }
    } else if (registerType == RegisterType::HoldingRegister) {
        for (uint16_t addr = startAddress; addr < startAddress + length; ++addr) {
            HoldingSummaryIndex summaryIndex = static_cast<HoldingSummaryIndex>(addr);
            auto it = m_holdingSummaryDataMap.find(summaryIndex);

            if (it != m_holdingSummaryDataMap.end()) {
                const SummaryInfo &info = it->second;
                if (!getDatas(addr, startAddress, length, info, buffer)) {
                    return false; // 数据获取失败
                }
            } else {
                return false; // 未找到对应的汇总信息地址
            }
        }
    } else {
        return false; // 未知的寄存器类型
    }

    return true; // 成功获取数据
}

// template <typename EnumType>
// double MdbsTcpDataManager::getSummaryValue(const RegisterType registerType, EnumType index) {
//     if (registerType == RegisterType::InputRegister) {
//         if constexpr (std::is_same<EnumType, InputSummaryIndex>::value) {
//             auto it = m_inputSummaryDataMap.find(index);
//             if (it != m_inputSummaryDataMap.end()) {
//                 const SummaryInfo &info = it->second;
//                 return info.value; // 物理值已经在 writeHoldingSummaryData 中更新
//             } else {
//                 throw std::runtime_error("Invalid InputSummaryIndex");
//             }
//         } else {
//             // 如果传入了其他不支持的类型
//             throw std::runtime_error("Unsupported enum type");
//         }
//     } else if (registerType == RegisterType::HoldingRegister) {
//         if constexpr (std::is_same<EnumType, HoldingSummaryIndex>::value) {
//             auto it = m_holdingSummaryDataMap.find(index);
//             if (it != m_holdingSummaryDataMap.end()) {
//                 const SummaryInfo &info = it->second;
//                 return info.value; // 物理值已经在 writeHoldingSummaryData 中更新
//             } else {
//                 throw std::runtime_error("Invalid HoldingSummaryIndex");
//             }
//         } else {
//             // 如果传入了其他不支持的类型
//             throw std::runtime_error("Unsupported enum type");
//         }
//     } else {
//         throw std::runtime_error("Invalid register type ");
//     }
// }

template <typename EnumType>
bool MdbsTcpDataManager::getSummaryValue(const RegisterType registerType, EnumType index, double &value) {
    if (registerType == RegisterType::InputRegister) {
        if constexpr (std::is_same<EnumType, InputSummaryIndex>::value) {
            auto it = m_inputSummaryDataMap.find(index);
            if (it != m_inputSummaryDataMap.end()) {
                const SummaryInfo &info = it->second;
                value = info.value;
                return true;
            } else {
                return false; // 未找到对应的索引
            }
        } else {
            return false; // 不支持的枚举类型
        }
    } else if (registerType == RegisterType::HoldingRegister) {
        if constexpr (std::is_same<EnumType, HoldingSummaryIndex>::value) {
            auto it = m_holdingSummaryDataMap.find(index);
            if (it != m_holdingSummaryDataMap.end()) {
                const SummaryInfo &info = it->second;
                value = info.value;
                return true;
            } else {
                return false; // 未找到对应的索引
            }
        } else {
            return false; // 不支持的枚举类型
        }
    } else {
        return false; // 无效的寄存器类型
    }
}

// 显式实例化
// template double MdbsTcpDataManager::getSummaryValue<HoldingSummaryIndex>(const RegisterType registerType,
//                                                                          HoldingSummaryIndex index);
// template double MdbsTcpDataManager::getSummaryValue<InputSummaryIndex>(const RegisterType registerType,
//                                                                        InputSummaryIndex index);

template bool MdbsTcpDataManager::getSummaryValue<HoldingSummaryIndex>(const RegisterType registerType,
                                                                       HoldingSummaryIndex index, double &value);
template bool MdbsTcpDataManager::getSummaryValue<InputSummaryIndex>(const RegisterType registerType,
                                                                     InputSummaryIndex index, double &value);

// void MdbsTcpDataManager::getDatas(uint16_t &addr, uint16_t startAddress, uint16_t length, const SummaryInfo &info,
//                                   std::vector<ModbusDataType> &buffer) {
//     // double类型转换成数据相应的类型
//     if (info.size == 16) {
//         buffer.push_back(static_cast<uint16_t>((info.value / info.conversionFactor) + info.offset));
//     } else if (info.size == 32) {
//         // 检查剩余寄存器是否足够
//         if (addr + 1 >= startAddress + length) {
//             // 如果剩余寄存器不足以读取32位数据，返回错误
//             throw std::runtime_error("读取的寄存器长度不足以满足数据的完整性。");
//         }

//         buffer.push_back(static_cast<uint32_t>((info.value / info.conversionFactor) + info.offset));

//         // 跳过下一个地址，因为 32 位数据占用了两个寄存器
//         ++addr;
//     }
// }

bool MdbsTcpDataManager::getDatas(uint16_t &addr, uint16_t startAddress, uint16_t length, const SummaryInfo &info,
                                  std::vector<ModbusDataType> &buffer) {
    // 根据数据类型转换 double 类型的数据
    if (info.size == 16) {
        buffer.push_back(static_cast<uint16_t>((info.value / info.conversionFactor) + info.offset));
    } else if (info.size == 32) {
        // 检查剩余寄存器是否足够
        if (addr + 1 >= startAddress + length) {
            // 如果剩余寄存器不足，返回失败
            return false;
        }

        buffer.push_back(static_cast<uint32_t>((info.value / info.conversionFactor) + info.offset));

        // 跳过下一个地址，因为 32 位数据占用了两个寄存器
        ++addr;
    } else {
        // 不支持的数据大小
        return false;
    }
    return true; // 成功获取数据
}

// uint16_t MdbsTcpDataManager::writeHoidingSummaryData(uint16_t startAddress, const std::vector<uint16_t> &data) {
//     uint16_t registersWritten = 0;

//     for (size_t i = 0; i < data.size(); ++i) {
//         uint16_t addr = startAddress + i;
//         HoldingSummaryIndex holdingSummaryIndex = static_cast<HoldingSummaryIndex>(addr);
//         auto it = m_holdingSummaryDataMap.find(holdingSummaryIndex);

//         if (it != m_holdingSummaryDataMap.end()) {
//             SummaryInfo &info = it->second;

//             if (info.readOnly) {
//                 // 如果是只读属性,则跳过写入操作
//                 continue;
//             }

//             // 根据数据类型进行写入操作
//             if (info.size == 16) {
//                 info.value = static_cast<double>(data[i] - info.offset) * info.conversionFactor;
//                 ++registersWritten;
//             } else if (info.size == 32) {
//                 // 检查剩余数据是否足够
//                 if (i + 1 >= data.size()) {
//                     // 如果剩余数据不足以写入32位数据,抛出异常
//                     throw std::runtime_error("写入的数据长度不足以满足32位数据的完整性。");
//                 }

//                 // 32位 中端字节序
//                 uint32_t value = (static_cast<uint32_t>(data[i + 1]) << 16) | static_cast<uint32_t>(data[i]);
//                 info.value = static_cast<double>((value - info.offset) * info.conversionFactor);
//                 registersWritten += 2;
//                 ++i; // 跳过下一个地址,因为32位数据占用了两个寄存器
//             }
//         } else {
//             // 如果找不到对应的地址,抛出异常
//             throw std::runtime_error("找不到对应的汇总信息地址。");
//         }
//     }

//     return registersWritten;
// }

bool MdbsTcpDataManager::writeHoidingSummaryData(uint16_t startAddress, const std::vector<uint16_t> &data,
                                                 uint16_t &registersWritten) {
    registersWritten = 0;

    for (size_t i = 0; i < data.size(); ++i) {
        uint16_t addr = startAddress + static_cast<uint16_t>(i);
        HoldingSummaryIndex holdingSummaryIndex = static_cast<HoldingSummaryIndex>(addr);
        auto it = m_holdingSummaryDataMap.find(holdingSummaryIndex);

        if (it != m_holdingSummaryDataMap.end()) {
            SummaryInfo &info = it->second;

            if (info.readOnly) {
                // 如果是只读属性，跳过写入操作
                continue;
            }

            // 根据数据类型进行写入操作
            if (info.size == 16) {
                info.value = static_cast<double>(data[i] - info.offset) * info.conversionFactor;
                ++registersWritten;
            } else if (info.size == 32) {
                // 检查剩余数据是否足够
                if (i + 1 >= data.size()) {
                    // 数据长度不足，返回失败
                    return false;
                }

                // 32位 中端字节序
                uint32_t value = (static_cast<uint32_t>(data[i + 1]) << 16) | static_cast<uint32_t>(data[i]);
                info.value = static_cast<double>((value - info.offset) * info.conversionFactor);
                registersWritten += 2;
                ++i; // 跳过下一个地址
            } else {
                // 不支持的数据大小
                return false;
            }
        } else {
            // 未找到对应的汇总信息地址
            return false;
        }
    }

    return true; // 写入成功
}
