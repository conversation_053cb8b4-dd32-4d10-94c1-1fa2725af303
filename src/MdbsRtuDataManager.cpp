#include "MdbsRtuDataManager.h"
#include "ConfigManager.h"
#include "Logger.h"

MdbsRtuDataManager::MdbsRtuDataManager() {
    // 初始化当前协议的处理器
    loadConfig();
}

void MdbsRtuDataManager::loadConfig() {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    // 读取当前协议ID
    if (config["currMetertProtId"]) {
        m_currentProtocolId = config["currMetertProtId"].as<std::string>();
    } else {
        LOG_ERROR << "未找到currMetertProtId字段。";
        return;
    }

    // 根据当前协议ID加载对应的寄存器配置
    std::string protocolKey = "Protocol_" + m_currentProtocolId;

    // 创建MdbsRtuDataHandler并加载配置
    m_currentHandler = std::make_shared<MdbsRtuDataHandler>(protocolKey);
}

void MdbsRtuDataManager::reloadConfig() {
    // 重新加载配置文件
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    // 读取新的协议ID
    std::string newProtocolId;
    if (config["currMetertProtId"]) {
        newProtocolId = config["currMetertProtId"].as<std::string>();
    } else {
        LOG_ERROR << "未找到currMetertProtId字段。";
        return;
    }

    // 检查协议ID是否发生变化
    if (newProtocolId != m_currentProtocolId) {
        // 更新当前协议ID
        m_currentProtocolId = newProtocolId;

        // 根据当前协议ID加载对应的寄存器配置
        std::string protocolKey = "Protocol_" + m_currentProtocolId;

        // 创建MdbsRtuDataHandler并加载配置
        m_currentHandler = std::make_shared<MdbsRtuDataHandler>(protocolKey);
    } else {
        LOG_INFO << "协议ID未变化, 保持当前协议。";
    }
}

std::shared_ptr<MdbsRtuDataHandler> MdbsRtuDataManager::getHandler() {
    return m_currentHandler;
}

float MdbsRtuDataManager::calculateVoltage(float URMSx) {
    // 获取寄存器 PT 的值
    std::string PTStr = m_currentHandler->getDataByCategory({"保持寄存器", "二次侧电参量数据"}, "PT");
    if (PTStr.empty()) {
        return -1;
    }

    float UrAt = std::stod(PTStr); // 将字符串转换为 float

    return URMSx * UrAt;
}

float MdbsRtuDataManager::calculateCurrent(float IRMSx) {
    // 获取寄存器 CT 的值
    std::string CTStr = m_currentHandler->getDataByCategory({"保持寄存器", "二次侧电参量数据"}, "CT");
    if (CTStr.empty()) {
        return -1;
    }

    float IrAt = std::stod(CTStr); // 将字符串转换为 float

    return IRMSx * IrAt;
}

float MdbsRtuDataManager::calculateActivePower(float Px) {
    // 获取寄存器 PT 和 CT 的值
    std::string PTStr = m_currentHandler->getDataByCategory({"保持寄存器", "二次侧电参量数据"}, "PT");
    std::string CTStr = m_currentHandler->getDataByCategory({"保持寄存器", "二次侧电参量数据"}, "CT");
    if (CTStr.empty() || PTStr.empty()) {
        return -1;
    }

    float UrAt = std::stod(PTStr); // 将字符串转换为 float
    float IrAt = std::stod(CTStr); // 将字符串转换为 float

    return Px * UrAt * IrAt;
}

float MdbsRtuDataManager::calculateReactivePower(float Qx) {
    // 获取寄存器 PT 和 CT 的值
    std::string PTStr = m_currentHandler->getDataByCategory({"保持寄存器", "二次侧电参量数据"}, "PT");
    std::string CTStr = m_currentHandler->getDataByCategory({"保持寄存器", "二次侧电参量数据"}, "CT");
    if (CTStr.empty() || PTStr.empty()) {
        return -1;
    }

    float UrAt = std::stod(PTStr); // 将字符串转换为 float
    float IrAt = std::stod(CTStr); // 将字符串转换为 float

    return Qx * UrAt * IrAt;
}

float MdbsRtuDataManager::calculateEnergy(float E) {
    // 获取寄存器 PT 和 CT 的值
    std::string PTStr = m_currentHandler->getDataByCategory({"保持寄存器", "PCS数据"}, "PT");
    std::string CTStr = m_currentHandler->getDataByCategory({"保持寄存器", "PCS数据"}, "CT");
    if (CTStr.empty() || PTStr.empty()) {
        return -1;
    }

    float UrAt = std::stod(PTStr); // 将字符串转换为 float
    float IrAt = std::stod(CTStr); // 将字符串转换为 float

    return E * UrAt * IrAt;
}