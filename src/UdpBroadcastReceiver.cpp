#include "UdpBroadcastReceiver.h"
#include "Logger.h"
#include <arpa/inet.h>
#include <cstring>
#include <iostream>
#include <sys/socket.h>
#include <unistd.h>
#include <chrono>
#include <sstream>
#include <iomanip>

UdpBroadcastReceiver::UdpBroadcastReceiver(int port) : m_sockfd(-1), m_port(port) {
}

UdpBroadcastReceiver::~UdpBroadcastReceiver() {
    if (m_sockfd != -1) {
        close(m_sockfd);
    }
}

bool UdpBroadcastReceiver::init() {
    if (m_sockfd != -1) {
        close(m_sockfd);
    }

    m_sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (m_sockfd < 0) {
        LOG_ERROR << "Failed to create socket";
        return false;
    }

    memset(&m_serverAddr, 0, sizeof(m_serverAddr));
    m_serverAddr.sin_family = AF_INET;
    m_serverAddr.sin_port = htons(m_port);
    m_serverAddr.sin_addr.s_addr = htonl(INADDR_ANY);

    if (bind(m_sockfd, (struct sockaddr *)&m_serverAddr, sizeof(m_serverAddr)) < 0) {
        LOG_ERROR << "Bind failed";
        return false;
    }

    return true;
}

void UdpBroadcastReceiver::receive() {
    fd_set readfds;
    char buffer[128];
    struct sockaddr_in clientAddr;
    socklen_t addrLen = sizeof(clientAddr);

    LOG_INFO << "Listening for broadcast messages on UDP m_port " << m_port << "...";

    FD_ZERO(&readfds);
    FD_SET(m_sockfd, &readfds);
    int maxfd = m_sockfd;

    struct timeval timeout;
    timeout.tv_sec = 0; // 不等待 立刻返回
    timeout.tv_usec = 0;

    int ret = select(maxfd + 1, &readfds, nullptr, nullptr, &timeout);
    if (ret < 0) {
        if (errno == EINTR) {
            LOG_ERROR << "select was interrupted by a signal. Retrying...";
            return;
        } else {
            perror("select failed");
            close(m_sockfd);
            init();
            return;
        }
    } else if (ret == 0) {
        // Timeout
        return;
    }

    if (FD_ISSET(m_sockfd, &readfds)) {
        ssize_t bytesReceived = recvfrom(m_sockfd, buffer, sizeof(buffer), 0, (struct sockaddr *)&clientAddr, &addrLen);
        if (bytesReceived > 0) {
            LOG_DEBUG << "Data in hex: " << [&]() {
                std::stringstream ss;
                for (ssize_t i = 0; i < bytesReceived; ++i) {
                    ss << std::hex << std::setw(2) << std::setfill('0')
                       << static_cast<int>(static_cast<unsigned char>(buffer[i])) << " ";
                }
                return ss.str();
            }();

            m_dataPackets.emplace_back(bytesReceived, buffer, clientAddr);
        } else {
            LOG_ERROR << "Error or no data received";
        }
    }
}