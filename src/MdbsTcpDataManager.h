#ifndef MDBSTCPDATAMANAGER_H
#define MDBSTCPDATAMANAGER_H

#include "MdbsTcpDataHandler.h"
#include "StructDefs.h"
#include <memory>
#include <mutex>
#include <unordered_map>
#include <variant>

class MdbsTcpDataManager {
  public:
    /**
     * @brief 获取 MdbsTcpDataManager 的唯一实例
     * @return 静态 MdbsTcpDataManager 实例的引用
     */
    static MdbsTcpDataManager &getInstance() {
        static MdbsTcpDataManager instance; // 静态局部变量，第一次调用时初始化，之后保持实例
        return instance;
    }

    // 禁止拷贝构造和赋值操作符
    MdbsTcpDataManager(const MdbsTcpDataManager &) = delete;
    MdbsTcpDataManager &operator=(const MdbsTcpDataManager &) = delete;

    // 禁止移动构造和移动赋值操作符
    MdbsTcpDataManager(MdbsTcpDataManager &&) = delete;
    MdbsTcpDataManager &operator=(MdbsTcpDataManager &&) = delete;

    /**
     * @brief 添加一个新的 MdbsTcpDataHandler 实例
     * @param dbAddr 设备的数据库地址
     * @param isTemp 如果为 true，则添加到临时管理器中
     */
    void addHandler(uint32_t dbAddr, bool isTemp);

    /**
     * @brief 获取指定数据库地址的 MdbsTcpDataHandler 实例
     * @param dbAddr 设备的数据库地址
     * @param isTemp 如果为 true，则从临时管理器中获取
     * @return 返回指向 MdbsTcpDataHandler 的指针，如果不存在返回 nullptr
     */
    std::shared_ptr<MdbsTcpDataHandler> getHandler(uint32_t dbAddr, bool isTemp);

    /**
     * @brief 删除指定数据库地址的 MdbsTcpDataHandler 实例
     * @param dbAddr 设备的数据库地址
     * @param isTemp 如果为 true，则从临时管理器中删除
     */
    void removeHandler(uint32_t dbAddr, bool isTemp);

    /**
     * @brief 将临时数据库表转到正式表中
     * @param dbAddr 设备的数据库地址
     */
    void replaceTemp2Formal(uint32_t dbAddr);

    /**
     * @brief 获取正式或临时的 MdbsTcpDataHandler 实例表
     * @param isTemp 如果为 true，则返回临时处理器表；否则返回正式处理器表
     * @return 返回处理器的映射表
     */
    inline const std::unordered_map<uint32_t, std::shared_ptr<MdbsTcpDataHandler>> &getHandlersMap(bool isTemp) const {
        return isTemp ? m_tempHandlers : m_formalHandlers;
    };

    /**
     * @brief 更新汇总信息
     * @param summaryData 存放待更新的汇总信息
     * 在每次 Modbus 数据解析完成后，更新汇总信息。
     */
    void updateSummaryInfo(const InputSummaryData &summaryData);

    /**
     * @brief 更新EMS汇总信息
     * 在每次 Modbus 数据解析完成后，更新汇总信息。
     */
    void updateEmsInfo();

    /**
     * @brief 获取原始汇总信息(未经加工)
     * @param startAddress 起始地址
     * @param length 需要读取的寄存器数量
     * @param buffer 用于存储返回的汇总数据的 vector
     * @return 成功/失败
     */
    // std::vector<ModbusDataType> getRawSummaryData(uint16_t startAddress, uint16_t length,
    //                                               const RegisterType registerType);
    bool getRawSummaryData(uint16_t startAddress, uint16_t length, const RegisterType registerType, std::vector<ModbusDataType> &buffer);

    /**
     * @brief 获取指定寄存器汇总信息的物理值
     * @param registerType 寄存器类型
     * @param index 汇总索引（例如 HoldingSummaryIndex::TOTAL_POWER）
     * @param value 用于存储返回的汇总数据的 value
     * @return 成功/失败 返回对应的物理值
     */
    // template <typename EnumType> double getSummaryValue(const RegisterType registerType, EnumType index);
    template <typename EnumType> bool getSummaryValue(const RegisterType registerType, EnumType index, double &value);

    /**
     * @brief 写入汇总信息
     * @param startAddress 起始地址
     * @param data 要写入的数据
     * @param registersWritten 用于存储返回写入的长度
     * @return 返回写入的寄存器数量,如果写入失败则抛出异常
     */
    // uint16_t writeHoidingSummaryData(uint16_t startAddress, const std::vector<uint16_t> &data);
    bool writeHoidingSummaryData(uint16_t startAddress, const std::vector<uint16_t> &data, uint16_t &registersWritten);

    std::mutex m_mutex; // 保护 m_formalHandlers 和 m_tempHandlers 的线程安全

  private:
    MdbsTcpDataManager();

    // void getDatas(uint16_t &addr, uint16_t startAddress, uint16_t length, const SummaryInfo &info,
    //               std::vector<ModbusDataType> &buffer);
    bool getDatas(uint16_t &addr, uint16_t startAddress, uint16_t length, const SummaryInfo &info, std::vector<ModbusDataType> &buffer);

    std::unordered_map<uint32_t, std::shared_ptr<MdbsTcpDataHandler>> m_formalHandlers; // 管理正式数据处理器 (键：dbAddr)。 用 shared_ptr 可以避免资源竞争问题，可以不用锁
    std::unordered_map<uint32_t, std::shared_ptr<MdbsTcpDataHandler>> m_tempHandlers; // 管理临时数据处理器 (键：dbAddr)。 用 shared_ptr 可以避免资源竞争问题，可以不用锁
    std::unordered_map<InputSummaryIndex, SummaryInfo> m_inputSummaryDataMap;     // 汇总信息的存储 (输入寄存器)
    std::unordered_map<HoldingSummaryIndex, SummaryInfo> m_holdingSummaryDataMap; // 汇总信息的存储 (保持寄存器)
};

#endif // MDBSTCPDATAMANAGER_H