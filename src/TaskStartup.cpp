#include "TaskStartup.h"
#include "ConfigManager.h"
#include "DatabaseAdapter.h"
#include "Logger.h"
#include <limits.h>
#include <unistd.h>

TaskStartup::TaskStartup() {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    if (config.IsNull()) {
        LOG_ERROR << "加载YAML文件失败或配置为空。";
        return;
    }

    // 初始化数据库
    if (!DatabaseAdapter::getInstance().initializeDatabase()) {
        LOG_ERROR << "初始化数据库失败，但程序将继续运行。";
    } else {
        LOG_INFO << "数据库初始化成功。";
    }

    m_manager = new (std::nothrow) TaskManager();
    if (!m_manager) {
        LOG_ERROR << "分配TaskManager内存失败。";
        return;
    }

    int udpPort = config["network"]["udp_port"].as<int>();
    m_udpTask = new (std::nothrow) UdpTask(udpPort);
    if (!m_udpTask) {
        LOG_ERROR << "分配UdpTask内存失败。";
        delete m_manager;
        m_manager = nullptr;
        return;
    }

    m_tcpTask = new (std::nothrow) TcpTask();
    if (!m_tcpTask) {
        LOG_ERROR << "分配TcpTask内存失败。";
        delete m_manager;
        delete m_udpTask;
        m_udpTask = nullptr;
        m_manager = nullptr;
        return;
    }

    m_rtuTask = new (std::nothrow) RtuTask();
    if (!m_rtuTask) {
        LOG_ERROR << "分配RtuTask内存失败。";
        delete m_manager;
        delete m_udpTask;
        delete m_tcpTask;
        m_udpTask = nullptr;
        m_manager = nullptr;
        m_tcpTask = nullptr;
        return;
    }
}

TaskStartup::~TaskStartup() {
    delete m_manager;
    delete m_udpTask;
    delete m_tcpTask;
    delete m_rtuTask;
}

void TaskStartup::Start() {
    TaskConfig UdpTask;
    UdpTask.name = "UdpTask";
    UdpTask.cycleTime = 8000;         // ms
    UdpTask.taskFunction = [this]() { // 成员指针 m_udpTask 需要通过 this 指针来访问
        m_udpTask->run();
    };
    m_configLists.push_back(UdpTask);

    TaskConfig TcpRxTask;
    TcpRxTask.name = "TcpRxTask";
    TcpRxTask.cycleTime = 8000; // ms TODO: 后期根据服务端数量进行调整
    TcpRxTask.taskFunction = [this]() {
        m_tcpTask->modbusRxTasks();
    };
    m_configLists.push_back(TcpRxTask);

    TaskConfig TcpTxTask;
    TcpTxTask.name = "TcpTxTask";
    TcpTxTask.cycleTime = 8000; // ms TODO: 后期根据服务端数量进行调整
    TcpTxTask.taskFunction = [this]() {
        m_tcpTask->modbusTxTasks();
    };
    m_configLists.push_back(TcpTxTask);

    TaskConfig checkUserInput;
    checkUserInput.name = "checkUserInput"; // 检查用户输入
    checkUserInput.cycleTime = 8000;        // ms
    checkUserInput.taskFunction = [this]() {
        m_tcpTask->checkUserInput();
    };
    m_configLists.push_back(checkUserInput);

    TaskConfig TcpSlaveTask;
    TcpSlaveTask.name = "TcpSlaveTask"; // 第三方访问modbus数据
    TcpSlaveTask.cycleTime = 8000;      // ms
    TcpSlaveTask.taskFunction = [this]() {
        m_tcpTask->TcpSlaveTask();
    };
    m_configLists.push_back(TcpSlaveTask);

    TaskConfig RtuCommTask;
    RtuCommTask.name = "RtuCommTask"; // 电表
    RtuCommTask.cycleTime = 8000;     // ms
    RtuCommTask.taskFunction = [this]() {
        m_rtuTask->RtuCommTask();
    };
    m_configLists.push_back(RtuCommTask);

    TaskConfig dbMonthlyCleanupTask;
    dbMonthlyCleanupTask.name = "dbMonthlyCleanupTask"; // 检查数据库过期数据
    dbMonthlyCleanupTask.cycleTime = 8000;              // ms
    dbMonthlyCleanupTask.taskFunction = [this]() {
        m_tcpTask->dbMonthlyCleanupTask();
    };
    m_configLists.push_back(dbMonthlyCleanupTask);

    TaskConfig dbMonitorTask;
    dbMonitorTask.name = "DatabaseMonitor";
    dbMonitorTask.cycleTime = 8000; // ms
    dbMonitorTask.taskFunction = []() {
        DatabaseMonitor::getInstance().monitorTask();
    };
    m_configLists.push_back(dbMonitorTask);

    m_manager->addTask(m_configLists);
    m_manager->startAllTasks();
}