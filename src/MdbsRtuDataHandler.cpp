#include "MdbsRtuDataHandler.h"
#include "Logger.h"

MdbsRtuDataHandler::MdbsRtuDataHandler(const std::string protocolKey)
    : ModbusDataHandler(), m_protocolKey(protocolKey) {
    initialize();
}

void MdbsRtuDataHandler::loadConfigFromFile(const YAML::Node &config) {
    // 与之前的实现相同
    if (config.IsNull()) {
        LOG_ERROR << "加载YAML文件失败或配置为空。";
        return;
    }

    // 使用引用避免拷贝
    const YAML::Node &categories = config["meterRegisterData"][m_protocolKey];
    const YAML::Node &readMap = config["meterReadMap"];

    if (categories) { // 检查指针是否为空并且节点有效
        for (const auto &categoryNode : categories) {
            std::string registerType = categoryNode.first.as<std::string>();
            const auto &subCategories = categoryNode.second;

            for (const auto &subCategoryNode : subCategories) {
                std::string subCategory = subCategoryNode.first.as<std::string>();
                const auto &registerDataList = subCategoryNode.second;

                // 处理寄存器数据
                parseRegisters(registerDataList, registerType, subCategory);
            }
        }
    } else {
        LOG_ERROR << "配置节点不存在或为空。";
    }

    // 解析读取映射
    if (!readMap) {
        LOG_ERROR << "未找到 ReadMap 配置节点。";
        return;
    }

    parseReadMap(readMap);
}