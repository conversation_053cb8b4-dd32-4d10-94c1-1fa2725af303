#include "TcpTask.h"
#include "DatabaseAdapter.h"
#include "Logger.h"
#include "UdpClientDataHandler.h"
#include <algorithm>
#include <chrono>
#include <ctime>
#include <ifaddrs.h>
#include <iostream>
#include <netdb.h>
#include <sys/select.h>
#include <unistd.h>
#include <iomanip>

TcpTask::TcpTask() : m_dataManager(MdbsTcpDataManager::getInstance()) {
    try {
        // // 首先初始化数据库
        // DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
        // if (!dbAdapter.initializeDatabase()) {
        //     throw std::runtime_error("数据库初始化失败");
        // }

        // 获取数据库表
        getDbTable();

        // 初始化 m_idMap
        initializeIdMap();

        // 动态获取net1网口的IP地址
        std::string ip = getIpAddress("net2");
        if (ip.empty()) {
            // 默认IP
            ip = "*************";
        }
        initializeSlave(ip, 502, 1);
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "发生异常: " << e.what();
    }
}

std::string TcpTask::getIpAddress(const std::string &interfaceName) {
    struct ifaddrs *ifaddr, *ifa;
    int family;
    char host[NI_MAXHOST];

    // 获取系统中所有网络接口信息
    if (getifaddrs(&ifaddr) == -1) {
        return std::string(); // 返回空字符串
    }

    // 遍历每个网络接口
    for (ifa = ifaddr; ifa != nullptr; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == nullptr) {
            continue;
        }

        family = ifa->ifa_addr->sa_family;

        // 只处理 IPv4 地址
        if (family == AF_INET && interfaceName == ifa->ifa_name) {
            // 将 IP 地址转换为字符串
            if (getnameinfo(ifa->ifa_addr, sizeof(struct sockaddr_in), host, NI_MAXHOST, nullptr, 0, NI_NUMERICHOST) == 0) {
                freeifaddrs(ifaddr); // 释放内存
                return std::string(host);
            }
        }
    }

    freeifaddrs(ifaddr);  // 释放内存
    return std::string(); // 返回空字符串
}

void TcpTask::dbMonthlyCleanupTask() {
    auto now = std::chrono::system_clock::now();
    std::time_t now_c = std::chrono::system_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&now_c);
    try {
        // 检查是否是每月的第一天 （注：1号当天设备重启都会进行一次清除处理 m_isMonthlyCleanup 没有持久化保存）
        if (now_tm.tm_mday == 1) {
            if (!m_isMonthlyCleanup) {
                // 执行清理操作
                DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                dbAdapter.removeExpiredDeviceTables(12); // 保留最近12个月的表

                m_isMonthlyCleanup = true; // 当月已做清除处理
                return;
            }
        } else {
            if (m_isMonthlyCleanup) {
                m_isMonthlyCleanup = false; // 复位处理过期数据标志位
            }
        }

        // 休眠 5 秒钟
        std::this_thread::sleep_for(std::chrono::seconds(5));
    } catch (const std::exception &e) {
        LOG_ERROR << "发生异常: " << e.what();
    }
}

void TcpTask::modbusTxTasks() {
    try {
        // 处理待写入的操作
        handleWritesTask();

        /* 下面的业务与 handleWritesTask (线程) 业务并发执行*/

        // 获取并解析所有处理器的数据
        processAllDataHandlers();

        // 更新EMS汇总信息
        m_dataManager.updateEmsInfo();
    } catch (const std::exception &e) {
        LOG_ERROR << "发生异常: " << e.what();
    }
}

void TcpTask::handleWritesTask() {
    auto &udpHandler = UdpClientDataHandler::getInstance();
    auto energyMap = udpHandler.getEnergyDataMap(); // 已经在内部加锁，安全获取

    if (energyMap.empty()) {
        return;
    }

    for (auto &t : m_modbusTxThreads) {
        if (t.joinable()) {
            // 如果存在未完成的线程，等待所有线程完成
            t.join();
        }
    }
    // 清理之前的线程
    m_modbusTxThreads.clear();

    // 为每个客户端处理创建一个独立线程
    for (const auto &it : energyMap) {
        const std::string &ip = it.second.ip;
        const uint32_t dbAddr = it.first;

        m_modbusTxThreads.emplace_back(&TcpTask::handlePendingWrites, this, ip, dbAddr);
    }
};

void TcpTask::handlePendingWrites(const std::string &ip, uint32_t dbAddr) {
    auto &udpHandler = UdpClientDataHandler::getInstance();

    bool isTemp;
    // 检查与处理设备是否新加入的情况 && 获取指定 dbAddr 的数据管理器。
    if (!udpHandler.isClientOffline(dbAddr)) {
        // 设备在线
        std::shared_ptr<MdbsTcpDataHandler> modbDataHandler = getDataHandler(dbAddr, isTemp);

        if (modbDataHandler == nullptr) {
            return;
        }

        // 查看是否需要写数据
        if (!isTemp) {
            auto pendingWrite = modbDataHandler->getPendingWrite();
            if (pendingWrite) {
                std::shared_ptr<ModbusTcpClient> client = udpHandler.getModbusClient(ip);
                if (!handleWriteOperation(client, *pendingWrite)) {
                    LOG_ERROR << "Write operation failed.";
                }
            }
        }
    }
}

void TcpTask::processAllDataHandlers() {
    const auto &modbDatahandlers = m_dataManager.getHandlersMap(FORMAL);

    for (const auto &map : modbDatahandlers) {
        auto dbAddr = map.first;
        auto &dataHandler = map.second;

        // 进行数据解析 以及 汇总信息计算
        processDataHandler(dataHandler, dbAddr);
    }
}

void TcpTask::processDataHandler(const std::shared_ptr<MdbsTcpDataHandler> &dataHandler, uint32_t dbAddr) {
    auto &udpHandler = UdpClientDataHandler::getInstance();

    auto dataBatch = dataHandler->getCacheDataBatch(20); // 每次处理20条数据
    for (auto &data : dataBatch) {
        dataHandler->parseData(data);
    }

    auto it = m_dataReadyMap.find(dbAddr);
    if (it != m_dataReadyMap.end()) {
        if (!it->second) {
            // 插入系统 SN 到数据库表
            if (saveSystemSn(dbAddr)) {
                notifyDataReady(dbAddr); // 设置首次读取数据完成标志
            }
        }
    }
}

void TcpTask::notifyDataReady(uint32_t dbAddr) {
    // 通知特定 dbAddr 的数据已准备好
    std::unique_lock<std::mutex> lock(m_dataReadyMutex);
    m_dataReadyMap[dbAddr] = true;
    // m_dataReadyCondVar.notify_all();
}

void TcpTask::modbusRxTasks() {
    try {
        auto &udpHandler = UdpClientDataHandler::getInstance();
        auto energyMap = udpHandler.getEnergyDataMap(); // 已经在内部加锁，安全获取

        if (energyMap.empty()) {
            return;
        }

        for (auto &t : m_modbusRxThreads) {
            if (t.joinable()) {
                // 如果存在未完成的线程，等待所有线程完成
                t.join();
            }
        }
        // 清理之前的线程
        m_modbusRxThreads.clear();

        // 为每个客户端处理创建一个独立线程
        for (const auto &it : energyMap) {
            const std::string &ip = it.second.ip;
            const uint32_t dbAddr = it.first;
            m_modbusRxThreads.emplace_back(&TcpTask::handleClient, this, ip, dbAddr);
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "发生异常: " << e.what();
    }
}

void TcpTask::handleClient(const std::string &ip, uint32_t dbAddr) {
    auto &udpHandler = UdpClientDataHandler::getInstance();
    notifyReadState(dbAddr, false); // 正在操作的客户端

    if (udpHandler.isClientOffline(dbAddr) || udpHandler.isTempClientOffline(dbAddr)) {
        notifyReadState(dbAddr, true); // 操作完成的客户端
        return;
    }

    bool isTemp = true;
    // 检查与处理设备是否新加入的情况 && 获取指定 dbAddr 的数据管理器。
    std::shared_ptr<MdbsTcpDataHandler> modbDataHandler = getDataHandler(dbAddr, isTemp);
    if (modbDataHandler == nullptr) {
        notifyReadState(dbAddr, true); // 操作完成的客户端
        return;
    }

    std::shared_ptr<ModbusTcpClient> client = udpHandler.getModbusClient(ip);

    if (client && client->isConnected()) {
        // 进行读操作
        processClient(client, modbDataHandler, isTemp);

    } else {
        // 掉线/无连接 处理
        handleOfflineClient(ip, dbAddr);
    }

    notifyReadState(dbAddr, true); // 操作完成的客户端
}

void TcpTask::processClient(std::shared_ptr<ModbusTcpClient> client, std::shared_ptr<MdbsTcpDataHandler> modbDataHandler, bool isTemp) {
    // 读取数据
    readModbusData(client, modbDataHandler, "输入寄存器", isTemp);
    readModbusData(client, modbDataHandler, "保持寄存器", isTemp);
}

void TcpTask::checkUserInput() {
    try {
        // 使用 select 异步操作，非阻塞，锁不会占用太久
        std::lock_guard<std::mutex> clientLock(m_pendingDevicesMutex);
        // 如果没有待处理设备，直接返回
        if (m_pendingDevices.empty()) {
            return;
        }

        if (!m_hasPromptedForInput) {
            promptUserForAction();
        }

        fd_set readfds;
        struct timeval tv;
        FD_ZERO(&readfds);
        FD_SET(STDIN_FILENO, &readfds); // 监视标准输入
        tv.tv_sec = 0;
        tv.tv_usec = 0;

        int retval = select(STDIN_FILENO + 1, &readfds, NULL, NULL, &tv);

        if (retval == -1) {
            LOG_ERROR << "select() error";
        } else if (retval) {
            handleUserInput();
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "发生异常: " << e.what();
    }
}

void TcpTask::promptUserForAction() {
    m_dbAddrCurrent = *m_pendingDevices.begin(); // 获取待处理设备中的第一个临时设备

    // 提示用户
    LOG_INFO << "当前处理的临时设备 dbAddr : " << m_dbAddrCurrent << "\n选择操作：\n1. 全新加入\n2. 设备替换\n选择: ";
    m_hasPromptedForInput = true; // 设置提醒标志位，确保只提醒一次
}

void TcpTask::handleUserInput() {
    std::string input;
    std::getline(std::cin, input);
    try {
        if (m_awaitingReplacementInput) {
            handleReplacementInput(input);
        } else {
            int userChoice = std::stoi(input);
            processPendingDevices(userChoice, m_dbAddrCurrent);
        }
    } catch (const std::invalid_argument &) {
        LOG_ERROR << "Invalid input. Please enter a valid number.";
    } catch (const std::out_of_range &) {
        LOG_ERROR << "Input out of range. Please enter a valid number.";
    }
}

void TcpTask::handleReplacementInput(const std::string &input) {
    int deviceId = std::stoi(input); // 输入的设备编号
    uint32_t oldDbAddr = getDbAddrFromId(deviceId);
    if (oldDbAddr != -1) {
        try {
            replaceDeviceData(oldDbAddr, deviceId);
            m_pendingDevices.erase(m_dbAddrCurrent); // 移除已处理的设备
            m_hasPromptedForInput = false;           // 重置提醒标志位
            m_dbAddrCurrent = 0;                     // 初始化当前正在操作的 dbAddr
        } catch (const std::runtime_error &e) {
            LOG_ERROR << "Caught exception: " << e.what();
        }
    } else {
        LOG_ERROR << "无效的设备编号。";
    }
    m_awaitingReplacementInput = false;
}

void TcpTask::replaceDeviceData(uint32_t oldDbAddr, int deviceId) {
    // 清除被选择设备的离线记录
    auto &udpHandler = UdpClientDataHandler::getInstance();
    {
        std::lock_guard<std::mutex> offlineLock(udpHandler.m_offlineDevicesMutex);
        udpHandler.m_offlineDevices.erase(oldDbAddr);

        DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
        dbAdapter.getAccess().remove(m_offlineDeviceTableName, "db_addr = " + std::to_string(oldDbAddr));
    }

    {
        std::unique_lock<std::mutex> lock(m_dataReadyMutex);
        std::string currentSn = "null";
        m_dataReadyMap[m_dbAddrCurrent] = false;

        // 更新设备信息到数据库表
        DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
        dbAdapter.updateDeviceInfo(deviceId, m_dbAddrCurrent, currentSn);
    }

    {
        std::unique_lock<std::mutex> lock(m_readCompleteMutex);
        m_readCompleteCondVar.wait(lock, [this] {
            return m_readCompleteMap[m_dbAddrCurrent];
        });

        // 注：设备信息插入到 DeviceInfo 数据库后才能更新 idMap
        m_idMap[m_dbAddrCurrent] = deviceId;
        m_idMap.erase(oldDbAddr);

        // 移除被替换的离线设备
        m_dataManager.removeHandler(oldDbAddr, FORMAL);
        // 从临时管理表转到正式表中管理
        m_dataManager.replaceTemp2Formal(m_dbAddrCurrent);

        // 重命名设备的数据表
        DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
        // 将临时设备的数据插入到离线的正式设备表中
        dbAdapter.insertTempData2Formal(m_dbAddrCurrent, oldDbAddr);

        // 重命名离线的正式设备表名为新设备的 dbAddr
        dbAdapter.renameDeviceTables(oldDbAddr, m_dbAddrCurrent, FORMAL);

        // 删除临时设备的表
        dbAdapter.removeDeviceTable(m_dbAddrCurrent, TEMP);
    }

    // 同步设备系统时间
    writeSystemTime(m_dbAddrCurrent);
}

void TcpTask::processPendingDevices(int userChoice, uint32_t dbAddr) {
    auto &udpHandler = UdpClientDataHandler::getInstance();

    if (userChoice == 1 || userChoice == 2) {
        if (userChoice == 1) {
            addNewDevice(dbAddr);
        } else if (userChoice == 2) {
            promptUserForReplacement();
        }
    } else {
        LOG_ERROR << "Invalid choice. Please enter 1 or 2.";
    }
}

void TcpTask::addNewDevice(uint32_t dbAddr) {
    try {
        int id = m_currentId++;
        LOG_WARNING << "设备准备全新加入，dbAddr = " << dbAddr;

        {
            std::unique_lock<std::mutex> lock(m_dataReadyMutex);
            std::string currentSn = "null";
            m_dataReadyMap[dbAddr] = false;

            // 插入设备信息到数据库表
            DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
            dbAdapter.creatDeviceInfo(id, dbAddr, currentSn);
        }

        {
            std::unique_lock<std::mutex> lock(m_readCompleteMutex);
            m_readCompleteCondVar.wait(lock, [this] {
                return m_readCompleteMap[m_dbAddrCurrent];
            });

            // 注：设备信息插入到 DeviceInfo 数据库后才能更新 idMap
            m_idMap[m_dbAddrCurrent] = id;

            // 从临时管理器中移除，添加到正式管理器中
            m_dataManager.replaceTemp2Formal(dbAddr);

            // 重命名临时设备表名为正式设备表名
            DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
            dbAdapter.Temp2FormalTable(dbAddr);
        }

        // 同步设备系统时间
        writeSystemTime(m_dbAddrCurrent);

        m_pendingDevices.erase(dbAddr); // 移除已处理的设备
        m_hasPromptedForInput = false;  // 重置提醒标志位
        m_dbAddrCurrent = 0;            // 初始化当前正在操作的 dbAddr
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Caught exception: " << e.what();
    }
}

void TcpTask::promptUserForReplacement() {
    auto &udpHandler = UdpClientDataHandler::getInstance();

    {
        std::lock_guard<std::mutex> offlineLock(udpHandler.m_offlineDevicesMutex);

        if (udpHandler.m_offlineDevices.empty()) {
            LOG_INFO << "当前没有离线设备，无法进行替换操作。";
            m_hasPromptedForInput = false; // 重置提醒标志位
            return;
        }

        LOG_INFO << "离线设备编号:";
        for (const auto &dbAddr : udpHandler.m_offlineDevices) {
            int id = getIdFromDbAddr(dbAddr);
            if (id != -1) {
                LOG_INFO << "编号: " << id << ", dbAddress: " << dbAddr;
                break;
            }
        }
    }

    LOG_INFO << "请输入要替换的设备的ID: ";
    m_awaitingReplacementInput = true;
}

std::string TcpTask::getCurrentTimestamp() {
    // 获取当前时间(单位 ms)
    auto now = std::chrono::system_clock::now();
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
    return std::to_string(milliseconds.count());
}

void TcpTask::getDbTable() {
    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();

    // 使用DatabaseAdapter获取所有表名
    m_dbPath = "data.db"; // 使用默认数据库路径
    m_deviceInfoTableName = dbAdapter.getDeviceInfoTable();
    m_offlineDeviceTableName = dbAdapter.getOfflineDeviceTable();
}

void TcpTask::initializeIdMap() {
    try {
        DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
        std::string query = "SELECT id, db_addr FROM " + m_deviceInfoTableName + ";";

        auto result = dbAdapter.getAccess().query(query);
        int maxId = 0; // 用于跟踪最大的 id

        for (const auto &row : result) {
            if (row.size() == 2) {
                int id = std::stoi(row[0]);
                uint32_t dbAddr = static_cast<uint32_t>(std::stoul(row[1]));
                // m_idMap[id] = dbAddr;
                m_idMap[dbAddr] = id;
                if (id > maxId) {
                    maxId = id; // 更新最大 id
                }
            }
        }

        m_currentId = maxId + 1; // 将 m_currentId 设置为最大 id 加 1，用于生成唯一编号
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Caught exception while initializing m_idMap: " << e.what();
    }
}

void TcpTask::writeSystemTime(uint32_t dbAddr) {
    auto dataHandler = m_dataManager.getHandler(dbAddr, FORMAL);

    if (dataHandler != nullptr) {
        // 获取当前时间
        std::time_t now = std::time(nullptr);

        // UTC 时间转成中国时区时间 + 8h
        std::time_t localtTimestamp = now + 8 * 3600;

        // 转换为 string
        std::string timestamp = std::to_string(localtTimestamp);

        try {
            // 该函数会根据写入的目标寄存器类型来转换 writeValue 类型
            dataHandler->writeData({"保持寄存器", "系统数据"}, "系统时间", timestamp);
        } catch (const std::invalid_argument &e) {
            LOG_ERROR << "Caught exception: " << e.what();
        }
    } else {
        LOG_ERROR << "无 " << m_dbAddrCurrent << "索引的数据处理器";
    }
}

void TcpTask::handleNewDevice(const uint32_t &dbAddr) {
    // 添加到待处理设备列表
    m_pendingDevices.insert(dbAddr);

    // 将数据管理器加入临时表进行管理
    m_dataManager.addHandler(dbAddr, TEMP);

    // 提示用户
    LOG_WARNING << "有新设备接入 dbAddr = " << dbAddr << ", 待处理";
}

void TcpTask::readData(std::shared_ptr<ModbusTcpClient> client,
                       std::shared_ptr<MdbsTcpDataHandler> modbDataHandler,
                       const std::string &registerType,
                       uint16_t startAddress,
                       size_t length,
                       bool isTemp,
                       bool isRead,
                       bool isSave) {
    std::vector<uint16_t> buffer(length);
    int ret = -1;
    uint8_t registerTypeCode = -1;

    if (isRead || isSave) {
        if (registerType == "输入寄存器") {
            registerTypeCode = MODBUS_FC_READ_INPUT_REGISTERS;
            ret = client->readInputRegisters(startAddress, length, buffer); // 返回寄存器读取个数
        } else if (registerType == "保持寄存器") {
            registerTypeCode = MODBUS_FC_READ_HOLDING_REGISTERS;
            ret = client->readHoldingRegisters(startAddress, length, buffer); // 返回寄存器读取个数
        } else {
            LOG_ERROR << "未知的寄存器类型: " << registerType;
            return;
        }
    }

    if (ret > 0) {
        // 注意: 保存和读取数据的顺序不能改变，如果先读取数据再保存，buffer 会被清空 std::move(buffer)
        if (isSave) {
            // if (modbDataHandler->getDbAddr() == 1 && registerType == "输入寄存器") {
            //     // 检查是否包含指定的寄存器地址
            //     const uint16_t targetAddresses[] = {0x0020};

            //     for (const auto &targetAddr : targetAddresses) {
            //         if (startAddress <= targetAddr && targetAddr < startAddress + length) {
            //             // 计算目标地址在buffer中的索引位置
            //             size_t index = targetAddr - startAddress;

            //             LOG_INFO << "=================== 读取到寄存器数据 ===================";
            //             LOG_INFO << "返回寄存器个数: " << ret;
            //             LOG_INFO << "寄存器类型: " << registerType;
            //             LOG_INFO << "寄存器起始地址: 0x" << std::hex << std::setw(4) << std::setfill('0') << targetAddr
            //                      << " (十进制: " << std::dec << targetAddr << ")";

            //             // 打印从目标地址开始的所有数据
            //             std::stringstream dataContent;
            //             dataContent << "数据内容: ";
            //             for (size_t i = index; i < buffer.size(); ++i) {
            //                 dataContent << "0x" << std::hex << std::setw(4) << std::setfill('0') << buffer[i] << " ";
            //             }
            //             LOG_INFO << dataContent.str();

            //             // 打印十进制值
            //             std::stringstream decValues;
            //             decValues << "十进制值: ";
            //             for (size_t i = index; i < buffer.size(); ++i) {
            //                 decValues << buffer[i] << " ";
            //             }
            //             LOG_INFO << decValues.str();

            //             LOG_INFO << "=====================================================";
            //         }
            //     }
            // }

            // 将读取到的数据存入数据库
            LOG_WARNING << "IP: " << client->getIp() << " - 数据开始存入数据库 " << registerType;

            // 使用新的saveDataToDatabase方法保存数据
            saveDataToDatabase(registerTypeCode, startAddress, ret, buffer, modbDataHandler->getDbAddr(), isTemp);
        }

        if (isRead) {
            //     LOG_WARNING << "数据开始存入缓存 " << registerType;

            ModbusData mdbsData;
            mdbsData.timestamp = getCurrentTimestamp();
            mdbsData.registerType = registerType;
            mdbsData.startAddress = startAddress;
            mdbsData.length = ret; // 寄存器个数
            mdbsData.dataBuffer = std::move(buffer);
            mdbsData.dbAddr = modbDataHandler->getDbAddr();

            // 把数据存入缓存当中
            modbDataHandler->addCacheData(mdbsData);
        }
    } else {
        if (client) {
            LOG_ERROR << "从 " << client->getIp() << " 读取" << registerType << "数据失败";
        } else {
            LOG_ERROR << "Modbus客户端连接异常，读取" << registerType << "数据失败";
        }
    }
}

void TcpTask::readModbusData(std::shared_ptr<ModbusTcpClient> client,
                             std::shared_ptr<MdbsTcpDataHandler> modbDataHandler,
                             const std::string &registerType,
                             bool isTemp) {
    if (modbDataHandler == nullptr) {
        return;
    }

    try {
        // 获取寄存器地址块配置
        const auto &configs = modbDataHandler->getRegConfigMap(registerType);

        uint32_t dbAddr = modbDataHandler->getDbAddr();

        for (const auto &config : configs) {
            auto currentTime = std::chrono::steady_clock::now();
            bool isRead = true;
            bool isSave = true; // 第一次上电不检测，直接保存到数据库
            std::string key = registerType + "/" + std::to_string(config.addr); // 地址块路径

            {
                // 加锁以保护 m_lastReadTimesMap 的访问
                std::lock_guard<std::mutex> lock(m_lastReadTimesMutex);
                // 检查读取周期
                if (m_lastReadTimesMap[dbAddr].find(key) != m_lastReadTimesMap[dbAddr].end()) {
                    auto readElapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                                               currentTime - m_lastReadTimesMap[dbAddr][key])
                                               .count();

                    if (readElapsedTime < config.intervalTime || isTemp) {
                        isRead = false;
                    }

                    if (readElapsedTime > config.intervalTime && isTemp) {
                        // 检测心跳
                        client->monitorConnection();
                    }

                } else {
                    // 初始化
                    m_lastReadTimesMap[dbAddr][key] = currentTime;
                    if (isTemp) {
                        isRead = false;
                    }
                }

                if (!isTemp && isRead) {
                    // 更新上次读取时间
                    m_lastReadTimesMap[dbAddr][key] = currentTime;
                }
            }

            {
                // 加锁以保护 m_lastSaveDbTimesMap 的访问
                std::lock_guard<std::mutex> lock(m_lastSaveDbTimesMutex);
                // 检查保存数据库周期， 第一次上电不检测，直接保存到数据库
                if (m_lastSaveDbTimesMap[dbAddr].find(key) != m_lastSaveDbTimesMap[dbAddr].end()) {
                    auto elapsedTime = std::chrono::duration_cast<std::chrono::minutes>(currentTime -
                                                                                        m_lastSaveDbTimesMap[dbAddr][key])
                                           .count();
                    if (elapsedTime < config.saveDbTime) {
                        // 未达到保存数据库时间
                        isSave = false;
                        if (isRead) {
                            goto readBlock;
                        } else {
                            continue; // 读数据到缓冲的时间 和 保存到数据库的时间 均未达到时间间隔，跳过读取
                        }
                    }
                }
                // 更新上次保存数据库时间
                m_lastSaveDbTimesMap[dbAddr][key] = currentTime;
            }
        readBlock:
            readData(client, modbDataHandler, registerType, config.addr, config.length, isTemp, isRead, isSave);
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << e.what();
    }
}

bool TcpTask::handleWriteOperation(std::shared_ptr<ModbusTcpClient> client, const std::vector<std::pair<int, std::any>> &writeBatch) {
    if (writeBatch.empty()) {
        return false;
    }

    // 确保了 writeBatch 是连续的
    bool isBatchWrite = writeBatch.size() > 1;

    if (isBatchWrite) {
        // 准备批量写入的数据
        std::vector<uint16_t> values;
        for (const auto &[address, data] : writeBatch) {
            std::vector<uint16_t> tempRegisters;
            if (!convertToValues(data, tempRegisters)) {
                LOG_ERROR << "Failed to convert data for address " << address << ".";
                return false;
            }
            values.insert(values.end(), tempRegisters.begin(), tempRegisters.end());
        }

        // 使用批量写入
        return client->writeMultipleRegisters(writeBatch.front().first, values);
    } else {
        // 单个写入
        const auto &[address, data] = writeBatch.front();
        std::vector<uint16_t> registers;

        if (!convertToValues(data, registers)) {
            LOG_ERROR << "Failed to convert data for address " << address << ".";
            return false;
        }

        bool success = false;
        if (registers.size() == 1) {
            success = client->writeSingleRegister(address, registers[0]);
        } else {
            success = client->writeMultipleRegisters(address, registers);
        }

        if (!success) {
            LOG_ERROR << "Failed to write register(s) at address " << address << ".";
        }

        return success;
    }
}

bool TcpTask::convertToValues(const std::any &data, std::vector<uint16_t> &registers) {
    // 检查写操作的值类型并处理

    registers.clear();

    // 输出实际类型
    if (data.type() == typeid(uint16_t)) {
        registers.push_back(std::any_cast<uint16_t>(data));
    } else if (data.type() == typeid(int16_t)) {
        registers.push_back(static_cast<uint16_t>(std::any_cast<int16_t>(data)));
    } else if (data.type() == typeid(uint32_t)) {
        uint32_t temp = std::any_cast<uint32_t>(data);
        registers.push_back(static_cast<uint16_t>(temp >> 16));
        registers.push_back(static_cast<uint16_t>(temp & 0xFFFF));
    } else if (data.type() == typeid(int32_t)) {
        int32_t temp = std::any_cast<int32_t>(data);
        uint32_t utemp = static_cast<uint32_t>(temp);
        registers.push_back(static_cast<uint16_t>(utemp >> 16));
        registers.push_back(static_cast<uint16_t>(utemp & 0xFFFF));
    } else if (data.type() == typeid(std::string)) {
        std::string value = std::any_cast<std::string>(data);
        for (size_t i = 0; i < value.size(); i += 2) {
            uint16_t combined = 0;
            combined |= static_cast<uint16_t>(value[i]);
            if (i + 1 < value.size()) {
                combined |= static_cast<uint16_t>(value[i + 1]) << 8;
            }
            registers.push_back(combined);
        }
    } else if (data.type() == typeid(std::vector<uint16_t>)) {
        registers = std::any_cast<std::vector<uint16_t>>(data);
    } else if (data.type() == typeid(std::bitset<16>)) {
        std::bitset<16> bits = std::any_cast<std::bitset<16>>(data);
        registers.push_back(static_cast<uint16_t>(bits.to_ulong()));
    } else if (data.type() == typeid(float)) {
        float f = std::any_cast<float>(data);
        uint32_t temp = *reinterpret_cast<uint32_t *>(&f);

        // 使用 reinterpret_cast 将 float 的位模式拆分为两个 uint16_t
        registers.push_back(static_cast<uint16_t>(temp >> 16));
        registers.push_back(static_cast<uint16_t>(temp & 0xFFFF));
    } else {
        LOG_ERROR << "Unsupported data type.";
        return false;
    }

    return true;
}

std::shared_ptr<MdbsTcpDataHandler> TcpTask::getDataHandler(uint32_t dbAddr, bool &isTemp) {
    if (getIdFromDbAddr(dbAddr) == -1) { // 考虑突然掉电情况
        std::lock_guard<std::mutex> clientLock(m_pendingDevicesMutex);

        if (!m_dataManager.getHandler(dbAddr, TEMP) && isTemp && m_pendingDevices.find(dbAddr) == m_pendingDevices.end()) {
            LOG_WARNING << "设备准备存入待处理列表  dbAddr = " << dbAddr;
            handleNewDevice(dbAddr);
            isTemp = true;
        }
    } else {
        // 掉电重连情况
        if (!m_dataManager.getHandler(dbAddr, FORMAL)) {
            m_dataManager.addHandler(dbAddr, FORMAL);
        }
        isTemp = false;
    }

    return m_dataManager.getHandler(dbAddr, isTemp);
}

void TcpTask::notifyReadState(uint32_t dbAddr, bool state) {
    {
        std::unique_lock<std::mutex> lock(m_readCompleteMutex);
        m_readCompleteMap[dbAddr] = state; //  true: 操作完成的客户端
    }
    m_readCompleteCondVar.notify_all();
}

void TcpTask::handleOfflineClient(const std::string &ip, uint32_t dbAddr) {
    LOG_ERROR << "Modbus-TCP连接到 " << ip << " 已断开";

    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
    auto &udpHandler = UdpClientDataHandler::getInstance();

    if (dbAddr == m_dbAddrCurrent) {
        std::lock_guard<std::mutex> clientLock(m_pendingDevicesMutex);
        m_dataManager.removeHandler(dbAddr, TEMP);      // 移除临时处理器
        m_pendingDevices.erase(dbAddr);                 // 移除未处理且离线的设备 (如果存在)
        udpHandler.m_offlineTempDevices.insert(dbAddr); // 加入到临时离线设备表中
        m_hasPromptedForInput = false;                  // 重置提醒标志位

        dbAdapter.removeDeviceTable(dbAddr, TEMP); // 删除临时数据库表

        return; // 临时设备不加入到正式离线记录表中
    }

    {
        std::lock_guard<std::mutex> offlineLock(udpHandler.m_offlineDevicesMutex);

        if (udpHandler.m_offlineDevices.find(dbAddr) == udpHandler.m_offlineDevices.end()) {
            udpHandler.m_offlineDevices.insert(dbAddr); // 记录离线客户端

            dbAdapter.getAccess().insert(m_offlineDeviceTableName, {{"db_addr", std::to_string(dbAddr)}});
        }
    }
}

void TcpTask::initializeSlave(const std::string &ip, int port, int slaveId) {
    m_slave = std::make_unique<qModbusTcpSlave>(ip, port, slaveId);
}

void TcpTask::TcpSlaveTask() {
    try {
        m_slave->acceptConnect();

        for (auto it = m_slave->m_clientContexts.begin(); it != m_slave->m_clientContexts.end(); it++) {
            int client_socket = it->first;
            modbus_t *client_ctx = it->second;

            if (!m_slave->m_clientResetFlag) {
                // 发送网络异常，需退出循环重新更新 m_clientContexts
                m_slave->m_clientResetFlag = true;
                break;
            }

            receiveRawData(client_ctx);
        }

    } catch (const std::exception &e) {
        LOG_ERROR << e.what();
    }
}

void TcpTask::receiveRawData(modbus_t *client_ctx) {
    uint8_t query[MODBUS_TCP_MAX_ADU_LENGTH];
    int rc;

    m_slave->receiveQuery(client_ctx, query, rc);

    if (rc != -1) {
        std::vector<uint8_t> response(MODBUS_TCP_MAX_ADU_LENGTH);

        // 检查功能码
        uint8_t functionCode = m_slave->handleRequest(client_ctx, query, response);
        if (functionCode == MODBUS_EXCEPTION_ILLEGAL_FUNCTION) {
            return; // 非法功能码,已处理
        }

        const uint8_t slaveId = query[6];

        // 是否为汇总信息操作
        if (slaveId == 0) {
            if (functionCode == MODBUS_FC_WRITE_SINGLE_REGISTER || functionCode == MODBUS_FC_WRITE_MULTIPLE_REGISTERS) {
                // 处理汇总信息写入请求
                handleSummaryWriteRequest(client_ctx, query);

            } else if (functionCode == MODBUS_FC_READ_HOLDING_REGISTERS || functionCode == MODBUS_FC_READ_INPUT_REGISTERS) {
                // 处理汇总信息读取请求
                handleSummaryReadRequest(client_ctx, query);

            } else {
                // 返回异常
                m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_FUNCTION);
            }
            return;
        }

        auto &udpHandler = UdpClientDataHandler::getInstance();
        const auto dbAddr = getDbAddrFromId(slaveId); // 注意考虑是否会耗时 时间复杂度并不是 O(1)

        // 查询设备是否离线状态
        {
            std::lock_guard<std::mutex> offlineLock(udpHandler.m_offlineDevicesMutex);
            const auto offlineIt = udpHandler.m_offlineDevices.find(dbAddr);
            if (dbAddr == -1 || offlineIt != udpHandler.m_offlineDevices.end()) {
                m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_SLAVE_OR_SERVER_FAILURE);
                return;
            }
        }

        // 从handler.m_energyDataMap中找到对应的服务端
        auto energyMap = udpHandler.getEnergyDataMap(); // 已经在内部加锁，安全获取
        const auto serverIt = energyMap.find(dbAddr);
        if (serverIt == energyMap.end()) {
            m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_SLAVE_OR_SERVER_FAILURE);
            return;
        }

        // 获取对应客户端
        const std::string &serverIp = serverIt->second.ip;
        std::shared_ptr<ModbusTcpClient> serverClient = udpHandler.getModbusClient(serverIp);
        if (!serverClient || !serverClient->isConnected()) {
            m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_SLAVE_OR_SERVER_FAILURE);
            return;
        }

        // 解析查询命令,获取startAddress和length
        uint16_t startAddress = (query[8] << 8) | query[9];
        uint16_t length = (query[10] << 8) | query[11];

        std::vector<uint16_t> buffer(length);
        int ret = -1;

        if (functionCode == MODBUS_FC_READ_HOLDING_REGISTERS) {
            ret = serverClient->readHoldingRegisters(startAddress, length, buffer);
        } else if (functionCode == MODBUS_FC_READ_INPUT_REGISTERS) {
            ret = serverClient->readInputRegisters(startAddress, length, buffer);
        }

        if (ret < 0) {
            m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_SLAVE_OR_SERVER_FAILURE);
            return;
        }

        std::vector<ModbusDataType> modbusBuffer;

        // 遍历 buffer，并将每个 uint16_t 转换为 ModbusDataType
        for (const auto &value : buffer) {
            modbusBuffer.push_back(static_cast<ModbusDataType>(value)); // 直接存储为 uint16_t
        }

        // 打包响应数据
        std::vector<uint8_t> responseData(MODBUS_TCP_MAX_ADU_LENGTH);
        int responseLength = m_slave->packReadRegistersResponse(query, modbusBuffer, responseData.data());

        // 将响应发送给从机
        m_slave->sendResponse(client_ctx, responseData, responseLength);
    }
}

void TcpTask::handleSummaryReadRequest(modbus_t *client_ctx, uint8_t *query) {
    // 解析查询命令，获取 startAddress 和 length
    uint8_t functionCode = query[7];
    uint16_t startAddress = (query[8] << 8) | query[9];
    uint16_t length = (query[10] << 8) | query[11];

    RegisterType registerType;
    if (functionCode == MODBUS_FC_READ_INPUT_REGISTERS) {
        registerType = RegisterType::InputRegister;
    } else if (functionCode == MODBUS_FC_READ_HOLDING_REGISTERS) {
        registerType = RegisterType::HoldingRegister;
    } else {
        m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_FUNCTION);
        return;
    }

    // 获取汇总信息
    std::vector<ModbusDataType> buffer;
    if (!m_dataManager.getRawSummaryData(startAddress, length, registerType, buffer)) {
        // 返回 Modbus 错误码 (0x02) 非法数据地址
        m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
        return;
    }

    // 打包响应数据
    std::vector<uint8_t> responseData(MODBUS_TCP_MAX_ADU_LENGTH);
    int responseLength = m_slave->packReadRegistersResponse(query, buffer, responseData.data());

    // 将响应发送给从机
    m_slave->sendResponse(client_ctx, responseData, responseLength);
}

void TcpTask::handleSummaryWriteRequest(modbus_t *client_ctx, uint8_t *query) {
    // 解析查询命令，获取 startAddress 和 data
    uint8_t functionCode = query[7];
    uint16_t startAddress = (query[8] << 8) | query[9];
    std::vector<uint16_t> data;

    if (functionCode == MODBUS_FC_WRITE_SINGLE_REGISTER) {
        data.push_back((query[10] << 8) | query[11]);
    } else if (functionCode == MODBUS_FC_WRITE_MULTIPLE_REGISTERS) {
        uint16_t quantity = (query[10] << 8) | query[11];
        for (uint16_t i = 0; i < quantity; ++i) {
            data.push_back((query[13 + i * 2] << 8) | query[14 + i * 2]);
        }
    } else {
        // 返回 Modbus 异常响应
        m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_FUNCTION);
        return;
    }

    uint16_t registersWritten;
    // 更新（写入）汇总信息
    if (!m_dataManager.writeHoidingSummaryData(startAddress, data, registersWritten)) {
        // 返回 Modbus 异常响应
        m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
        return;
    }

    // 检查是否需要分发 totalPower
    if (startAddress <= static_cast<uint16_t>(HoldingSummaryIndex::TOTAL_POWER) &&
        startAddress + data.size() > static_cast<uint16_t>(HoldingSummaryIndex::TOTAL_POWER)) {
        // 获取写入的 totalPower 值
        double totalPowerValue;
        if (m_dataManager.getSummaryValue(RegisterType::HoldingRegister, HoldingSummaryIndex::TOTAL_POWER, totalPowerValue)) {
            // 将 totalPower 平均分配给所有在线的 MdbsTcpDataHandler
            if (!distributeTotalPowerToHandlers(totalPowerValue)) {
                // 没有在线设备 或者 没找到， 错误码 (0x03) 非法数据
                m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
            }
        } else {
            // 返回 Modbus 错误码 (0x02) 非法数据地址
            m_slave->modbusReplyException(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
        }
    }

    // 打包响应数据
    std::vector<uint8_t> responseData(MODBUS_TCP_MAX_ADU_LENGTH);
    int responseLength = m_slave->packWriteRegistersResponse(query, registersWritten, responseData.data());

    // 将响应发送给主站
    m_slave->sendResponse(client_ctx, responseData, responseLength);
}

bool TcpTask::distributeTotalPowerToHandlers(double totalPowerValue) {
    auto &clientHandler = UdpClientDataHandler::getInstance();

    // 获取在线的 MdbsTcpDataHandler 数量
    std::vector<std::shared_ptr<MdbsTcpDataHandler>> onlineHandlers;

    const auto &handlersMap = m_dataManager.getHandlersMap(FORMAL);
    for (const auto &handlerPair : handlersMap) {
        uint32_t dbAddr = handlerPair.first;
        if (!clientHandler.isClientOffline(dbAddr)) {
            onlineHandlers.push_back(handlerPair.second);
        }
    }

    size_t onlineCount = onlineHandlers.size();
    if (onlineCount == 0) {
        LOG_ERROR << "没有在线的 ModbusDataHandler，无法分配 totalPower。";
        return false;
    }

    // 计算平均值 注：超过单台最高输出功率不做限制，照常下发
    double averagePower = totalPowerValue / onlineCount;

    // 将平均值写入每个在线的 MdbsTcpDataHandler 的 "保持寄存器" "系统数据" "有功功率"
    for (auto udpHandler : onlineHandlers) {
        DataAttributes dataAttrs;
        if (udpHandler->getRegisterAttributes({"保持寄存器", "系统数据"}, "有功功率", dataAttrs)) {
            // 将物理值转换为寄存器值
            double rawValue = averagePower / dataAttrs.conversionFactor + dataAttrs.offset;

            std::string writeValue = std::to_string(rawValue);
            // 该函数会根据写入的目标寄存器类型来转换 writeValue 类型
            udpHandler->writeData({"保持寄存器", "系统数据"}, "有功功率", writeValue);
        } else {
            LOG_ERROR << "未找到对应的寄存器信息";
            return false;
        }
    }
    return true;
}

uint32_t TcpTask::getDbAddrFromId(int deviceId) const {
    auto idMapIt = std::find_if(m_idMap.begin(), m_idMap.end(), [deviceId](const auto &item) {
        return item.second == deviceId;
    });

    if (idMapIt != m_idMap.end()) {
        return idMapIt->first;
    } else {
        return -1;
    }
}

int TcpTask::getIdFromDbAddr(uint32_t dbAddr) const {
    auto idMapIt = m_idMap.find(dbAddr);
    if (idMapIt != m_idMap.end()) {
        return idMapIt->second;
    } else {
        return -1;
    }
}

bool TcpTask::saveSystemSn(uint32_t dbAddr) {
    int id = getIdFromDbAddr(dbAddr);
    if (id != -1) {
        auto dataHandler = m_dataManager.getHandler(dbAddr, FORMAL);
        if (dataHandler != nullptr) {
            std::string currentSn = dataHandler->getDataByCategory({"保持寄存器", "系统数据"}, "系统序列号");

            if (currentSn.empty()) {
                currentSn = "null";
            }

            // 插入设备信息到数据库表
            DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
            dbAdapter.insertSysSn2DevInfo(id, currentSn);
            return true;
        }
    }
    return false;
}

void TcpTask::saveDataToDatabase(const uint8_t registerTypeCode,
                                 uint16_t startAddress,
                                 uint16_t length,
                                 const std::vector<uint16_t> &data,
                                 const uint32_t dbAddr,
                                 bool isTemp) {
    // 准备数据
    std::vector<std::pair<std::string, int64_t>> values = {{"timestamp", static_cast<int64_t>(std::stoll(getCurrentTimestamp()))},
                                                           {"db_addr", static_cast<int64_t>(dbAddr)},
                                                           {"register_type", static_cast<int64_t>(registerTypeCode)},
                                                           {"start_address", static_cast<int64_t>(startAddress)},
                                                           {"length", static_cast<int64_t>(length)}};

    // 将uint16_t数据转换为uint8_t数据
    std::vector<uint8_t> byteData;
    byteData.reserve(data.size() * sizeof(uint16_t)); // 预分配空间提高性能

    for (const auto &value : data) {
        // 每个uint16_t拆分为两个uint8_t（高字节和低字节）
        byteData.push_back(static_cast<uint8_t>(value & 0xFF));        // 低字节
        byteData.push_back(static_cast<uint8_t>((value >> 8) & 0xFF)); // 高字节
    }

    // 插入数据库
    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
    dbAdapter.insertDeviceDataWithCompression(values, byteData, isTemp);
}