#ifndef QMODBUSTCPSLAVE_H
#define QMODBUSTCPSLAVE_H

#include "MdbsTcpDataManager.h"
#include <arpa/inet.h>
#include <atomic>
#include <chrono>
#include <iostream>
#include <modbus/modbus.h>
#include <mutex>
#include <stdexcept>
#include <unordered_map>
#include <unordered_set>
#include <vector>

class qModbusTcpSlave {
  public:
    qModbusTcpSlave(const std::string &ip_address, int port, int slave_id);
    ~qModbusTcpSlave();

    void acceptConnect();

    uint8_t receiveQuery(modbus_t *client_ctx, uint8_t *query, int &rc);

    void sendResponse(modbus_t *client_ctx, std::vector<uint8_t> &response, int responseLength);

    int packReadRegistersResponse(const uint8_t *request, const std::vector<ModbusDataType> &data, uint8_t *response);

    int packWriteRegistersResponse(const uint8_t *request, uint16_t registersWritten, uint8_t *response);

    void modbusReplyException(modbus_t *client_ctx, const uint8_t *req, uint8_t exception_code);

    int handleRequest(modbus_t *client_ctx, const uint8_t *query, std::vector<uint8_t> &response);

    std::unordered_map<int, modbus_t *> m_clientContexts;

    std::atomic<bool> m_clientResetFlag = true;

  private:
    void reconnect();

    void removeClientContext(modbus_t *client_ctx);

    void cleanAllClient();

    modbus_t *m_ctx;
    int m_slaveId;
    std::string m_ipAddress;
    int m_port;
    std::unordered_map<std::string, std::unordered_map<int, uint16_t>> m_dataMap;

    bool m_connected;
    int m_serverSocket = -1;
    const int MODBUS_TCP_HEADER_LENGTH = 7;
    std::recursive_mutex m_connectionMutex;
    const int m_listenCount = 5;
};

#endif // QMODBUSTCPSLAVE_H