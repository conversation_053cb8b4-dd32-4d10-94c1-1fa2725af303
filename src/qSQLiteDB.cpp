#include "qSQLiteDB.h"
#include "CompressionUtils.h"
#include "ConfigManager.h"
#include "Logger.h"
#include <algorithm>
#include <regex>

qSQLiteDB &qSQLiteDB::getInstance(const std::string &dbPath) {
    static qSQLiteDB instance(dbPath);
    return instance;
}

qSQLiteDB::qSQLiteDB(const std::string &dbPath) : iSQLiteDB(dbPath) {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    m_deviceInfoTableName = config["deviceInfoTableName"].as<std::string>("device_info");
    m_offlineDeviceTableName = config["offlineDeviceTableName"].as<std::string>("offline_device");

    executeWithLock("CREATE TABLE IF NOT EXISTS " + m_deviceInfoTableName +
                    "("
                    "id INTEGER PRIMARY KEY, "
                    "db_addr INTEGER, "
                    "current_sn TEXT, "
                    "last_sn TEXT);");

    executeWithLock("CREATE TABLE IF NOT EXISTS " + m_offlineDeviceTableName +
                    "("
                    "db_addr INTEGER PRIMARY KEY );");
}

std::string qSQLiteDB::getDeviceTableName(uint32_t dbAddr, const std::string &monthStr, bool isTemp) {
    std::string prefix = isTemp ? "temp_device_" : "device_";
    return prefix + std::to_string(dbAddr) + "_" + monthStr;
}

// 创建设备数据表，按月份创建
void qSQLiteDB::createDeviceTable(uint32_t dbAddr, const std::string &monthStr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);
    std::string tableName = getDeviceTableName(dbAddr, monthStr, isTemp);

    createDeviceTableIfNotExists(tableName);
}

void qSQLiteDB::removeDeviceTable(uint32_t dbAddr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取所有与该设备相关的表名
    std::string prefix = isTemp ? "temp_device_" : "device_";
    prefix += std::to_string(dbAddr) + "_%";
    std::string sql = "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '" + prefix + "';";

    auto result = query(sql);

    for (const auto &row : result) {
        if (!row.empty()) {
            std::string tableName = row[0];
            std::string dropSql = "DROP TABLE IF EXISTS " + tableName + ";";
            execute(dropSql);
        }
    }
}

void qSQLiteDB::insertTempData2Formal(uint32_t tempDbAddr, uint32_t formalDbAddr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取临时设备的所有数据表
    std::string tempTablePrefix = getDeviceTablePrefix(tempDbAddr, true);
    std::string sql = "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '" + tempTablePrefix + "%';";
    auto result = query(sql);

    for (const auto &row : result) {
        if (!row.empty()) {
            std::string tempTableName = row[0];

            // 构建对应的正式表名
            std::string formalTableName = tempTableName;
            // 替换表名前缀
            size_t prefixPos = formalTableName.find("temp_device_");
            if (prefixPos != std::string::npos) {
                formalTableName.replace(prefixPos, std::string("temp_device_").length(), "device_");
            } else {
                continue; // 未找到匹配的表名，跳过
            }

            // 替换设备地址
            size_t addrPos = formalTableName.find(std::to_string(tempDbAddr), prefixPos);
            if (addrPos != std::string::npos) {
                formalTableName.replace(addrPos, std::to_string(tempDbAddr).length(), std::to_string(formalDbAddr));
            } else {
                continue; // 未找到匹配的设备地址，跳过
            }

            // 确保正式表已创建
            createDeviceTableIfNotExists(formalTableName);

            // 将临时表的数据插入到正式表
            std::string insertSql = "INSERT OR IGNORE INTO " + formalTableName + " SELECT * FROM " + tempTableName + ";";
            execute(insertSql);

            LOG_INFO << "将临时表数据插入到正式表：" << tempTableName << " -> " << formalTableName;
        }
    }
}

void qSQLiteDB::renameDeviceTables(uint32_t oldDbAddr, uint32_t newDbAddr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 构建表名前缀
    std::string oldTablePrefix = getDeviceTablePrefix(oldDbAddr, isTemp);
    std::string newTablePrefix = getDeviceTablePrefix(newDbAddr, isTemp);

    // 查询所有匹配的表名
    std::string sql = "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '" + oldTablePrefix + "%';";
    auto result = query(sql);

    for (const auto &row : result) {
        if (!row.empty()) {
            std::string oldTableName = row[0];
            // 构建新的表名
            std::string newTableName = oldTableName;
            // 将 oldDbAddr 替换为 newDbAddr
            size_t pos = newTableName.find(std::to_string(oldDbAddr));
            if (pos != std::string::npos) {
                newTableName.replace(pos, std::to_string(oldDbAddr).length(), std::to_string(newDbAddr));
            } else {
                continue; // 未找到匹配的表名，跳过
            }

            // 检查新表名是否已存在
            std::string checkQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + newTableName + "';";
            auto checkResult = query(checkQuery);
            if (!checkResult.empty()) {
                // 如果新表名已存在，可以选择跳过或抛出异常
                LOG_ERROR << "表名 " << newTableName << " 已存在，无法重命名。";
                continue;
            }

            // 执行重命名操作
            std::string renameSql = "ALTER TABLE " + oldTableName + " RENAME TO " + newTableName + ";";
            execute(renameSql);

            LOG_INFO << "重命名表：" << oldTableName << " 为 " << newTableName;
        }
    }
}

void qSQLiteDB::Temp2FormalTable(uint32_t dbAddr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取设备的临时表前缀
    std::string tempTablePrefix = getDeviceTablePrefix(dbAddr, true); // true 表示临时表

    // 查询所有匹配的临时表名
    std::string sql = "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '" + tempTablePrefix + "%';";
    auto result = query(sql);

    for (const auto &row : result) {
        if (!row.empty()) {
            std::string tempTableName = row[0];

            // 构建对应的正式表名
            std::string formalTableName = tempTableName;
            // 替换表名前缀
            size_t prefixPos = formalTableName.find("temp_device_");
            if (prefixPos != std::string::npos) {
                formalTableName.replace(prefixPos, std::string("temp_device_").length(), "device_");

                // 检查正式表是否已存在
                std::string checkQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + formalTableName + "';";
                auto checkResult = query(checkQuery);
                if (!checkResult.empty()) {
                    // 如果正式表已存在，可以选择跳过或删除旧表
                    LOG_ERROR << "正式表 " << formalTableName << " 已存在，跳过重命名。";
                    continue;
                }

                // 执行重命名操作
                std::string renameSql = "ALTER TABLE " + tempTableName + " RENAME TO " + formalTableName + ";";
                execute(renameSql);

                LOG_INFO << "重命名临时表：" << tempTableName << " 为正式表：" << formalTableName;
            } else {
                LOG_ERROR << "无法识别的临时表名：" << tempTableName << "，跳过。";
                continue; // 未找到匹配的临时表前缀，跳过
            }
        }
    }
}
void qSQLiteDB::insertDeviceDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                                const std::vector<uint8_t> &dataBytes,
                                                bool isTemp) {
    // 从传入的 values 中找出 db_addr
    uint32_t dbAddr = 0;
    bool foundDbAddr = false;
    for (const auto &pair : values) {
        if (pair.first == "db_addr") {
            dbAddr = static_cast<uint32_t>(pair.second);
            foundDbAddr = true;
            break;
        }
    }

    if (!foundDbAddr) {
        // 未在 values 中找到 db_addr，返回，不保存至数据库
        LOG_ERROR << "insertDeviceDataWithCompression: 未在 values 中找到 db_addr!";
        return;
    }

    try {
        //  压缩数据
        CompressionUtils &compressTool = CompressionUtils::getInstance();
        std::vector<uint8_t> compressedData = compressTool.compressData(dataBytes);

        insertDeviceData(dbAddr, values, "data", compressedData, isTemp);
    } catch (const std::exception &e) {
        LOG_ERROR << e.what();
    }
}

void qSQLiteDB::insertDeviceData(uint32_t dbAddr,
                                 const std::vector<std::pair<std::string, int64_t>> &values,
                                 const std::string &blobFieldName,
                                 const std::vector<uint8_t> &blobData,
                                 bool isTemp) {
    // 获取当前年月，格式为"YYYYMM"
    auto now = std::chrono::system_clock::now();
    std::time_t now_c = std::chrono::system_clock::to_time_t(now);
    char monthStr[32]; // YYYYMM, enlarged to avoid truncation warnings
    std::strftime(monthStr, sizeof(monthStr), "%Y%m", std::localtime(&now_c));

    std::string tableName = getDeviceTableName(dbAddr, monthStr, isTemp);
    createDeviceTable(dbAddr, monthStr, isTemp); // 确保表已创建

    insertWithBinaryData(tableName, values, blobFieldName, blobData);
}

void qSQLiteDB::creatDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn) {
    try {
        iSQLiteDB::insert(
            "device_info",
            {{"id", std::to_string(id)}, {"db_addr", std::to_string(dbAddr)}, {"current_sn", currentSn}, {"last_sn", "NULL"}});
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Caught exception in creatDeviceInfo: " << e.what();
    }
}

void qSQLiteDB::updateDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn) {
    try {
        std::string oldCurrentSn;
        std::string query = "SELECT current_sn FROM  " + m_deviceInfoTableName + " WHERE id = " + std::to_string(id) + " LIMIT 1"; // 限制一条

        auto results = iSQLiteDB::query(query);
        if (!results.empty()) {
            oldCurrentSn = results[0][0];
        } else {
            oldCurrentSn = "NULL";
        }

        iSQLiteDB::update(m_deviceInfoTableName,
                          {{"db_addr", std::to_string(dbAddr)}, {"last_sn", oldCurrentSn}, {"current_sn", currentSn}},
                          "id = " + std::to_string(id));
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Caught exception in updateDeviceInfo: " << e.what();
    }
}

void qSQLiteDB::insertSysSn2DevInfo(int id, const std::string &currentSn) {
    try {
        iSQLiteDB::update(m_deviceInfoTableName, {{"current_sn", currentSn}}, "id = " + std::to_string(id));
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Caught exception in updateDeviceInfo: " << e.what();
    }
}

void qSQLiteDB::insertWithBinaryData(const std::string &tableName,
                                     const std::vector<std::pair<std::string, int64_t>> &values,
                                     const std::string &blobFieldName,
                                     const std::vector<uint8_t> &blobData) {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    // 构建 SQL 语句
    std::string sql = "INSERT INTO " + tableName + " (";
    std::string placeholders = "(";
    bool first = true;
    for (const auto &pair : values) {
        if (!first) {
            sql += ", ";
            placeholders += ", ";
        }
        sql += pair.first;
        placeholders += "?";
        first = false;
    }
    // 添加 BLOB 数据的字段和占位符
    sql += ", " + blobFieldName + ") VALUES ";
    placeholders += ", ?)";
    sql += placeholders + ";";

    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr);
    if (rc != SQLITE_OK) {
        throw std::runtime_error("Failed to prepare statement: " + std::string(sqlite3_errmsg(m_db)));
    }

    // 绑定参数
    int index = 1;
    for (const auto &pair : values) {
        sqlite3_bind_int64(stmt, index++, pair.second);
    }
    // 绑定 BLOB 数据
    if (!blobData.empty()) {
        sqlite3_bind_blob(stmt, index, blobData.data(), static_cast<int>(blobData.size()), SQLITE_TRANSIENT);
    } else {
        // 如果 blobData 为空，则绑定 NULL
        sqlite3_bind_null(stmt, index);
    }

    // 执行语句
    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        throw std::runtime_error("Failed to execute statement: " + std::string(sqlite3_errmsg(m_db)));
    }

    sqlite3_finalize(stmt);
}

void qSQLiteDB::renameTable(const std::string &oldTableName, const std::string &newTableName) {
    // 锁定互斥量，确保线程安全
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 检查新表名是否已存在
    std::string checkQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + newTableName + "';";
    auto result = query(checkQuery);

    if (!result.empty()) {
        // 如果新表名已存在，抛出异常
        throw std::runtime_error("表名 " + newTableName + " 已存在，无法重命名。");
    }

    // 执行重命名操作
    std::string sqlRename = "ALTER TABLE " + oldTableName + " RENAME TO " + newTableName + ";";
    execute(sqlRename);
}

void qSQLiteDB::removeExpiredDeviceTables(int keepMonths) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    std::time_t now_c = std::chrono::system_clock::to_time_t(now);

    // 计算保留的最早月份
    struct tm *timeinfo = std::localtime(&now_c);
    int year = timeinfo->tm_year + 1900;
    int month = timeinfo->tm_mon + 1;

    // 回退keepMonths个月
    month -= keepMonths;
    while (month <= 0) {
        month += 12;
        year--;
    }

    // 格式化最早月份字符串
    char earliestMonthStr[32];
    std::sprintf(earliestMonthStr, "%04d%02d", year, month);

    // 获取所有表
    auto tables = query("SELECT name FROM sqlite_master WHERE type='table'");

    // 正则表达式匹配设备表和电表表
    std::regex deviceTableRegex("(device|temp_device)_\\d+_(\\d{6})");
    std::regex meterTableRegex("meter_(\\d{6})");

    // 遍历所有表
    for (const auto &row : tables) {
        if (!row.empty()) {
            std::smatch deviceMatches;
            std::smatch meterMatches;

            // 检查是否为设备表
            if (std::regex_search(row[0], deviceMatches, deviceTableRegex) && deviceMatches.size() > 2) {
                std::string monthStr = deviceMatches[2].str();

                // 如果表的月份早于最早保留月份，则删除
                if (monthStr.compare(earliestMonthStr) < 0) {
                    executeWithLock("DROP TABLE IF EXISTS " + row[0]);
                }
            }
            // 检查是否为电表表
            else if (std::regex_search(row[0], meterMatches, meterTableRegex) && meterMatches.size() > 1) {
                std::string monthStr = meterMatches[1].str();

                // 如果表的月份早于最早保留月份，则删除
                if (monthStr.compare(earliestMonthStr) < 0) {
                    executeWithLock("DROP TABLE IF EXISTS " + row[0]);
                }
            }
        }
    }
}

std::string qSQLiteDB::getDeviceTablePrefix(uint32_t dbAddr, bool isTemp) {
    std::string prefix = isTemp ? "temp_device_" : "device_";
    prefix += std::to_string(dbAddr) + "_";
    return prefix;
}

void qSQLiteDB::createDeviceTableIfNotExists(const std::string &tableName) {
    std::string sql = "CREATE TABLE IF NOT EXISTS " + tableName +
                      "("
                      "timestamp INTEGER, "
                      "db_addr INTEGER, "
                      "register_type INTEGER, "
                      "start_address INTEGER, "
                      "length INTEGER, "
                      "data BLOB, "
                      "PRIMARY KEY (timestamp, db_addr));";
    execute(sql);
}

std::vector<DataRecord> qSQLiteDB::queryDeviceData(uint32_t dbAddr, int64_t startTime, int64_t endTime) {
    std::vector<DataRecord> dataRecords;

    // 获取相关的表名
    std::vector<std::string> tableNames = getDeviceTablesInRange(dbAddr, startTime, endTime);

    // 构建查询语句
    for (const auto &tableName : tableNames) {
        // 检查表是否存在
        std::string checkTableSql = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + tableName + "';";
        auto result = query(checkTableSql);
        if (result.empty()) {
            continue; // 表不存在，跳过
        }

        // 查询数据
        std::string sql = "SELECT timestamp, db_addr, register_type, start_address, length, data FROM " + tableName +
                          " WHERE timestamp >= " + std::to_string(startTime) +
                          " AND timestamp <= " + std::to_string(endTime) + ";";

        // 执行查询，处理结果
        std::vector<DataRecord> records = queryDataRecords(sql);
        dataRecords.insert(dataRecords.end(), records.begin(), records.end());
    }

    // 按时间排序
    std::sort(dataRecords.begin(), dataRecords.end(), [](const DataRecord &a, const DataRecord &b) {
        return a.timestamp < b.timestamp;
    });

    return dataRecords;
}

std::vector<DataRecord> qSQLiteDB::queryDataRecords(const std::string &sql) {
    std::vector<DataRecord> records;

    std::lock_guard<std::mutex> lock(m_db_mutex);

    // 执行查询
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr);
    if (rc != SQLITE_OK) {
        throw std::runtime_error("Failed to prepare statement: " + std::string(sqlite3_errmsg(m_db)));
    }

    CompressionUtils &compressTool = CompressionUtils::getInstance();

    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        DataRecord record;
        record.timestamp = sqlite3_column_int64(stmt, 0);
        record.dbAddr = static_cast<uint32_t>(sqlite3_column_int64(stmt, 1));
        record.registerType = sqlite3_column_int(stmt, 2);
        record.startAddress = sqlite3_column_int(stmt, 3);
        record.length = sqlite3_column_int(stmt, 4);

        // 从数据库读出的压缩数据
        const void *blobData = sqlite3_column_blob(stmt, 5);
        int blobSize = sqlite3_column_bytes(stmt, 5);
        std::vector<uint8_t> compressedData;

        if (blobSize > 0 && blobData != nullptr) {
            compressedData.assign(static_cast<const uint8_t *>(blobData), static_cast<const uint8_t *>(blobData) + blobSize);
        }

        if (!compressedData.empty()) {
            try {
                record.data = compressTool.decompressData(compressedData,        // 压缩后的数据
                                                          compressedData.size(), // 压缩数据的大小
                                                          record.length); // 解压后预期大小（注意这里的含义要与插入时一致）
            } catch (const std::exception &e) {
                LOG_ERROR << "解压失败: " << e.what();
                record.length = 0;
                record.data = {0};
            }
        }

        records.push_back(record);
    }

    if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        throw std::runtime_error("Failed to execute statement: " + std::string(sqlite3_errmsg(m_db)));
    }

    sqlite3_finalize(stmt);
    return records;
}

std::vector<std::string> qSQLiteDB::getDeviceTablesInRange(uint32_t dbAddr, int64_t startTime, int64_t endTime) {
    std::vector<std::string> tableNames;

    // 将时间戳转换为年月字符串
    std::time_t start_time_t = startTime / 1000; // 毫秒转秒
    std::time_t end_time_t = endTime / 1000;

    std::tm start_tm = *std::localtime(&start_time_t);
    std::tm end_tm = *std::localtime(&end_time_t);

    char monthStr[32]; // YYYYMM buffer enlarged to prevent truncation

    for (int year = start_tm.tm_year + 1900; year <= end_tm.tm_year + 1900; ++year) {
        int startMonth = (year == start_tm.tm_year + 1900) ? start_tm.tm_mon + 1 : 1;
        int endMonth = (year == end_tm.tm_year + 1900) ? end_tm.tm_mon + 1 : 12;

        for (int month = startMonth; month <= endMonth; ++month) {
            std::snprintf(monthStr, sizeof(monthStr), "%04d%02d", year, month);
            std::string tableName = getDeviceTableName(dbAddr, monthStr);
            tableNames.push_back(tableName);
        }
    }

    return tableNames;
}

std::string qSQLiteDB::getMeterTableName(const std::string &monthStr) {
    std::string prefix = getMeterTablePrefix();
    return prefix + monthStr;
}

std::string qSQLiteDB::getMeterTablePrefix() {
    return "meter_";
}

void qSQLiteDB::createMeterTableIfNotExists(const std::string &tableName) {
    std::string createTableSql = "CREATE TABLE IF NOT EXISTS " + tableName +
                                 " ("
                                 "timestamp INTEGER, "
                                 "register_type INTEGER, "
                                 "start_address INTEGER, "
                                 "length INTEGER, "
                                 "data BLOB, "
                                 "PRIMARY KEY (timestamp))";
    executeWithLock(createTableSql);
}

void qSQLiteDB::createMeterTable(const std::string &monthStr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);
    std::string tableName = getMeterTableName(monthStr);
    createMeterTableIfNotExists(tableName);
}

void qSQLiteDB::removeMeterTable(const std::string &monthStr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);
    std::string tableName = getMeterTableName(monthStr);
    executeWithLock("DROP TABLE IF EXISTS " + tableName);
}

void qSQLiteDB::insertMeterDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                               const std::vector<uint8_t> &dataBytes) {
    // 获取当前年月，格式为"YYYYMM"
    auto now = std::chrono::system_clock::now();
    std::time_t now_c = std::chrono::system_clock::to_time_t(now);
    char monthStr[32]; // YYYYMM buffer enlarged to prevent truncation
    std::strftime(monthStr, sizeof(monthStr), "%Y%m", std::localtime(&now_c));

    try {
        // 压缩数据
        CompressionUtils &compressTool = CompressionUtils::getInstance();
        std::vector<uint8_t> compressedData = compressTool.compressData(dataBytes);

        // 获取表名
        std::string tableName = getMeterTableName(monthStr);

        // 确保表存在
        createMeterTableIfNotExists(tableName);

        // 插入数据
        insertWithBinaryData(tableName, values, "data", compressedData);
    } catch (const std::exception &e) {
        LOG_ERROR << e.what();
    }
}

std::vector<std::string> qSQLiteDB::getMeterTablesInRange(int64_t startTime, int64_t endTime) {
    std::vector<std::string> tableNames;

    // 将时间戳转换为年月字符串
    std::time_t start_time_t = startTime / 1000; // 毫秒转秒
    std::time_t end_time_t = endTime / 1000;

    std::tm start_tm = *std::localtime(&start_time_t);
    std::tm end_tm = *std::localtime(&end_time_t);

    char monthStr[32]; // YYYYMM buffer enlarged to prevent truncation

    for (int year = start_tm.tm_year + 1900; year <= end_tm.tm_year + 1900; ++year) {
        int startMonth = (year == start_tm.tm_year + 1900) ? start_tm.tm_mon + 1 : 1;
        int endMonth = (year == end_tm.tm_year + 1900) ? end_tm.tm_mon + 1 : 12;

        for (int month = startMonth; month <= endMonth; ++month) {
            std::snprintf(monthStr, sizeof(monthStr), "%04d%02d", year, month);
            std::string tableName = getMeterTableName(monthStr);
            tableNames.push_back(tableName);
        }
    }

    return tableNames;
}

std::vector<DataRecord> qSQLiteDB::queryMeterData(int64_t startTime, int64_t endTime) {
    std::vector<DataRecord> dataRecords;

    // 获取相关的表名
    std::vector<std::string> tableNames = getMeterTablesInRange(startTime, endTime);

    // 构建查询语句
    for (const auto &tableName : tableNames) {
        // 检查表是否存在
        std::string checkTableSql = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + tableName + "';";
        auto result = query(checkTableSql);
        if (result.empty()) {
            continue; // 表不存在，跳过
        }

        // 查询数据
        std::string sql = "SELECT timestamp, 0 as db_addr, register_type, start_address, length, data FROM " +
                          tableName + " WHERE timestamp >= " + std::to_string(startTime) +
                          " AND timestamp <= " + std::to_string(endTime) + ";";

        // 执行查询，处理结果
        std::vector<DataRecord> records = queryDataRecords(sql);
        dataRecords.insert(dataRecords.end(), records.begin(), records.end());
    }

    // 按时间排序
    std::sort(dataRecords.begin(), dataRecords.end(), [](const DataRecord &a, const DataRecord &b) {
        return a.timestamp < b.timestamp;
    });

    return dataRecords;
}
