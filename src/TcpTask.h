#ifndef TCP_TASK_H
#define TCP_TASK_H

#include "ModbusTcpClient.h"
#include "qModbusTcpSlave.h"
#include "qSQLiteDB.h"
#include <atomic>
#include <condition_variable>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <unordered_set>
#include <vector>

class TcpTask {
  public:
    TcpTask();

    std::string getIpAddress(const std::string &interfaceName);

    /**
     * 主任务处理函数，负责每个月检查一次数据库过期数据任务
     */
    void dbMonthlyCleanupTask();

    /**
     * 主任务处理函数，负责处理解析数据以及发送数据任务
     */
    void modbusRxTasks();

    /**
     * 主任务处理函数，负责处理所有Modbus传输任务
     */
    void modbusTxTasks();

    void checkUserInput();

    void TcpSlaveTask();

    inline const std::unordered_map<uint32_t, int> &getIDMap() const {
        return m_idMap;
    };

    /**
     * @brief 保存Modbus数据到数据库
     * @param registerType 寄存器类型
     * @param startAddress 起始地址
     * @param length 长度
     * @param data 数据
     * @param dbAddr 数据库地址
     */
    void saveDataToDatabase(const uint8_t registerTypeCode,
                            uint16_t startAddress,
                            uint16_t length,
                            const std::vector<uint16_t> &data,
                            const uint32_t dbAddr,
                            bool isTemp);

  private:
    // 线程处理函数
    void handleClient(const std::string &ip, uint32_t dbAddr);

    // 原有的私有成员函数
    void readData(std::shared_ptr<ModbusTcpClient> client,
                  std::shared_ptr<MdbsTcpDataHandler> modbDataHandler,
                  const std::string &registerType,
                  uint16_t startAddress,
                  size_t length,
                  bool isTemp,
                  bool isRead,
                  bool isSave);
    void readModbusData(std::shared_ptr<ModbusTcpClient> client,
                        std::shared_ptr<MdbsTcpDataHandler> modbDataHandler,
                        const std::string &registerType,
                        bool isTemp);
    bool handleWriteOperation(std::shared_ptr<ModbusTcpClient> client, const std::vector<std::pair<int, std::any>> &writeBatch);

    bool convertToValues(const std::any &data, std::vector<uint16_t> &registers);

    void handleNewDevice(const uint32_t &dbAddr);

    void processPendingDevices(int userChoice, uint32_t dbAddr);

    void promptUserForReplacement();

    std::string getCurrentTimestamp();

    // 获取数据库表
    void getDbTable();

    // 从数据库读取数据并初始化 m_idMap
    void initializeIdMap();

    void writeSystemTime(uint32_t dbAddr);

    /**
     * @brief 检查与处理设备是否新加入的情况 && 获取指定 dbAddr 的数据管理器。
     *
     * @param dbAddr 客户端的数据库地址。
     * @param isTemp 引用一个布尔值，如果数据管理器是临时的则设置为 true。
     * @return 指向指定 dbAddr 的 MdbsTcpDataHandler 的指针。
     */
    std::shared_ptr<MdbsTcpDataHandler> getDataHandler(uint32_t dbAddr, bool &isTemp);

    /**
     * @brief 获取指定 IP 地址的 Modbus 客户端。
     *
     * @param ip 客户端的 IP 地址。
     * @return 指向指定 IP 地址的 ModbusTcpClient 的指针。
     */
    // ModbusTcpClient *getModbusClient(const std::string &ip);

    /**
     * @brief 通过读取和写入 Modbus 数据处理客户端。
     *
     * @param client 指向 ModbusTcpClient 的指针。
     * @param modbDataHandler 指向 MdbsTcpDataHandler 的指针。
     * @param isTemp 表示数据管理器是否是临时的。
     */
    void processClient(std::shared_ptr<ModbusTcpClient> client, std::shared_ptr<MdbsTcpDataHandler> modbDataHandler, bool isTemp);

    /**
     * @brief 通知指定 dbAddr 的读取操作已完成。
     *
     * @param dbAddr 客户端的数据库地址。
     */
    void notifyReadState(uint32_t dbAddr, bool state);

    /**
     * @brief 处理客户端离线的情况。
     *
     * @param ip 客户端的 IP 地址。
     * @param dbAddr 客户端的数据库地址。
     */
    void handleOfflineClient(const std::string &ip, uint32_t dbAddr);

    /**
     * @brief 提示用户对当前设备采取的操作。
     *
     * 该函数显示当前设备的数据库地址，并要求用户选择是添加新设备还是替换现有设备。
     */
    void promptUserForAction();

    /**
     * @brief 处理来自控制台的用户输入。
     *
     * 该函数读取用户的输入并相应地处理。如果系统正在等待设备替换输入，它会处理该输入；
     * 否则，它会根据用户的选择处理待处理的设备。
     */
    void handleUserInput();

    /**
     * @brief 处理用户输入的设备替换信息。
     *
     * @param input 用户输入的指定要替换的设备 ID。
     *
     * 该函数在系统等待设备替换输入时处理用户输入。它验证设备 ID 并继续用新设备的数据替换旧设备的数据。
     */
    void handleReplacementInput(const std::string &input);

    /**
     * @brief 用新设备的数据替换旧设备的数据。
     *
     * @param oldDbAddr 旧设备的数据库地址。
     * @param deviceId 要替换的设备 ID。
     *
     * 该函数更新内部映射和数据结构，以用新设备的数据替换旧设备的数据。它还更新数据库并将系统时间与新设备同步。
     */
    void replaceDeviceData(uint32_t oldDbAddr, int deviceId);

    /**
     * @brief 向系统添加新设备。
     *
     * @param dbAddr 新设备的数据库地址。
     *
     * 该函数将新设备的数据插入到主要数据结构和数据库中。它还将系统时间与新设备同步并更新内部映射。
     */
    void addNewDevice(uint32_t dbAddr);

    /**
     * @brief 用于创建mdbs从站。
     *
     * @param ip 从站IP地址。
     * @param port 从站端口号。
     * @param slaveId 从站id
     *
     */
    void initializeSlave(const std::string &ip, int port, int slaveId);

    /**
     * @brief 根据设备 id 来获取对应的设备 dbAddr。
     *
     * @param deviceId 设备id。
     *
     */
    uint32_t getDbAddrFromId(int deviceId) const;

    /**
     * @brief 根据设备 dbAddr 来获取对应的设备 id。
     *
     * @param dbAddr 设备 dbAddr。
     *
     */
    int getIdFromDbAddr(uint32_t dbAddr) const;

    /**
     * @brief 接收和处理主站请求任务。
     *
     * @param client_ctx 从站描述符。
     *
     */
    void receiveRawData(modbus_t *client_ctx);

    /**
     * @brief 处理 SlaveId 为0的读任务（汇总信息）。
     *
     * @param client_ctx 从站描述符。
     * @param query 主站请求包。
     *
     */
    void handleSummaryReadRequest(modbus_t *client_ctx, uint8_t *query);

    /**
     * @brief 处理 SlaveId 为0的写任务（汇总信息）。
     *
     * @param client_ctx 从站描述符。
     * @param query 主站请求包。
     *
     */
    void handleSummaryWriteRequest(modbus_t *client_ctx, uint8_t *query);

    /**
     * @brief 遍历并处理所有数据处理器的数据
     */
    void processAllDataHandlers();

    /**
     * @brief 处理单个数据处理器的数据
     * @param dataHandler 数据处理器对象的智能指针
     * @param dbAddr 数据库地址，用于标识数据处理器
     * @param summaryData 引用，用于累积汇总数据
     */
    void processDataHandler(const std::shared_ptr<MdbsTcpDataHandler> &dataHandler, uint32_t dbAddr);

    /**
     * @brief 通知系统某个数据处理器的数据已准备就绪
     * @param dbAddr 数据库地址，用于标识数据处理器
     */
    void notifyDataReady(uint32_t dbAddr);

    /**
     * @brief 分配 totalPower 给所有在线的 MdbsTcpDataHandler
     * @param totalPowerValue 总的有功功率值
     */
    bool distributeTotalPowerToHandlers(double totalPowerValue);

    /**
     * @brief modbus Tcp 写任务（总）
     */
    void handleWritesTask();

    /**
     * @brief modbus Tcp 写任务线程
     * @param ip 客户端的 IP 地址。
     * @param dbAddr 客户端的数据库地址。
     */
    void handlePendingWrites(const std::string &ip, uint32_t dbAddr);

    // 接收线程相关成员变量
    std::vector<std::thread> m_modbusRxThreads;

    // 发送线程相关成员变量
    std::vector<std::thread> m_modbusTxThreads;

    // MdbsTcpDataManager m_dataManager;              // 使用 MdbsTcpDataManager 管理实例
    std::unordered_map<uint32_t, int> m_idMap;     // 编号与dbAddr的映射表
    std::unordered_set<uint32_t> m_pendingDevices; // 跟踪正在等待用户确认的设备 (元素保持唯一性)
    std::unordered_map<uint32_t, std::unordered_map<std::string, std::chrono::steady_clock::time_point>> m_lastReadTimesMap; // 键1：Addr 键2：寄存器集 (数据块) 路径 键3:超时时间内的经历时间
    std::unordered_map<uint32_t, std::unordered_map<std::string, std::chrono::steady_clock::time_point>> m_lastSaveDbTimesMap; // 键1：Addr 键2：寄存器集 (数据块) 路径 键3:超时时间内的经历时间

    std::string m_dbPath;                 // 数据库路径
    std::string m_mbdsDataBakTableName;   // 存储临时数据表
    std::string m_deviceInfoTableName;    // 存储设备身份信息表
    std::string m_offlineDeviceTableName; // 存储离线设备表
    int m_currentId = 1;                  // 用于生成唯一编号
    uint32_t m_dbAddrCurrent = 0;         // 当前正在处理的临时设备的数据库地址

    bool m_awaitingReplacementInput = false; // 标记是否在等待替换设备的输入
    bool m_hasPromptedForInput = false;      // 记录是否已经提醒过用户

    std::mutex m_dataReadyMutex; // mdbs数据读取解析完成条件锁
    // std::condition_variable m_dataReadyCondVar; // mdbs数据读取解析完成条件变量
    std::unordered_map<uint32_t, bool> m_dataReadyMap; // 存储mdbs数据读取完成情况 （键：dbAddr 值：完成情况）

    std::mutex m_readCompleteMutex;
    std::condition_variable m_readCompleteCondVar;
    std::unordered_map<uint32_t, bool> m_readCompleteMap;

    std::mutex m_pendingDevicesMutex;
    // std::mutex m_clientMutex;
    std::mutex m_lastReadTimesMutex;   // 上次读取数据块时间的锁
    std::mutex m_lastSaveDbTimesMutex; // 上次保存到数据库时间的锁

    std::unique_ptr<qModbusTcpSlave> m_slave;

    // 数据处理器实例
    MdbsTcpDataManager &m_dataManager;

    bool m_isMonthlyCleanup = false; // 当月是否已执行清理操作

    /**
     * @brief 保存设备的系统SN到数据库
     * @param dbAddr 设备的数据库地址
     * @return 是否成功保存SN
     */
    bool saveSystemSn(uint32_t dbAddr);
};

#endif // TCP_TASK_H