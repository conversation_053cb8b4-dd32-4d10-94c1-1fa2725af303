#include "qModbusTcpSlave.h"
#include "Logger.h"
#include <cstring>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <sys/socket.h>
#include <unistd.h>

qModbusTcpSlave::qModbusTcpSlave(const std::string &ip_address, int port, int slave_id)
    : m_slaveId(slave_id), m_ipAddress(ip_address), m_port(port), m_connected(false) {
    m_ctx = modbus_new_tcp(ip_address.c_str(), port);

    if (m_ctx == nullptr) {
        throw std::runtime_error("Failed to create Modbus context");
    }

    if (modbus_set_slave(m_ctx, m_slaveId) == -1) {
        modbus_free(m_ctx);
        throw std::runtime_error("Failed to set slave ID");
    }

    // 设置modbus超时时间 100 毫秒
    struct timeval timeout;
    timeout.tv_sec = 0;
    timeout.tv_usec = 100000;
    modbus_set_response_timeout(m_ctx, timeout.tv_sec, timeout.tv_usec);

    // m_serverSocket = modbus_tcp_listen(m_ctx, m_listenCount); // 只允许监听5个 最大量10个
    // if (m_serverSocket == -1) {
    //     modbus_close(m_ctx);
    //     modbus_free(m_ctx);
    //     m_ctx = nullptr;
    //     throw std::runtime_error("Failed to listen");
    // }

    // 创建服务器套接字
    m_serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (m_serverSocket == -1) {
        modbus_close(m_ctx);
        modbus_free(m_ctx);
        m_ctx = nullptr;
        throw std::runtime_error("Failed to create server socket");
    }

    // 设置 SO_REUSEADDR 选项
    int reuse = 1;
    if (setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEADDR, &reuse, sizeof(reuse)) == -1) {
        close(m_serverSocket);
        modbus_close(m_ctx);
        modbus_free(m_ctx);
        m_ctx = nullptr;
        throw std::runtime_error("Failed to set SO_REUSEADDR");
    }

    // 设置 SO_REUSEPORT 选项
    int reuse_port = 1;
    if (setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEPORT, &reuse_port, sizeof(reuse_port)) == -1) {
        LOG_ERROR << "设置 SO_REUSEPORT 失败: " << strerror(errno);
        close(m_serverSocket);
        modbus_free(m_ctx);
        m_ctx = nullptr;
        throw std::runtime_error("无法设置 SO_REUSEPORT");
    }

    // 绑定地址和端口
    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(m_port);
    addr.sin_addr.s_addr = inet_addr(m_ipAddress.c_str());
    if (bind(m_serverSocket, (struct sockaddr *)&addr, sizeof(addr)) == -1) {
        LOG_ERROR << "Failed to bind: " << strerror(errno);
        close(m_serverSocket);
        modbus_close(m_ctx);
        modbus_free(m_ctx);
        m_ctx = nullptr;
        throw std::runtime_error("Failed to bind server socket");
    }

    // 开始监听
    if (listen(m_serverSocket, m_listenCount) == -1) {
        LOG_ERROR << "Failed to listen: " << strerror(errno);
        close(m_serverSocket);
        modbus_close(m_ctx);
        modbus_free(m_ctx);
        m_ctx = nullptr;
        throw std::runtime_error("Failed to listen on server socket");
    }

    // 将服务器套接字设置到 Modbus 上下文中
    modbus_set_socket(m_ctx, m_serverSocket);
    m_connected = true;
}

qModbusTcpSlave::~qModbusTcpSlave() {
    std::lock_guard<std::recursive_mutex> lock(m_connectionMutex);
    cleanAllClient();

    if (m_ctx != nullptr) {
        modbus_close(m_ctx);
        modbus_free(m_ctx);
        close(m_serverSocket);
        m_ctx = nullptr;
    }
}

void qModbusTcpSlave::acceptConnect() {
    // 递归锁
    std::lock_guard<std::recursive_mutex> lock(m_connectionMutex);

    fd_set rfds;
    FD_ZERO(&rfds);
    FD_SET(m_serverSocket, &rfds);

    struct timeval tv;
    tv.tv_sec = 0; // 不阻塞，影响后面响应回复报文的规定响应时间
    tv.tv_usec = 0;

    int ret = select(m_serverSocket + 1, &rfds, nullptr, nullptr, &tv); // 监听的最大端口号为 m_serverSocket + 1
    if (ret == -1) {
        // 处理错误
        LOG_ERROR << "select failed: " << modbus_strerror(errno);
        reconnect();
        return;
    } else if (ret == 0) {
        return;
    } else {
        if (FD_ISSET(m_serverSocket, &rfds)) {
            int client_socket = modbus_tcp_accept(m_ctx, &m_serverSocket);
            if (client_socket != -1) {
                // 为新客户端创建一个Modbus context，并且设置 TCP KeepAlive
                int optval = 1;
                if (setsockopt(client_socket, SOL_SOCKET, SO_KEEPALIVE, &optval, sizeof(optval)) == -1) {
                    LOG_ERROR << "Failed to set SO_KEEPALIVE: " << strerror(errno);
                }

                // 根据需要可再进一步定制 KeepAlive 的各类参数
                // Idle 时间、探测间隔、探测次数等
                int keepIdle = 5;     // 空闲 3s 后开始发送探测包
                int keepInterval = 2; // 每 5s 发送一次探测包
                int keepCount = 3;    // 连续发送 3 个探测包无响应则认定连接失效
                setsockopt(client_socket, IPPROTO_TCP, TCP_KEEPIDLE, &keepIdle, sizeof(keepIdle));
                setsockopt(client_socket, IPPROTO_TCP, TCP_KEEPINTVL, &keepInterval, sizeof(keepInterval));
                setsockopt(client_socket, IPPROTO_TCP, TCP_KEEPCNT, &keepCount, sizeof(keepCount));
                modbus_t *client_ctx = modbus_new_tcp(m_ipAddress.c_str(), m_port);
                if (client_ctx == nullptr) {
                    LOG_ERROR << "Failed to create client context";
                    close(client_socket); // 确保socket关闭
                } else {
                    modbus_set_socket(client_ctx, client_socket);
                    m_clientContexts[client_socket] = client_ctx;
                }
            }
        }
    }
}

void qModbusTcpSlave::reconnect() {
    if (m_connected) {
        return;
    }

    try {
        // 递归锁
        std::lock_guard<std::recursive_mutex> lock(m_connectionMutex);

        // 复位旧的 ctx 上下文
        if (m_ctx != nullptr) {
            modbus_close(m_ctx);
            modbus_free(m_ctx);
            close(m_serverSocket);
            m_ctx = nullptr;
        }

        // 创建新的 ctx 上下文
        m_ctx = modbus_new_tcp(m_ipAddress.c_str(), m_port);
        if (m_ctx == nullptr) {
            throw std::runtime_error("Failed to create Modbus context");
        }

        if (modbus_set_slave(m_ctx, m_slaveId) == -1) {
            throw std::runtime_error("Failed to set slave ID");
        }

        // 设置modbus超时时间 100 毫秒
        struct timeval timeout;
        timeout.tv_sec = 0;
        timeout.tv_usec = 100000;
        modbus_set_response_timeout(m_ctx, timeout.tv_sec, timeout.tv_usec);

        // m_serverSocket = modbus_tcp_listen(m_ctx, m_listenCount); // 只允许监听5个 最大量10个
        // if (m_serverSocket == -1) {
        //     modbus_close(m_ctx);
        //     modbus_free(m_ctx);
        //     m_ctx = nullptr;
        //     throw std::runtime_error("Failed to listen");
        // }

        // 创建服务器套接字
        m_serverSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (m_serverSocket == -1) {
            modbus_close(m_ctx);
            modbus_free(m_ctx);
            m_ctx = nullptr;
            throw std::runtime_error("Failed to create server socket");
        }

        // 设置 SO_REUSEADDR 选项
        int reuse = 1;
        if (setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEADDR, &reuse, sizeof(reuse)) == -1) {
            close(m_serverSocket);
            modbus_close(m_ctx);
            modbus_free(m_ctx);
            m_ctx = nullptr;
            throw std::runtime_error("Failed to set SO_REUSEADDR");
        }

        // 设置 SO_REUSEPORT 选项
        int reuse_port = 1;
        if (setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEPORT, &reuse_port, sizeof(reuse_port)) == -1) {
            LOG_ERROR << "设置 SO_REUSEPORT 失败: " << strerror(errno);
            close(m_serverSocket);
            modbus_free(m_ctx);
            m_ctx = nullptr;
            throw std::runtime_error("无法设置 SO_REUSEPORT");
        }

        // 绑定地址和端口
        struct sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_port = htons(m_port);
        addr.sin_addr.s_addr = inet_addr(m_ipAddress.c_str());
        if (bind(m_serverSocket, (struct sockaddr *)&addr, sizeof(addr)) == -1) {
            LOG_ERROR << "Failed to bind: " << strerror(errno);
            close(m_serverSocket);
            modbus_close(m_ctx);
            modbus_free(m_ctx);
            m_ctx = nullptr;
            throw std::runtime_error("Failed to bind server socket");
        }

        // 开始监听
        if (listen(m_serverSocket, m_listenCount) == -1) {
            LOG_ERROR << "Failed to listen: " << strerror(errno);
            close(m_serverSocket);
            modbus_close(m_ctx);
            modbus_free(m_ctx);
            m_ctx = nullptr;
            throw std::runtime_error("Failed to listen on server socket");
        }

        // 将服务器套接字设置到 Modbus 上下文中
        modbus_set_socket(m_ctx, m_serverSocket);
        m_connected = true;
    } catch (const std::exception &e) {
        LOG_ERROR << "Reconnect failed: " << e.what();

        if (m_ctx != nullptr) {
            modbus_close(m_ctx);
            modbus_free(m_ctx);
            m_ctx = nullptr;
            m_connected = false;
        }
    }
}

uint8_t qModbusTcpSlave::receiveQuery(modbus_t *client_ctx, uint8_t *query, int &rc) {
    // 递归锁
    std::lock_guard<std::recursive_mutex> lock(m_connectionMutex);
    if (!m_connected) {
        reconnect();
        rc = -1;
        return rc;
    }

    int sockfd = modbus_get_socket(client_ctx);
    fd_set readfds;
    FD_ZERO(&readfds);
    FD_SET(sockfd, &readfds);
    struct timeval tv;
    tv.tv_sec = 0;
    tv.tv_usec = 0; // 不阻塞

    int retSel = select(sockfd + 1, &readfds, nullptr, nullptr, &tv);
    if (retSel <= 0) {
        // 无数据/出错，避免直接卡死在 modbus_receive
        rc = -1;
        return rc;
    }

    rc = modbus_receive(client_ctx, query);

    if (rc == -1) {
        int err = errno;
        LOG_ERROR << "Error receiving Modbus query: " << modbus_strerror(err);

        // if (errno == ECONNREFUSED || errno == EHOSTUNREACH || errno == ENETDOWN || errno == ENETUNREACH ||
        //     errno == ETIMEDOUT || errno == EPIPE || errno == ENOTSOCK) {
        // }

        if (errno == EHOSTUNREACH || errno == ENETDOWN || errno == ENETUNREACH || errno == ENOTSOCK) {
            m_connected = false;
            m_clientResetFlag = false;

            cleanAllClient();
            reconnect();
        } else if (errno == ECONNRESET || errno == ETIMEDOUT || errno == EPIPE) {
            m_clientResetFlag = false;
            // 连接已关闭，需要移除客户端上下文
            removeClientContext(client_ctx);
        }
    } else if (rc == 0) {
        m_clientResetFlag = false;
        // 连接已关闭，需要移除客户端上下文
        removeClientContext(client_ctx);
    }
    return rc;
}

int qModbusTcpSlave::handleRequest(modbus_t *client_ctx, const uint8_t *query, std::vector<uint8_t> &response) {
    uint8_t functionCode = query[7];

    // 只处理功能码，具体逻辑在 TcpTask 中处理
    switch (functionCode) {
    case MODBUS_FC_READ_HOLDING_REGISTERS:
    case MODBUS_FC_READ_INPUT_REGISTERS:
    case MODBUS_FC_WRITE_SINGLE_REGISTER:
    case MODBUS_FC_WRITE_MULTIPLE_REGISTERS:
        return functionCode; // 返回功能码，具体处理在 TcpTask
    default:
        // 0 表示成功发送异常响应，或 -1 表示发生错误
        return modbus_reply_exception(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_FUNCTION);
    }
}

// int qModbusTcpSlave::handleReadRegisters(
//     modbus_t *client_ctx, const uint8_t *query,
//     const std::unordered_map<std::string, std::unordered_map<int, std::shared_ptr<RegisterData>>> &registers,
//     std::vector<uint8_t> &response) {

//     int unitID = query[6];
//     int functionCode = query[7];
//     int address = (query[8] << 8) + query[9];
//     int num = (query[10] << 8) + query[11];

//     response.resize(9 + num * 2);
//     response[0] = query[0];             // Transaction ID High
//     response[1] = query[1];             // Transaction ID Low
//     response[2] = 0x00;                 // Protocol ID High
//     response[3] = 0x00;                 // Protocol ID Low
//     response[4] = (3 + num * 2) >> 8;   // Byte Count High
//     response[5] = (3 + num * 2) & 0xFF; // Byte Count Low
//     response[6] = unitID;               // Unit ID
//     response[7] = functionCode;         // Function Code
//     response[8] = num * 2;              // Byte Count

//     for (const auto registerIt : registers) {
//         const auto regName = registerIt.first;
//         const auto &registerMap = registerIt.second;
//         if (registerMap.find(address) != registerMap.end()) {
//             for (int i = 0; i < num;) {
//                 auto regData = registerMap.at(address + i);
//                 if (true) {
//                     // 取出数据对象
//                     // auto regData = regDataIt->second;
//                     const std::vector<uint16_t> &values = regData->getRawData();
//                     size_t valueCount = regData->getDataLength();

//                     // 填充 response 数组
//                     for (size_t j = 0; j < valueCount; ++j) {
//                         if (3 + i * 2 + j * 2 + 1 < MODBUS_TCP_MAX_ADU_LENGTH) {
//                             response[9 + i * 2 + j * 2] = values[j] >> 8;
//                             response[10 + i * 2 + j * 2] = values[j] & 0xFF;
//                         } else {
//                             std::cerr << "Error: Response buffer overflow" << std::endl;
//                             // Response buffer overflow
//                             return modbus_reply_exception(client_ctx, query,
//                             MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE);
//                         }
//                     }
//                     i += valueCount;
//                 } else {
//                     // 数据默认全0，不需要再次把相应位填0
//                     i += 1; // 默认按16位处理
//                 }
//             }
//             return response.size();
//         }
//     }
//     // 0 表示成功发送异常响应，或 -1 表示发生错误
//     return modbus_reply_exception(client_ctx, query, MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS);
// }

void qModbusTcpSlave::sendResponse(modbus_t *client_ctx, std::vector<uint8_t> &response, int responseLength) {
    const uint8_t *responsePtr = response.data();
    // for (int i = 0; i < responseLength; ++i) {
    //     std::cout << "response[" << i << "] = 0x" << std::hex << std::setw(2) << std::setfill('0')
    //               << static_cast<int>(responsePtr[i]) << std::dec << std::endl;
    // }

    for (auto it = m_clientContexts.begin(); it != m_clientContexts.end(); ++it) {
        if (it->second == client_ctx) {
            auto clientSocket = it->first;
            send(clientSocket,
                 responsePtr,
                 responseLength,
                 MSG_NOSIGNAL); // 屏蔽对已关闭的 socket 进行写操作，send 将不会引发 SIGPIPE，而是返回错误
        }
    }
}

int qModbusTcpSlave::packReadRegistersResponse(const uint8_t *request, const std::vector<ModbusDataType> &data, uint8_t *response) {
    // 复制MBAP报文头
    memcpy(response, request, MODBUS_TCP_HEADER_LENGTH);

    // 设置从机地址和功能码
    response[6] = request[6]; // 从机地址
    response[7] = request[7]; // 功能码 (读寄存器功能码)

    // 初始化字节数和偏移量
    uint16_t byteCount = 0;
    size_t offset = 9; // 数据部分从 response[9] 开始

    // 复制数据并同时计算字节数
    for (const auto &d : data) {
        if (std::holds_alternative<uint16_t>(d)) {
            // 处理16位数据
            uint16_t value = std::get<uint16_t>(d);
            response[offset] = value >> 8;       // 高字节
            response[offset + 1] = value & 0xFF; // 低字节

            offset += 2;
            byteCount += 2; // 16位数据占用2字节
        } else if (std::holds_alternative<uint32_t>(d)) {
            // 处理32位数据 (中端字节序)
            uint32_t value = std::get<uint32_t>(d);
            response[offset] = (value >> 8) & 0xFF;      // 低字节（高）
            response[offset + 1] = value & 0xFF;         // 低字节（低）
            response[offset + 2] = (value >> 24) & 0xFF; // 高字节（高）
            response[offset + 3] = (value >> 16) & 0xFF; // 高字节（低）

            offset += 4;
            byteCount += 4; // 32位数据占用4字节
        }
    }

    // 设置字节数
    response[8] = byteCount;

    // PDU 长度 (功能码1字节 + 字节数1字节 + 数据 byteCount)
    uint16_t pduLength = 1 + 1 + byteCount;

    // 设置MBAP头中的PDU长度（高字节在前，低字节在后）
    response[4] = (pduLength >> 8) & 0xFF; // 高字节
    response[5] = pduLength & 0xFF;        // 低字节

    // 返回整个Modbus TCP报文的长度 (MBAP头长度 + PDU长度)
    return MODBUS_TCP_HEADER_LENGTH + pduLength;
}

int qModbusTcpSlave::packWriteRegistersResponse(const uint8_t *request, uint16_t registersWritten, uint8_t *response) {
    // 复制MBAP报文头
    memcpy(response, request, MODBUS_TCP_HEADER_LENGTH);

    // 获取功能码
    uint8_t functionCode = request[7];

    // 设置从机地址和功能码
    response[6] = request[6];   // 从机地址
    response[7] = functionCode; // 功能码

    // 设置起始地址
    response[8] = request[8]; // 起始地址高字节
    response[9] = request[9]; // 起始地址低字节

    uint16_t pduLength = 0;

    // 根据功能码处理不同的响应
    if (functionCode == MODBUS_FC_WRITE_SINGLE_REGISTER) {
        // 写单个寄存器的响应 (功能码 0x06)
        // 响应格式与请求相同：寄存器地址(2字节) + 写入值(2字节)
        response[10] = request[10]; // 写入值高字节
        response[11] = request[11]; // 写入值低字节

        // PDU 长度 (功能码1字节 + 寄存器地址2字节 + 写入值2字节)
        pduLength = 1 + 2 + 2;
    } else if (functionCode == MODBUS_FC_WRITE_MULTIPLE_REGISTERS) {
        // 写多个寄存器的响应 (功能码 0x10)
        // 响应格式：起始地址(2字节) + 写入的寄存器数量(2字节)
        response[10] = (registersWritten >> 8) & 0xFF; // 寄存器数量高字节
        response[11] = registersWritten & 0xFF;        // 寄存器数量低字节

        // PDU 长度 (功能码1字节 + 起始地址2字节 + 写入的寄存器数量2字节)
        pduLength = 1 + 2 + 2;
    } else {
        // 不支持的功能码，返回错误
        return -1;
    }

    // 设置MBAP头中的PDU长度（高字节在前，低字节在后）
    response[4] = (pduLength >> 8) & 0xFF; // 高字节
    response[5] = pduLength & 0xFF;        // 低字节

    // 返回整个Modbus TCP报文的长度 (MBAP头长度 + PDU长度)
    return MODBUS_TCP_HEADER_LENGTH + pduLength;
}

void qModbusTcpSlave::modbusReplyException(modbus_t *client_ctx, const uint8_t *req, uint8_t exception_code) {
    modbus_reply_exception(client_ctx, req, exception_code);
}

void qModbusTcpSlave::removeClientContext(modbus_t *client_ctx) {
    std::lock_guard<std::recursive_mutex> lock(m_connectionMutex);
    for (auto it = m_clientContexts.begin(); it != m_clientContexts.end(); ++it) {
        if (it->second == client_ctx) {
            modbus_close(client_ctx);
            modbus_free(client_ctx);
            close(it->first);
            m_clientContexts.erase(it);
            break;
        }
    }
}

void qModbusTcpSlave::cleanAllClient() {
    std::lock_guard<std::recursive_mutex> lock(m_connectionMutex);
    for (auto &pair : m_clientContexts) {
        modbus_close(pair.second);
        modbus_free(pair.second);
        close(pair.first);
    }
    m_clientContexts.clear();
}