#ifndef UDP_CLIENT_DATA_HANDLER_H
#define UDP_CLIENT_DATA_HANDLER_H

#include "ModbusTcpClient.h"
#include "StructDefs.h"
#include "qSQLiteDB.h"
#include <chrono>
#include <memory>
#include <mutex>
#include <netinet/in.h>
#include <unordered_map>
#include <unordered_set>
#include "ConfigManager.h"

// 固定设备信息结构体定义
struct FixedDeviceInfo {
    std::string ip;
    uint32_t dbAddr;
    int port;
};

class UdpClientDataHandler {
  public:
    static UdpClientDataHandler &getInstance();

    // 处理接收到的数据
    void handleData(char *data, size_t len, const sockaddr_in &source);

    // 管理设备映射表和连接
    void manageMap();

    /**
     * @brief 检查给定 dbAddr 的客户端是否离线。
     *
     * @param dbAddr 客户端的数据库地址。
     * @return 如果客户端离线返回 true，否则返回 false。
     */
    bool isClientOffline(uint32_t dbAddr);

    /**
     * @brief 用于检查临时设备是否离线
     *
     * @param dbAddr 临时客户端的数据库地址。
     * @return 如果临时客户端离线返回 true，否则返回 false。
     */
    bool isTempClientOffline(uint32_t dbAddr);

    /**
     * @brief 用于判断设备是否为正式设备
     *
     * @param dbAddr 客户端的数据库地址。
     * @return 如果返回 true 表示为正式设备，否则返回 false 表示为临时设备。
     */
    bool isFormalDevice(uint32_t dbAddr);

    /**
     * @brief 获取指定 IP 地址的 Modbus 客户端。
     *
     * @param ip 客户端的 IP 地址。
     * @return 获取成功返回指定客户端的共享指针，否则返回空共享指针。
     */
    std::shared_ptr<ModbusTcpClient> getModbusClient(const std::string &ip);

    /**
     * @brief 获取当前 m_energyDataMap 快照
     *
     * @return 获取当前 m_energyDataMap 快照。
     */
    std::unordered_map<uint32_t, EnergyDataWithIP> getEnergyDataMap();

    /**
     * @brief 初始化固定设备连接
     * 从配置文件读取固定设备信息并尝试建立连接
     */
    void initFixedDevices();

    /**
     * @brief 检查并尝试重连固定设备
     */
    void checkFixedDevicesConnection();

    std::unordered_map<uint32_t, EnergyDataWithIP> m_energyDataMap; // 哈希表实现，更快的查找和插入，不需有序存储
    std::mutex m_offlineDevicesMutex;                               // 离线设备集合读写锁
    std::unordered_set<uint32_t> m_offlineDevices; // 记录离线客户端(dbAddress) (该表由哈希实现，无序插入和取出,并且元素保持唯一性)

    std::set<uint32_t> m_offlineTempDevices; // 存储离线的临时设备
    std::mutex m_offlineTempMutex;           // 保护 m_offlineTempDevices 的互斥锁
    std::unordered_map<std::string, std::shared_ptr<ModbusTcpClient>> m_modbusClients; // 键:ip

  private:
    UdpClientDataHandler();
    ~UdpClientDataHandler() = default;
    UdpClientDataHandler(const UdpClientDataHandler &) = delete;
    UdpClientDataHandler &operator=(const UdpClientDataHandler &) = delete;

    const int8_t m_energyDataLen = 18;                                       // byte
    const std::chrono::seconds m_timeoutDuration = std::chrono::seconds(10); // 表格更新超时临界值 seconds

    std::string m_offlineDeviceTableName; // 存储离线设备表

    mutable std::mutex m_mapMutex; // 保护 m_energyDataMap

    // 新增成员变量
    bool m_useFixedDevices;                                       // 是否使用固定设备模式
    bool m_useUdpDiscovery;                                       // 是否使用UDP设备发现
    std::vector<FixedDeviceInfo> m_fixedDevices;                  // 固定设备信息列表
    std::chrono::seconds m_reconnectInterval;                     // 固定设备重连间隔
    std::chrono::steady_clock::time_point m_lastReconnectAttempt; // 上次重连尝试时间
};

#endif // UDP_CLIENT_DATA_HANDLER_H