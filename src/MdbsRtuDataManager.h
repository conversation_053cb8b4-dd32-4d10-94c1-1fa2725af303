#ifndef MDBSRTUDATAMANAGER_H
#define MDBSRTUDATAMANAGER_H

#include "MdbsRtuDataHandler.h"
#include <memory>
#include <string>
#include <yaml-cpp/yaml.h>

class MdbsRtuDataManager {
  public:
    /**
     * @brief 获取 MdbsRtuDataManager 的唯一实例
     * @return 静态 MdbsRtuDataManager 实例的引用
     */
    static MdbsRtuDataManager &getInstance() {
        static MdbsRtuDataManager instance; // 静态局部变量，第一次调用时初始化，之后保持实例
        return instance;
    }

    // 禁止拷贝构造和赋值操作符
    MdbsRtuDataManager(const MdbsRtuDataManager &) = delete;
    MdbsRtuDataManager &operator=(const MdbsRtuDataManager &) = delete;

    // 禁止移动构造和移动赋值操作符
    MdbsRtuDataManager(MdbsRtuDataManager &&) = delete;
    MdbsRtuDataManager &operator=(MdbsRtuDataManager &&) = delete;

    /**
     * @brief 加载配置文件并初始化当前协议的处理器
     */
    void loadConfig();

    /**
     * @brief 重新加载配置文件，切换当前协议
     */
    void reloadConfig();

    /**
     * @brief 获取当前的 MdbsRtuDataHandler
     * @return 指向当前处理器的指针
     */
    std::shared_ptr<MdbsRtuDataHandler> getHandler();

    /**
     * @brief 计算电压 U = URMSx × UrAt
     * @param URMSx 用户提供的参数，用于计算
     * @return 返回计算后的电压值，计算失败返回 -1
     */
    float calculateVoltage(float URMSx);

    /**
     * @brief 计算电流 I = IRMSx × IrAt
     * @param IRMSx 用户提供的电流基准值
     * @return 返回计算后的电流值，计算失败返回 -1
     */
    float calculateCurrent(float IRMSx);

    /**
     * @brief 计算有功功率 P = Px × UrAt × IrAt
     * @param Px 功率基准值
     * @return 返回计算后的有功功率值，计算失败返回 -1
     */
    float calculateActivePower(float Px);

    /**
     * @brief 计算无功功率 Q = Qx × UrAt × IrAt
     * @param Qx 无功功率基准值
     * @return 返回计算后的无功功率值，计算失败返回 -1
     */
    float calculateReactivePower(float Qx);

    /**
     * @brief 计算电能 Ep = E × UrAt × IrAt
     * @param E 电能基准值
     * @return 返回计算后的电能值，计算失败返回 -1
     */
    float calculateEnergy(float E);

  private:
    MdbsRtuDataManager();

    std::string m_currentProtocolId;                      // 当前协议ID
    std::shared_ptr<MdbsRtuDataHandler> m_currentHandler; // 当前协议的处理器
    YAML::Node m_config;                                  // 配置文件节点
};

#endif // MDBSRTUDATAMANAGER_H