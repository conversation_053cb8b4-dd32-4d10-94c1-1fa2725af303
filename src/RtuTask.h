#ifndef RTU_TASK_H
#define RTU_TASK_H

#include "MdbsRtuDataManager.h"
#include "ModbusRtuMaster.h"
#include "StructDefs.h"
#include <chrono>
#include <mutex>
#include <string>
#include <unordered_map>

/**
 * @brief RtuTask 类，负责处理 Modbus RTU 的读写任务
 */
class RtuTask {
  public:
    /**
     * @brief 构造函数，初始化 Modbus RTU 连接
     * @param device 设备名称，例如 "/dev/ttyUSB0"
     * @param baud 波特率
     * @param parity 校验方式，'N'（无校验）、'E'（偶校验）、'O'（奇校验）
     * @param dataBit 数据位，7 或 8
     * @param stopBit 停止位，1 或 2
     */
    RtuTask();

    /**
     * @brief 析构函数，释放资源
     */
    ~RtuTask();

    /**
     * @brief 处理 Modbus RTU 的读写操作
     *
     * 读写都在同一线程
     */
    void RtuCommTask();

  private:
    /**
     * @brief 读取 Modbus 数据
     * @param registerType 寄存器类型（输入寄存器或保持寄存器）
     */
    void readModbusData(const std::string &registerType);

    /**
     * @brief 读取指定的寄存器数据块
     * @param registerType 寄存器类型
     * @param startAddress 起始地址
     * @param length 读取长度
     * @param isRead 是否读取
     * @param isSave 是否保存到数据库
     */
    void readData(const std::string &registerType, uint16_t startAddress, size_t length, bool isRead, bool isSave);

    /**
     * @brief 处理写操作
     * @param writeBatch 待写入的数据批次
     * @return 是否写入成功
     */
    bool handleWriteOperation(const std::vector<std::pair<int, std::any>> &writeBatch);

    /**
     * @brief 将任意类型的数据转换为 Modbus 寄存器值
     * @param data 要转换的数据
     * @param registers 转换后的寄存器值
     * @return 转换是否成功
     */
    bool convertToValues(const std::any &data, std::vector<uint16_t> &registers);

    /**
     * @brief 获取当前时间戳，单位毫秒
     * @return 当前时间戳的字符串
     */
    std::string getCurrentTimestamp();

    /**
     * @brief 获取指定485口的配置信息
     * @param config 配置文件根节点
     * @param rs485Name 485口名称 例如: RS485-1, RS485-2, RS485-3
     */
    bool parseRS485Config(const YAML::Node &config, const std::string &rs485Name, RS485Config &outConfig);

    /**
     * @brief 将数据保存到数据库
     *
     * @param registerType 寄存器类型
     * @param startAddress 起始地址
     * @param length 长度
     * @param data 数据
     */
    void saveDataToDatabase(uint8_t registerTypeCode, uint16_t startAddress, uint16_t length, const std::vector<uint16_t> &data);

    // Modbus RTU 主站
    std::unique_ptr<ModbusRtuMaster> m_rtuMaster;

    // 数据处理器通过数据管理器获取
    MdbsRtuDataManager &m_dataManager;

    // 数据库相关
    std::string m_dbPath;

    // 时间管理
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> m_lastReadTimes;
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> m_lastSaveDbTimes;
    std::mutex m_lastReadTimesMutex;
    std::mutex m_lastSaveDbTimesMutex;
};

#endif // RTU_TASK_H