#ifndef QMYSQL_DB_H
#define QMYSQL_DB_H

#include "ConfigManager.h"
#include "IMySQLDB.h"
#include "StructDefs.h"
#include "DatabaseAccess.h"
#include <mutex>
#include <unordered_map>

class qMySQLDB : public IMySQLDB {
  public:
    /**
     * @brief 构造函数，创建qMySQLDB实例
     *
     * @param host MySQL主机名
     * @param user MySQL用户名
     * @param password MySQL密码
     * @param database MySQL数据库名
     * @param configFile MySQL配置文件路径
     * @param charset MySQL字符集
     * @param port MySQL端口号
     */
    qMySQLDB(const std::string &host = "localhost",
             const std::string &user = "root",
             const std::string &password = "",
             const std::string &database = "BlackBox",
             const std::string &configFile = "/etc/mysql/mysql.conf.d/mysqld.cnf",
             const std::string &charset = "utf8mb4",
             unsigned int port = 3306);

    /**
     * @brief 析构函数
     */
    ~qMySQLDB() = default;

    // 允许拷贝和移动
    qMySQLDB(const qMySQLDB &) = default;
    qMySQLDB &operator=(const qMySQLDB &) = default;
    qMySQLDB(qMySQLDB &&) noexcept = default;
    qMySQLDB &operator=(qMySQLDB &&) noexcept = default;

    /**
     * @brief 为设备创建独立的数据表
     *
     * @param dbAddr 设备的数据库地址
     * @param isTemp 是否为临时设备
     */
    void createDeviceTable(uint32_t dbAddr, const std::string &monthStr, bool isTemp = false);

    /**
     * @brief 删除设备的数据表
     *
     * @param dbAddr 设备的数据库地址
     * @param isTemp 是否为临时设备
     */
    void removeDeviceTable(uint32_t dbAddr, bool isTemp = false);

    /**
     * @brief 将临时设备的数据插入到正式设备的表中
     *
     * @param tempDbAddr 临时设备的数据库地址
     * @param formalDbAddr 正式设备的数据库地址
     */
    void insertTempData2Formal(uint32_t tempDbAddr, uint32_t formalDbAddr);

    /**
     * @brief 重命名设备的数据表
     *
     * @param oldDbAddr 旧设备的数据库地址
     * @param newDbAddr 新设备的数据库地址
     */
    void renameDeviceTables(uint32_t oldDbAddr, uint32_t newDbAddr, bool isTemp);

    /**
     * @brief 将指定设备的所有临时表转换为正式表
     *
     * @param dbAddr 设备的数据库地址
     */
    void Temp2FormalTable(uint32_t dbAddr);

    /**
     * @brief 向设备的数据表中插入数据 (注：仅适用单线程，localtime 非线程安全 )
     *
     * @param values 插入的数据列名和值
     * @param blobFieldName BLOB字段名
     * @param blobData BLOB数据
     * @param isTemp 是否为临时设备
     *
     * @note 仅适用单线程，localtime 非线程安全
     */
    void insertDeviceDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                         const std::vector<uint8_t> &dataBytes,
                                         bool isTemp);

    /**
     * @brief 向设备的数据表中插入数据 (注：仅适用单线程，localtime 非线程安全 )
     *
     * @param dbAddr 设备的数据库地址
     * @param values 插入的数据列名和值
     * @param blobFieldName BLOB字段名
     * @param blobData BLOB数据
     * @param isTemp 是否为临时设备
     *
     * @note 仅适用单线程，localtime 非线程安全
     */
    void insertDeviceData(uint32_t dbAddr,
                          const std::vector<std::pair<std::string, int64_t>> &values,
                          const std::string &blobFieldName,
                          const std::vector<uint8_t> &blobData,
                          bool isTemp = false);

    /**
     * @brief 创建设备信息
     *
     * @param id 设备的ID
     * @param dbAddr 设备的数据库地址
     * @param currentSn 设备的当前序列号
     */
    void creatDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn);

    /**
     * @brief 更新设备信息
     *
     * @param id 设备的ID
     * @param dbAddr 设备的数据库地址
     * @param currentSn 设备的当前序列号
     */
    void updateDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn);

    /**
     * @brief 插入系统序列号到设备信息表
     *
     * @param id 设备的ID
     * @param currentSn 设备的当前序列号
     */
    void insertSysSn2DevInfo(int id, const std::string &currentSn);

    /**
     * @brief 删除过期的设备数据表 (注：仅适用单线程，localtime 非线程安全)
     *
     * @param keepMonths 保留的月份数，例如24表示保留最近24个月的数据表
     *
     * @note 仅适用单线程，localtime 非线程安全
     */
    void removeExpiredDeviceTables(int keepMonths = 24);

    /**
     * @brief 查询设备在指定时间范围内的数据  (注：仅适用单线程，localtime 非线程安全)
     *
     * @param dbAddr 设备的数据库地址
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @return 数据记录的向量
     *
     * @note 仅适用单线程，localtime 非线程安全
     */
    std::vector<DataRecord> queryDeviceData(uint32_t dbAddr, int64_t startTime, int64_t endTime);

    /**
     * @brief 创建电表数据分月表
     *
     * @param monthStr 月份字符串，格式为YYYYMM
     */
    void createMeterTable(const std::string &monthStr);

    /**
     * @brief 删除电表数据分月表
     *
     * @param monthStr 月份字符串，格式为YYYYMM
     */
    void removeMeterTable(const std::string &monthStr);

    /**
     * @brief 向电表数据表中插入数据 (注：仅适用单线程，localtime 非线程安全)
     *
     * @param values 插入的数据列名和值
     * @param blobFieldName BLOB字段名
     * @param blobData BLOB数据
     */
    void insertMeterDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                        const std::vector<uint8_t> &dataBytes);

    /**
     * @brief 查询电表在指定时间范围内的数据
     *
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 查询结果记录集
     */
    std::vector<DataRecord> queryMeterData(int64_t startTime, int64_t endTime);

    inline const std::string getDeviceInfoTable() const {
        return m_deviceInfoTableName;
    };

    inline const std::string getOfflineDeviceTable() const {
        return m_offlineDeviceTableName;
    };

  private:
    /**
     * @brief 向表中插入数据。
     *
     * @param tableName 表名。
     * @param values 要更新的列-值对向量。
     * @param blobFieldName data 数据列名。
     * @param blobData data 数据 。
     */
    void insertWithBinaryData(const std::string &tableName,
                              const std::vector<std::pair<std::string, int64_t>> &values,
                              const std::string &blobFieldName,
                              const std::vector<uint8_t> &blobData);

    /**
     * @brief 辅助函数，用于重命名表
     *
     * @param oldDbAddr 旧设备的数据库地址
     * @param newDbAddr 新设备的数据库地址
     */
    void renameTable(const std::string &oldTableName, const std::string &newTableName);

    /**
     * @brief 获取设备表名，增加月份参数
     *
     * @param dbAddr 设备的数据库地址
     * @param monthStr 月份字符串，格式为"YYYYMM"
     * @param isTemp 是否为临时设备
     * @return 表名
     */
    std::string getDeviceTableName(uint32_t dbAddr, const std::string &monthStr, bool isTemp);

    /**
     * @brief 获取设备表前缀
     *
     * @param dbAddr 设备的数据库地址
     * @param isTemp 是否为临时设备
     * @return 表前缀
     */
    std::string getDeviceTablePrefix(uint32_t dbAddr, bool isTemp);

    /**
     * @brief 获取设备在指定时间范围内的所有表
     *
     * @param dbAddr 设备的数据库地址
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @return 表名列表
     */
    std::vector<std::string> getDeviceTablesInRange(uint32_t dbAddr, int64_t startTime, int64_t endTime);

    /**
     * @brief 如果表不存在则创建
     *
     * @param tableName 表名
     */
    void createDeviceTableIfNotExists(const std::string &tableName);

    std::vector<DataRecord> queryDataRecords(const std::string &sql);

    /**
     * @brief 获取电表数据表名
     *
     * @param monthStr 月份字符串，格式为YYYYMM
     * @return 表名
     */
    std::string getMeterTableName(const std::string &monthStr);

    /**
     * @brief 获取电表表前缀
     *
     * @return 表前缀
     */
    std::string getMeterTablePrefix();

    /**
     * @brief 确保电表数据表存在
     *
     * @param tableName 表名
     */
    void createMeterTableIfNotExists(const std::string &tableName);

    /**
     * @brief 获取电表在指定时间范围内的所有表
     *
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @return 表名列表
     */
    std::vector<std::string> getMeterTablesInRange(int64_t startTime, int64_t endTime);

    std::string m_deviceInfoTableName;    // 设备信息表名
    std::string m_offlineDeviceTableName; // 离线设备表名
    std::mutex m_deviceTablesMutex;       // 设备表互斥锁
};

#endif // QMYSQL_DB_H