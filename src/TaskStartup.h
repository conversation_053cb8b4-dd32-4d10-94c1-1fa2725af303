#ifndef TASK_STARTUP_H
#define TASK_STARTUP_H

#include "RtuTask.h"
#include "TaskManager.h"
#include "TcpTask.h"
#include "UdpTask.h"
#include <yaml-cpp/yaml.h>

class TaskStartup {
  private:
    TaskManager *m_manager = nullptr;
    UdpTask *m_udpTask = nullptr;
    TcpTask *m_tcpTask = nullptr;
    RtuTask *m_rtuTask = nullptr;
    std::vector<TaskConfig> m_configLists;

  public:
    TaskStartup();
    ~TaskStartup();

    void Start();
};

#endif // TASK_STARTUP_H
