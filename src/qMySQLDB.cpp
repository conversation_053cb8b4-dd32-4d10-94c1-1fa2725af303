#include "qMySQLDB.h"
#include "CompressionUtils.h"
#include "ConfigManager.h"
#include "DatabaseMonitor.h"
#include "ByteOrderUtils.h"
#include "Logger.h"
#include <iomanip>
#include <iostream>
#include <regex>
#include <ctime>

qMySQLDB::qMySQLDB(const std::string &host,
                   const std::string &user,
                   const std::string &password,
                   const std::string &database,
                   const std::string &configFile,
                   const std::string &charset,
                   unsigned int port)
    : IMySQLDB(host, user, password, database, configFile, charset, port) {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    m_deviceInfoTableName = config["deviceInfoTableName"].as<std::string>("device_info");
    m_offlineDeviceTableName = config["offlineDeviceTableName"].as<std::string>("offline_device");

    // 创建设备信息表
    executeWithLock("CREATE TABLE IF NOT EXISTS " + m_deviceInfoTableName +
                    "("
                    "id INTEGER PRIMARY KEY, "
                    "db_addr INTEGER, "
                    "current_sn TEXT, "
                    "last_sn TEXT);");

    // 创建离线设备表
    executeWithLock("CREATE TABLE IF NOT EXISTS " + m_offlineDeviceTableName +
                    "("
                    "db_addr INTEGER PRIMARY KEY );");

    // // 创建电表数据表
    // executeWithLock("CREATE TABLE IF NOT EXISTS " + m_meterDataTableName +
    //                 "("
    //                 "timestamp TEXT, "
    //                 "register_type TEXT, "
    //                 "start_address INTEGER, "
    //                 "length INTEGER, "
    //                 "data TEXT, "
    //                 "PRIMARY KEY (timestamp));");
}

std::string qMySQLDB::getDeviceTableName(uint32_t dbAddr, const std::string &monthStr, bool isTemp) {
    std::string prefix = isTemp ? "temp_device_" : "device_";
    return prefix + std::to_string(dbAddr) + "_" + monthStr;
}

// 创建设备数据表，按月份创建
void qMySQLDB::createDeviceTable(uint32_t dbAddr, const std::string &monthStr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    std::string tableName = getDeviceTableName(dbAddr, monthStr, isTemp);
    createDeviceTableIfNotExists(tableName);
}

void qMySQLDB::removeDeviceTable(uint32_t dbAddr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取所有表
    std::vector<std::vector<std::string>> tables = query("SHOW TABLES");
    m_lastExecutedSql = "SHOW TABLES";

    // 构建表名前缀
    std::string prefix = getDeviceTablePrefix(dbAddr, isTemp);

    // 删除匹配的表
    for (const auto &row : tables) {
        if (!row.empty() && row[0].find(prefix) == 0) {
            std::string dropQuery = "DROP TABLE IF EXISTS " + row[0];
            executeWithLock(dropQuery);
            m_lastExecutedSql = dropQuery;
        }
    }
}

void qMySQLDB::insertTempData2Formal(uint32_t tempDbAddr, uint32_t formalDbAddr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取所有表
    std::vector<std::vector<std::string>> tables = query("SHOW TABLES");
    m_lastExecutedSql = "SHOW TABLES";

    // 构建临时表名前缀
    std::string tempPrefix = getDeviceTablePrefix(tempDbAddr, true);

    // 遍历所有表
    for (const auto &row : tables) {
        if (!row.empty() && row[0].find(tempPrefix) == 0) {
            // 提取月份部分
            std::string monthPart = row[0].substr(tempPrefix.length());

            // 构建正式表名
            std::string formalTableName = getDeviceTablePrefix(formalDbAddr, false) + monthPart;

            // 确保正式表存在
            createDeviceTableIfNotExists(formalTableName);

            // 将临时表数据插入到正式表
            std::string insertQuery = "INSERT IGNORE INTO " + formalTableName + " SELECT * FROM " + row[0];
            executeWithLock(insertQuery);
            m_lastExecutedSql = insertQuery;
        }
    }
}

void qMySQLDB::renameDeviceTables(uint32_t oldDbAddr, uint32_t newDbAddr, bool isTemp) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取所有表
    std::vector<std::vector<std::string>> tables = query("SHOW TABLES");
    m_lastExecutedSql = "SHOW TABLES";

    // 构建旧表名前缀
    std::string oldPrefix = getDeviceTablePrefix(oldDbAddr, isTemp);

    // 遍历所有表
    for (const auto &row : tables) {
        if (!row.empty() && row[0].find(oldPrefix) == 0) {
            // 提取月份部分
            std::string monthPart = row[0].substr(oldPrefix.length());

            // 构建新表名
            std::string newTableName = getDeviceTablePrefix(newDbAddr, isTemp) + monthPart;

            // 重命名表
            renameTable(row[0], newTableName);
        }
    }
}

void qMySQLDB::Temp2FormalTable(uint32_t dbAddr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取所有表
    std::vector<std::vector<std::string>> tables = query("SHOW TABLES");
    m_lastExecutedSql = "SHOW TABLES";

    // 构建临时表名前缀
    std::string tempPrefix = getDeviceTablePrefix(dbAddr, true);

    // 遍历所有表
    for (const auto &row : tables) {
        if (!row.empty() && row[0].find(tempPrefix) == 0) {
            // 提取月份部分
            std::string monthPart = row[0].substr(tempPrefix.length());

            // 构建正式表名
            std::string formalTableName = getDeviceTablePrefix(dbAddr, false) + monthPart;

            // 重命名表
            renameTable(row[0], formalTableName);
        }
    }
}

void qMySQLDB::insertDeviceDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                               const std::vector<uint8_t> &dataBytes,
                                               bool isTemp) {
    // 获取当前时间
    time_t now = time(nullptr);
    struct tm *timeinfo = localtime(&now);

    // 格式化月份字符串
    char monthStr[32];
    strftime(monthStr, sizeof(monthStr), "%Y%m", timeinfo);

    // 从values中提取db_addr
    uint32_t dbAddr = 0;
    for (const auto &pair : values) {
        if (pair.first == "db_addr") {
            dbAddr = static_cast<uint32_t>(pair.second);
            break;
        }
    }

    if (dbAddr == 0) {
        LOG_ERROR << "Error: db_addr not found in values";
        return;
    }

    // 处理字节序 - 确保数据是大端序
    std::vector<uint8_t> dataBytesInBigEndian;

    // 检查数据大小是否为偶数（每个寄存器2字节）
    if (dataBytes.size() % 2 == 0) {
        // 获取字节序转换工具实例
        ByteOrderUtils &byteOrderUtils = ByteOrderUtils::getInstance();

        // 将原始字节数据视为uint16_t数组，并转换为大端序
        size_t registerCount = dataBytes.size() / 2;
        std::vector<uint16_t> registers(registerCount);

        // 从字节数组构建寄存器值（按照当前系统字节序）
        for (size_t i = 0; i < registerCount; i++) {
            registers[i] = (static_cast<uint16_t>(dataBytes[i * 2]) << 8) | static_cast<uint16_t>(dataBytes[i * 2 + 1]);
        }

        // 转换为大端序字节数组
        dataBytesInBigEndian = byteOrderUtils.registersToBytesBigEndian(registers.data(), registerCount);
    } else {
        // 如果不是偶数，直接使用原始数据
        dataBytesInBigEndian = dataBytes;
        LOG_WARNING << "Warning: dataBytes size is not even, skipping byte order conversion";
    }

    // 压缩数据（使用转换后的大端序数据）
    CompressionUtils &compressTool = CompressionUtils::getInstance();
    std::vector<uint8_t> compressedData = compressTool.compressData(dataBytesInBigEndian);

    // 获取表名
    std::string tableName = getDeviceTableName(dbAddr, monthStr, isTemp);

    // 确保表存在
    createDeviceTableIfNotExists(tableName);

    // 插入数据
    insertWithBinaryData(tableName, values, "data", compressedData);
}

void qMySQLDB::insertDeviceData(uint32_t dbAddr,
                                const std::vector<std::pair<std::string, int64_t>> &values,
                                const std::string &blobFieldName,
                                const std::vector<uint8_t> &blobData,
                                bool isTemp) {
    // 获取当前时间
    time_t now = time(nullptr);
    struct tm *timeinfo = localtime(&now);

    // 格式化月份字符串
    char monthStr[32];
    strftime(monthStr, sizeof(monthStr), "%Y%m", timeinfo);

    // 处理字节序 - 确保数据是大端序
    std::vector<uint8_t> blobDataInBigEndian;

    // 检查数据大小是否为偶数（每个寄存器2字节）
    if (blobData.size() % 2 == 0) {
        // 获取字节序转换工具实例
        ByteOrderUtils &byteOrderUtils = ByteOrderUtils::getInstance();

        // 将原始字节数据视为uint16_t数组，并转换为大端序
        size_t registerCount = blobData.size() / 2;
        std::vector<uint16_t> registers(registerCount);

        // 从字节数组构建寄存器值（按照当前系统字节序）
        for (size_t i = 0; i < registerCount; i++) {
            registers[i] = (static_cast<uint16_t>(blobData[i * 2]) << 8) | static_cast<uint16_t>(blobData[i * 2 + 1]);
        }

        // 转换为大端序字节数组
        blobDataInBigEndian = byteOrderUtils.registersToBytesBigEndian(registers.data(), registerCount);
    } else {
        // 如果不是偶数，直接使用原始数据
        blobDataInBigEndian = blobData;
        LOG_WARNING << "Warning: blobData size is not even, skipping byte order conversion";
    }

    // 获取表名
    std::string tableName = getDeviceTableName(dbAddr, monthStr, isTemp);

    // 确保表存在
    createDeviceTableIfNotExists(tableName);

    // 插入数据
    insertWithBinaryData(tableName, values, blobFieldName, blobDataInBigEndian);
}

void qMySQLDB::creatDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn) {
    std::string query = "INSERT INTO " + m_deviceInfoTableName + " (id, db_addr, current_sn) VALUES (" +
                        std::to_string(id) + ", " + std::to_string(dbAddr) + ", '" + escapeString(currentSn) + "')";

    executeWithLock(query);
    m_lastExecutedSql = query;
}

void qMySQLDB::updateDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn) {
    // 先查询当前的SN
    std::string query = "SELECT current_sn FROM " + m_deviceInfoTableName + " WHERE id = " + std::to_string(id);
    m_lastExecutedSql = query;

    std::vector<std::vector<std::string>> result = this->query(query);

    if (!result.empty() && !result[0].empty()) {
        std::string lastSn = result[0][0]; // 将准备替换的 Gmax 的 SN 号替换为上一个 Gmax 的 SN 号

        // 更新设备信息
        query = "UPDATE " + m_deviceInfoTableName + " SET db_addr = " + std::to_string(dbAddr) + ", last_sn = '" +
                escapeString(lastSn) + "', current_sn = '" + escapeString(currentSn) + "' WHERE id = " + std::to_string(id);
    } else {
        // 如果没有记录，则创建
        query = "INSERT INTO " + m_deviceInfoTableName + " (id, db_addr, current_sn) VALUES (" + std::to_string(id) +
                ", " + std::to_string(dbAddr) + ", '" + escapeString(currentSn) + "')";
    }

    executeWithLock(query);
    m_lastExecutedSql = query;
}

void qMySQLDB::insertSysSn2DevInfo(int id, const std::string &currentSn) {
    std::string query = "UPDATE " + m_deviceInfoTableName + " SET current_sn = '" + escapeString(currentSn) +
                        "' WHERE id = " + std::to_string(id);

    executeWithLock(query);
    m_lastExecutedSql = query;
}

void qMySQLDB::removeExpiredDeviceTables(int keepMonths) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);

    // 获取当前时间
    time_t now = time(nullptr);
    struct tm *timeinfo = localtime(&now);

    // 计算保留的最早月份
    int year = timeinfo->tm_year + 1900;
    int month = timeinfo->tm_mon + 1;

    // 回退keepMonths个月
    month -= keepMonths;
    while (month <= 0) {
        month += 12;
        year--;
    }

    // 格式化最早月份字符串
    char earliestMonthStr[32];
    sprintf(earliestMonthStr, "%04d%02d", year, month);

    // 获取所有表
    std::vector<std::vector<std::string>> tables = query("SHOW TABLES");
    m_lastExecutedSql = "SHOW TABLES";

    // 正则表达式匹配设备表和电表表
    std::regex deviceTableRegex("(device|temp_device)_\\d+_(\\d{6})");
    std::regex meterTableRegex("meter_(\\d{6})");

    // 遍历所有表
    for (const auto &row : tables) {
        if (!row.empty()) {
            std::smatch deviceMatches;
            std::smatch meterMatches;

            // 检查是否为设备表
            if (std::regex_search(row[0], deviceMatches, deviceTableRegex) && deviceMatches.size() > 2) {
                std::string monthStr = deviceMatches[2].str();

                // 如果表的月份早于最早保留月份，则删除
                if (monthStr.compare(earliestMonthStr) < 0) {
                    std::string dropQuery = "DROP TABLE IF EXISTS " + row[0];
                    executeWithLock(dropQuery);
                    m_lastExecutedSql = dropQuery;
                }
            }
            // 检查是否为电表表
            else if (std::regex_search(row[0], meterMatches, meterTableRegex) && meterMatches.size() > 1) {
                std::string monthStr = meterMatches[1].str();

                // 如果表的月份早于最早保留月份，则删除
                if (monthStr.compare(earliestMonthStr) < 0) {
                    std::string dropQuery = "DROP TABLE IF EXISTS " + row[0];
                    executeWithLock(dropQuery);
                    m_lastExecutedSql = dropQuery;
                }
            }
        }
    }
}

std::vector<DataRecord> qMySQLDB::queryDeviceData(uint32_t dbAddr, int64_t startTime, int64_t endTime) {
    // 获取时间范围内的表名
    std::vector<std::string> tableNames = getDeviceTablesInRange(dbAddr, startTime, endTime);

    // 构建查询SQL
    std::string sql;
    for (size_t i = 0; i < tableNames.size(); ++i) {
        if (i > 0) {
            sql += " UNION ALL "; // 合并多个表的查询结果
        }

        sql += "SELECT timestamp, db_addr, register_type, start_address, length, data FROM " + tableNames[i] +
               " WHERE timestamp >= " + std::to_string(startTime) + " AND timestamp <= " + std::to_string(endTime);
    }

    // 如果没有表，返回空结果
    if (sql.empty()) {
        return {};
    }

    // 按时间戳升序排序
    sql += " ORDER BY timestamp ASC";

    // 查询数据
    return queryDataRecords(sql);
}

std::string qMySQLDB::getDeviceTablePrefix(uint32_t dbAddr, bool isTemp) {
    std::string prefix = isTemp ? "temp_device_" : "device_";
    return prefix + std::to_string(dbAddr) + "_";
}

void qMySQLDB::createDeviceTableIfNotExists(const std::string &tableName) {
    std::string query = "CREATE TABLE IF NOT EXISTS " + tableName +
                        " ("
                        "timestamp BIGINT, "
                        "db_addr INT, "
                        "register_type INT, "
                        "start_address INT, "
                        "length INT, "
                        "data BLOB, "
                        "PRIMARY KEY (timestamp, db_addr), "
                        "INDEX (timestamp, start_address, register_type)"
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    executeWithLock(query);
    m_lastExecutedSql = query;
}

void qMySQLDB::createMeterTableIfNotExists(const std::string &tableName) {
    std::string createTableSql = "CREATE TABLE IF NOT EXISTS " + tableName +
                                 " ("
                                 "id INT AUTO_INCREMENT, "
                                 "timestamp BIGINT, "
                                 "register_type TINYINT UNSIGNED, "
                                 "start_address SMALLINT UNSIGNED, "
                                 "length SMALLINT UNSIGNED, "
                                 "data MEDIUMBLOB, "
                                 "PRIMARY KEY (id), "
                                 "INDEX (timestamp, start_address, register_type)"
                                 ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    executeWithLock(createTableSql);
    m_lastExecutedSql = createTableSql;
}

std::vector<std::string> qMySQLDB::getDeviceTablesInRange(uint32_t dbAddr, int64_t startTime, int64_t endTime) {
    std::vector<std::string> result;

    // 获取开始时间的月份
    time_t startTimeT = startTime / 1000; // 转换为秒
    struct tm *startTimeInfo = localtime(&startTimeT);

    // 获取结束时间的月份
    time_t endTimeT = endTime / 1000; // 转换为秒
    struct tm *endTimeInfo = localtime(&endTimeT);

    // 计算开始年月和结束年月
    int startYear = startTimeInfo->tm_year + 1900;
    int startMonth = startTimeInfo->tm_mon + 1;

    int endYear = endTimeInfo->tm_year + 1900;
    int endMonth = endTimeInfo->tm_mon + 1;

    // 遍历所有可能的月份
    int year = startYear;
    int month = startMonth;

    while (year < endYear || (year == endYear && month <= endMonth)) {
        // 使用 ostringstream 格式化月份字符串
        std::ostringstream oss;
        oss << std::setw(4) << std::setfill('0') << year   // std::setw 输出的字段宽度为 n 个字符。
            << std::setw(2) << std::setfill('0') << month; // std::setfill 用于设置填充字符。
        std::string monthStr = oss.str();

        // 获取表名
        std::string tableName = getDeviceTableName(dbAddr, monthStr, false);

        // 检查表是否存在
        std::string checkQuery =
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '" + tableName + "'";
        std::vector<std::vector<std::string>> tableExists = query(checkQuery);
        m_lastExecutedSql = checkQuery;

        if (!tableExists.empty() && !tableExists[0].empty() && tableExists[0][0] != "0") {
            result.push_back(tableName);
        }

        // 移动到下一个月
        month++;
        if (month > 12) {
            month = 1;
            year++;
        }
    }

    return result;
}

void qMySQLDB::insertWithBinaryData(const std::string &tableName,
                                    const std::vector<std::pair<std::string, int64_t>> &values,
                                    const std::string &blobFieldName,
                                    const std::vector<uint8_t> &blobData) {
    // 构建SQL语句
    std::string query = "INSERT INTO " + tableName + " (";
    std::string placeholders = " VALUES (";

    // 添加普通字段
    for (const auto &pair : values) {
        query += pair.first + ",";
        placeholders += std::to_string(pair.second) + ",";
    }

    // 添加BLOB字段
    query += blobFieldName + ")";
    placeholders += "?)";

    // 合并SQL
    query += placeholders;

    // 使用 DatabaseAccess 执行操作，确保被监控记录
    // 由于需要处理BLOB数据，我们仍然需要直接使用MySQL API
    std::lock_guard<std::mutex> lock(m_db_mutex);

    // 记录开始时间，用于性能监控
    auto start = std::chrono::high_resolution_clock::now();

    // 准备语句
    MYSQL_STMT *stmt = mysql_stmt_init(m_mysql);
    if (!stmt) {
        throw std::runtime_error("mysql_stmt_init() failed");
    }

    try {
        // 准备SQL
        if (mysql_stmt_prepare(stmt, query.c_str(), query.length()) != 0) {
            std::string error = mysql_stmt_error(stmt);
            throw std::runtime_error("mysql_stmt_prepare() failed: " + error);
        }

        // 绑定参数
        MYSQL_BIND bind[1];
        memset(bind, 0, sizeof(bind));

        // 绑定BLOB数据
        bind[0].buffer_type = MYSQL_TYPE_BLOB;
        bind[0].buffer = (void *)blobData.data();
        bind[0].buffer_length = blobData.size();
        bind[0].length = new unsigned long(blobData.size());

        // 设置绑定
        if (mysql_stmt_bind_param(stmt, bind) != 0) {
            std::string error = mysql_stmt_error(stmt);
            delete bind[0].length;
            throw std::runtime_error("mysql_stmt_bind_param() failed: " + error);
        }

        // 执行语句
        if (mysql_stmt_execute(stmt) != 0) {
            std::string error = mysql_stmt_error(stmt);
            delete bind[0].length;
            throw std::runtime_error("mysql_stmt_execute() failed: " + error);
        }

        // 清理
        delete bind[0].length;
        mysql_stmt_close(stmt);

        // 记录结束时间
        auto end = std::chrono::high_resolution_clock::now();

        // 计算执行时间（毫秒）
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();

        // 记录查询执行时间到监控系统
        DatabaseMonitor::getInstance().recordQueryTime(query, duration);

        // 记录最后执行的SQL
        m_lastExecutedSql = query;
    } catch (const std::exception &e) {
        mysql_stmt_close(stmt);
        throw;
    }
}

void qMySQLDB::renameTable(const std::string &oldTableName, const std::string &newTableName) {
    std::string query = "RENAME TABLE " + oldTableName + " TO " + newTableName;
    executeWithLock(query);
    m_lastExecutedSql = query;
}

std::vector<DataRecord> qMySQLDB::queryDataRecords(const std::string &sql) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::vector<DataRecord> records;

    // 记录开始时间，用于性能监控
    auto start = std::chrono::high_resolution_clock::now();

    try {
        // 执行查询
        if (mysql_query(m_mysql, sql.c_str()) != 0) {
            std::string error = mysql_error(m_mysql);
            throw std::runtime_error("SQL 错误: " + error);
        }

        // 获取结果集
        MYSQL_RES *res = mysql_store_result(m_mysql);
        if (!res) {
            std::string error = mysql_error(m_mysql);
            throw std::runtime_error("无法获取查询结果: " + error);
        }

        // 获取字段数
        int num_fields = mysql_num_fields(res);
        if (num_fields < 6) {
            mysql_free_result(res);
            throw std::runtime_error("查询结果字段数不足");
        }

        // 获取行数据
        MYSQL_ROW row;
        while ((row = mysql_fetch_row(res))) {
            DataRecord record;

            // 解析基本字段
            record.timestamp = std::stoll(row[0] ? row[0] : "0");
            record.dbAddr = std::stoul(row[1] ? row[1] : "0");
            record.registerType = std::stoi(row[2] ? row[2] : "0");
            record.startAddress = std::stoi(row[3] ? row[3] : "0");
            record.length = std::stoi(row[4] ? row[4] : "0"); // 寄存器个数

            // 获取BLOB数据长度
            unsigned long *lengths = mysql_fetch_lengths(res);
            if (lengths && row[5]) {
                // 解析BLOB数据
                record.data.resize(lengths[5]);
                memcpy(record.data.data(), row[5], lengths[5]);

                // 尝试解压数据
                try {
                    // 获取CompressionUtils实例并调用decompressData方法
                    // 注意：这里需要提供blobSize和expectedUncompressedSize参数
                    // 假设压缩数据大小就是record.data.size()，解压后大小是record.length * 2
                    record.data = CompressionUtils::getInstance().decompressData(record.data, record.data.size(), record.length * 2);
                } catch (const std::exception &e) {
                    LOG_ERROR << "解压数据失败: " << e.what();
                    // 如果解压失败，返回全0数据
                    record.data.clear();
                    record.data.resize(record.length * 2, 0); // 每个寄存器2字节
                }
            } else {
                // 如果没有数据，返回全0数据
                record.data.resize(record.length * 2, 0); // 每个寄存器2字节
            }

            records.push_back(record);
        }

        // 释放结果集
        mysql_free_result(res);

        // 记录结束时间
        auto end = std::chrono::high_resolution_clock::now();

        // 计算执行时间（毫秒）
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();

        // 记录查询执行时间到监控系统
        DatabaseMonitor::getInstance().recordQueryTime(sql, duration);

        // 记录最后执行的SQL
        m_lastExecutedSql = sql;
    } catch (const std::exception &e) {
        throw;
    }

    return records;
}

std::string qMySQLDB::getMeterTableName(const std::string &monthStr) {
    std::string prefix = getMeterTablePrefix();
    return prefix + monthStr;
}

std::string qMySQLDB::getMeterTablePrefix() {
    return "meter_";
}

void qMySQLDB::createMeterTable(const std::string &monthStr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);
    std::string tableName = getMeterTableName(monthStr);
    createMeterTableIfNotExists(tableName);
}

void qMySQLDB::removeMeterTable(const std::string &monthStr) {
    std::lock_guard<std::mutex> lock(m_deviceTablesMutex);
    std::string tableName = getMeterTableName(monthStr);
    std::string dropQuery = "DROP TABLE IF EXISTS " + tableName;
    executeWithLock(dropQuery);
    m_lastExecutedSql = dropQuery;
}

void qMySQLDB::insertMeterDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                              const std::vector<uint8_t> &dataBytes) {
    // 获取当前时间
    time_t now = time(nullptr);
    struct tm *timeinfo = localtime(&now);

    // 格式化月份字符串
    char monthStr[32];
    strftime(monthStr, sizeof(monthStr), "%Y%m", timeinfo);

    // 处理字节序 - 确保数据是大端序
    std::vector<uint8_t> dataBytesInBigEndian;

    // 检查数据大小是否为偶数（每个寄存器2字节）
    if (dataBytes.size() % 2 == 0) {
        // 获取字节序转换工具实例
        ByteOrderUtils &byteOrderUtils = ByteOrderUtils::getInstance();

        // 将原始字节数据视为uint16_t数组，并转换为大端序
        size_t registerCount = dataBytes.size() / 2;
        std::vector<uint16_t> registers(registerCount);

        // 从字节数组构建寄存器值（按照当前系统字节序）
        for (size_t i = 0; i < registerCount; i++) {
            registers[i] = (static_cast<uint16_t>(dataBytes[i * 2]) << 8) | static_cast<uint16_t>(dataBytes[i * 2 + 1]);
        }

        // 转换为大端序字节数组
        dataBytesInBigEndian = byteOrderUtils.registersToBytesBigEndian(registers.data(), registerCount);
    } else {
        // 如果不是偶数，直接使用原始数据
        dataBytesInBigEndian = dataBytes;
        LOG_WARNING << "Warning: dataBytes size is not even, skipping byte order conversion";
    }

    // 压缩数据（使用转换后的大端序数据）
    CompressionUtils &compressTool = CompressionUtils::getInstance();
    std::vector<uint8_t> compressedData = compressTool.compressData(dataBytesInBigEndian);

    // 获取表名
    std::string tableName = getMeterTableName(monthStr);

    // 确保表存在
    createMeterTableIfNotExists(tableName);

    // 插入数据
    insertWithBinaryData(tableName, values, "data", compressedData);
}

std::vector<std::string> qMySQLDB::getMeterTablesInRange(int64_t startTime, int64_t endTime) {
    std::vector<std::string> result;

    // 获取开始时间的月份
    time_t startTimeT = startTime / 1000; // 转换为秒
    struct tm *startTimeInfo = localtime(&startTimeT);

    // 获取结束时间的月份
    time_t endTimeT = endTime / 1000; // 转换为秒
    struct tm *endTimeInfo = localtime(&endTimeT);

    // 计算开始年月和结束年月
    int startYear = startTimeInfo->tm_year + 1900;
    int startMonth = startTimeInfo->tm_mon + 1;

    int endYear = endTimeInfo->tm_year + 1900;
    int endMonth = endTimeInfo->tm_mon + 1;

    // 遍历所有可能的月份
    int year = startYear;
    int month = startMonth;

    while (year < endYear || (year == endYear && month <= endMonth)) {
        // 使用 ostringstream 格式化月份字符串
        std::ostringstream oss;
        oss << std::setw(4) << std::setfill('0') << year   // std::setw 输出的字段宽度为 n 个字符。
            << std::setw(2) << std::setfill('0') << month; // std::setfill 用于设置填充字符。
        std::string monthStr = oss.str();

        // 获取表名
        std::string tableName = getMeterTableName(monthStr);

        // 检查表是否存在
        std::string checkQuery =
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '" + tableName + "'";
        std::vector<std::vector<std::string>> tableExists = query(checkQuery);
        m_lastExecutedSql = checkQuery;

        if (!tableExists.empty() && !tableExists[0].empty() && tableExists[0][0] != "0") {
            result.push_back(tableName);
        }

        // 移动到下一个月
        month++;
        if (month > 12) {
            month = 1;
            year++;
        }
    }

    return result;
}

std::vector<DataRecord> qMySQLDB::queryMeterData(int64_t startTime, int64_t endTime) {
    // 获取时间范围内的表名
    std::vector<std::string> tableNames = getMeterTablesInRange(startTime, endTime);

    // 构建查询SQL
    std::string sql;
    for (size_t i = 0; i < tableNames.size(); ++i) {
        if (i > 0) {
            sql += " UNION ALL "; // 合并多个表的查询结果
        }

        sql += "SELECT timestamp, 0 as db_addr, register_type, start_address, length, data FROM " + tableNames[i] +
               " WHERE timestamp >= " + std::to_string(startTime) + " AND timestamp <= " + std::to_string(endTime);
    }

    // 如果没有表，返回空结果
    if (sql.empty()) {
        return {};
    }

    // 按时间戳升序排序
    sql += " ORDER BY timestamp ASC";

    // 查询数据
    return queryDataRecords(sql);
}
