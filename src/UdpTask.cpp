#include "UdpTask.h"
#include "UdpClientDataHandler.h"
#include "Logger.h"

UdpTask::UdpTask(int port) : m_port(port) {
    m_udpReceiver = new (std::nothrow) UdpBroadcastReceiver(port);
    if (!m_udpReceiver) {
        LOG_ERROR << "分配UdpBroadcastReceiver内存失败";
        return;
    }

    m_InitRes = m_udpReceiver->init();
    if (m_InitRes != true) {
        LOG_ERROR << "初始化UDP接收器失败。";
        return;
    }
}

UdpTask::~UdpTask() {
    delete m_udpReceiver;
}

void UdpTask::run() {
    try {
        if (m_InitRes != true) {
            m_InitRes = m_udpReceiver->init();
            return;
        }

        m_udpReceiver->receive();

        auto &handler = UdpClientDataHandler::getInstance();

        for (auto &packet : m_udpReceiver->m_dataPackets) {
            handler.handleData(packet.data.data(), packet.length, packet.source);
        }

        handler.manageMap(); // 管理表的生命周期

        m_udpReceiver->m_dataPackets.clear();
    } catch (const std::exception &e) {
        LOG_ERROR << "发生异常: " << e.what();
    }
}