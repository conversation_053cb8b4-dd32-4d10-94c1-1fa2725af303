#include "UdpClientDataHandler.h"
#include "ConfigManager.h"
#include "DatabaseAdapter.h"
#include "Logger.h"
#include <arpa/inet.h>
#include <cstring>
#include <netinet/in.h>

UdpClientDataHandler &UdpClientDataHandler::getInstance() {
    static UdpClientDataHandler instance;
    return instance;
}

UdpClientDataHandler::UdpClientDataHandler()
    : m_useFixedDevices(false), m_useUdpDiscovery(true), m_reconnectInterval(10) { // 默认10秒尝试重连一次

    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    // 使用数据库适配器
    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();

    m_offlineDeviceTableName = dbAdapter.getOfflineDeviceTable();

    // 读取设备连接配置
    if (config["deviceConnection"]) {
        const YAML::Node &deviceConfig = config["deviceConnection"];

        std::string mode = "";

        // 设置连接模式
        if (deviceConfig["mode"]) {
            mode = deviceConfig["mode"].as<std::string>("udp");
            if (mode == "fixed") {
                m_useFixedDevices = true;
                m_useUdpDiscovery = false;
            } else if (mode == "hybrid") {
                m_useFixedDevices = true;
                m_useUdpDiscovery = true;
            } else if (mode == "udp") {
                m_useFixedDevices = false;
                m_useUdpDiscovery = true;
            } else { // 默认为"udp"模式
                m_useFixedDevices = false;
                m_useUdpDiscovery = true;
            }
        }

        // 读取UDP设置
        if ((mode == "hybrid" || mode == "fixed") && deviceConfig["udpSettings"] && deviceConfig["udpSettings"]["enabled"]) {
            m_useUdpDiscovery = deviceConfig["udpSettings"]["enabled"].as<bool>(true);
        }

        // 读取重连间隔
        if (deviceConfig["reconnectInterval"]) {
            int interval = deviceConfig["reconnectInterval"].as<int>(10);
            m_reconnectInterval = std::chrono::seconds(interval);
        }
    } else {
        // 默认使用UDP发现模式
        m_useFixedDevices = false;
        m_useUdpDiscovery = true;
    }

    // 初始化固定设备（如果启用）
    if (m_useFixedDevices) {
        LOG_WARNING << "初始化固定设备";
        initFixedDevices();
    }

    // 只有在启用UDP发现时才读取离线设备列表
    if (m_useUdpDiscovery) {
        std::string query = "SELECT db_addr FROM " + m_offlineDeviceTableName + ";";
        auto result = dbAdapter.getAccess().query(query);

        for (const auto &row : result) {
            if (row.size() == 1) {
                try {
                    uint32_t dbAddr = static_cast<uint32_t>(std::stoul(row[0]));

                    std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                    m_offlineDevices.insert(dbAddr);
                } catch (const std::exception &e) {
                    LOG_ERROR << "Error parsing db_addr: " << e.what();
                }
            }
        }

        // 读取设备信息表，获取所有已知的 dbAddr
        std::string deviceInfoTableName = dbAdapter.getDeviceInfoTable();
        query = "SELECT db_addr FROM " + deviceInfoTableName + ";";
        result = dbAdapter.getAccess().query(query);

        for (const auto &row : result) {
            if (row.size() == 1) {
                try {
                    uint32_t dbAddr = static_cast<uint32_t>(std::stoul(row[0]));

                    std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                    {
                        std::lock_guard<std::mutex> mapLock(m_mapMutex);
                        // 如果该 dbAddr 不在在线客户端列表和离线设备列表中
                        if (m_energyDataMap.find(dbAddr) == m_energyDataMap.end() &&
                            m_offlineDevices.find(dbAddr) == m_offlineDevices.end()) {
                            // 将其添加到离线设备列表
                            m_offlineDevices.insert(dbAddr);

                            // 插入到离线设备表中
                            dbAdapter.getAccess().insert(m_offlineDeviceTableName, {{"db_addr", std::to_string(dbAddr)}});
                        }
                    }

                } catch (const std::exception &e) {
                    LOG_ERROR << "Error parsing db_addr: " << e.what();
                }
            }
        }
    }
}

void UdpClientDataHandler::initFixedDevices() {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    // 清空固定设备列表
    m_fixedDevices.clear();

    // 如果没有固定设备配置，则使用硬编码的默认值
    if (!config["deviceConnection"] || !config["deviceConnection"]["fixedDevices"]) {
        // 添加3个默认设备
        m_fixedDevices.push_back({"*************", 1, 502});
        m_fixedDevices.push_back({"*************", 2, 502});
        m_fixedDevices.push_back({"*************", 3, 502});

        LOG_INFO << "使用默认的固定设备配置";
    } else {
        // 从配置文件读取固定设备列表
        const YAML::Node &fixedDevices = config["deviceConnection"]["fixedDevices"];
        for (const auto &device : fixedDevices) {
            if (device["ip"] && device["dbAddr"]) {
                std::string ip = device["ip"].as<std::string>();
                uint32_t dbAddr = device["dbAddr"].as<uint32_t>();
                int port = device["port"] ? device["port"].as<int>(502) : 502;

                m_fixedDevices.push_back({ip, dbAddr, port});
                LOG_INFO << "添加固定设备: IP=" << ip << ", dbAddr=" << dbAddr << ", port=" << port;
            }
        }
    }

    // 尝试连接所有固定设备
    for (const auto &device : m_fixedDevices) {
        // 检查是否已经有连接
        if (m_modbusClients.find(device.ip) == m_modbusClients.end()) {
            auto client = std::make_shared<ModbusTcpClient>(device.ip, device.port);
            if (client->connect()) {
                m_modbusClients[device.ip] = std::move(client);

                // 更新 m_energyDataMap
                std::lock_guard<std::mutex> mapLock(m_mapMutex);

                // 创建一个虚拟的能量数据
                EnergyData energyData = {};
                energyData.dbAddress = device.dbAddr;
                energyData.version = 1; // 默认版本

                LOG_WARNING << "存入固定设备";
                m_energyDataMap[device.dbAddr] = {device.ip, energyData, std::chrono::steady_clock::now()};

                // 从离线设备中移除
                {
                    std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                    m_offlineDevices.erase(device.dbAddr);

                    // 从数据库的离线设备表中移除
                    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                    dbAdapter.getAccess().remove(m_offlineDeviceTableName, "db_addr = " + std::to_string(device.dbAddr));
                }

                LOG_INFO << "成功连接到固定设备: " << device.ip;
            } else {
                LOG_ERROR << "无法连接到固定设备: " << device.ip;

                // 添加到离线设备列表
                std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                m_offlineDevices.insert(device.dbAddr);

                // 添加到数据库的离线设备表
                DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                dbAdapter.getAccess().insert(m_offlineDeviceTableName, {{"db_addr", std::to_string(device.dbAddr)}});
            }
        }
    }

    // 记录初始化时间
    m_lastReconnectAttempt = std::chrono::steady_clock::now();
}

void UdpClientDataHandler::checkFixedDevicesConnection() {
    if (!m_useFixedDevices) {
        return; // 不是固定设备模式，直接返回
    }

    auto now = std::chrono::steady_clock::now();
    if (now - m_lastReconnectAttempt < m_reconnectInterval) {
        return; // 未到重连时间
    }

    // 更新重连时间
    m_lastReconnectAttempt = now;

    for (const auto &device : m_fixedDevices) {
        auto clientIt = m_modbusClients.find(device.ip);

        // 检查设备是否已连接，连接是否有效
        if (clientIt == m_modbusClients.end() || !clientIt->second->isConnected()) {
            LOG_WARNING << "尝试重连固定设备: " << device.ip;

            auto client = std::make_shared<ModbusTcpClient>(device.ip, device.port);
            if (client->connect()) {
                m_modbusClients[device.ip] = std::move(client);

                // 更新 m_energyDataMap
                std::lock_guard<std::mutex> mapLock(m_mapMutex);

                // 创建一个虚拟的能量数据
                EnergyData energyData = {};
                energyData.dbAddress = device.dbAddr;
                energyData.version = 1; // 默认版本

                m_energyDataMap[device.dbAddr] = {device.ip, energyData, std::chrono::steady_clock::now()};

                // 从离线设备中移除
                {
                    std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                    m_offlineDevices.erase(device.dbAddr);

                    // 从数据库的离线设备表中移除
                    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                    dbAdapter.getAccess().remove(m_offlineDeviceTableName, "db_addr = " + std::to_string(device.dbAddr));
                }

                LOG_INFO << "成功重连到固定设备: " << device.ip;
            } else {
                LOG_ERROR << "重连固定设备失败: " << device.ip;

                // 连接失败，确保设备在离线列表中
                std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                if (m_offlineDevices.find(device.dbAddr) == m_offlineDevices.end()) {
                    m_offlineDevices.insert(device.dbAddr);

                    // 添加到数据库的离线设备表
                    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                    dbAdapter.getAccess().insert(m_offlineDeviceTableName, {{"db_addr", std::to_string(device.dbAddr)}});
                }
            }
        }
    }
}

void UdpClientDataHandler::handleData(char *data, size_t len, const sockaddr_in &source) {
    // 如果不使用UDP发现，则忽略UDP数据
    if (!m_useUdpDiscovery) {
        return;
    }

    if (len < m_energyDataLen) {
        LOG_WARNING << "接收到意外大小的数据。";
        return;
    }

    EnergyData energyData;
    std::memcpy(&energyData.version, data, sizeof(energyData.version));
    std::memcpy(&energyData.dbAddress, data + 2, sizeof(energyData.dbAddress));
    std::memcpy(&energyData.status, data + 6, sizeof(energyData.status));
    std::memcpy(&energyData.power, data + 8, sizeof(energyData.power));
    std::memcpy(&energyData.soc, data + 10, sizeof(energyData.soc));

    std::string ip = inet_ntoa(source.sin_addr);
    auto now = std::chrono::steady_clock::now(); // 更新插入表的时间

    {
        std::lock_guard<std::mutex> mapLock(m_mapMutex);
        // 更新映射表
        m_energyDataMap[energyData.dbAddress] = {ip, energyData, now};
    }
}

void UdpClientDataHandler::manageMap() {
    // 首先检查固定设备连接状态
    if (m_useFixedDevices) {
        checkFixedDevicesConnection();
    }

    // 如果使用UDP发现，执行原有管理逻辑
    if (m_useUdpDiscovery) {
        {
            // 检查是否需要建立新的Modbus连接
            std::lock_guard<std::mutex> mapLock(m_mapMutex);
            for (const auto &it : m_energyDataMap) {
                const std::string &ip = it.second.ip;
                const uint32_t dbAddr = it.first;

                if (m_modbusClients.find(ip) == m_modbusClients.end()) {
                    auto client = std::make_shared<ModbusTcpClient>(ip, 502); // 默认Modbus端口502
                    if (client->connect()) {
                        m_modbusClients[ip] = std::move(client);

                        {
                            std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                            m_offlineDevices.erase(dbAddr); // 如果存在离线表里，现重新连接成功，移除离线标志

                            DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                            dbAdapter.getAccess().remove(m_offlineDeviceTableName, "db_addr = " + std::to_string(dbAddr));
                        }

                        LOG_INFO << "建立与 " << ip << " 的Modbus-TCP连接";
                    } else {
                        LOG_WARNING << "无法建立与 " << ip << " 的Modbus-TCP连接";
                    }
                }
            }
        }

        // 移除存在于 m_energyDataMap 和 m_modbusClients 中的要释放掉的Modbus客户端连接
        for (auto it = m_modbusClients.begin(); it != m_modbusClients.end();) {
            const std::string &ip = it->first;
            bool valid = it->second->checkValid();

            if (!valid) {
                // 在移除连接前，检查是否是固定设备，如果是，将其添加到离线设备列表
                if (m_useFixedDevices) {
                    std::vector<uint32_t> fixedDeviceDbAddrs;

                    // 找出对应IP地址的所有dbAddr，并检查是否是固定设备
                    {
                        std::lock_guard<std::mutex> mapLock(m_mapMutex);
                        for (const auto &dataItem : m_energyDataMap) {
                            if (dataItem.second.ip == ip) {
                                uint32_t dbAddr = dataItem.first;

                                // 检查是否是固定设备
                                for (const auto &device : m_fixedDevices) {
                                    if (device.dbAddr == dbAddr) {
                                        fixedDeviceDbAddrs.push_back(dbAddr);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // 将找到的固定设备添加到离线设备列表
                    if (!fixedDeviceDbAddrs.empty()) {
                        std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                        for (uint32_t dbAddr : fixedDeviceDbAddrs) {
                            if (m_offlineDevices.find(dbAddr) == m_offlineDevices.end()) {
                                m_offlineDevices.insert(dbAddr);

                                // 添加到数据库的离线设备表
                                DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                                dbAdapter.getAccess().insert(m_offlineDeviceTableName, {{"db_addr", std::to_string(dbAddr)}});

                                LOG_INFO << "固定设备 (dbAddr=" << dbAddr << ", IP=" << ip << ") 连接检查失效，已添加到离线设备列表";
                            }
                        }
                    }
                }

                {
                    // 移除 m_energyDataMap 对应的键值对
                    std::lock_guard<std::mutex> mapLock(m_mapMutex);
                    for (auto item = m_energyDataMap.begin(); item != m_energyDataMap.end();) {
                        const uint32_t dbAddr = item->first;
                        if (item->second.ip == ip) {
                            uint32_t dbAddr = item->first;
                            item = m_energyDataMap.erase(item);
                            m_offlineTempDevices.erase(dbAddr); // m_energyDataMap 里的客户端已移除，如果其存在临时离线表里 ，进行移除
                        } else {
                            ++item;
                        }
                    }
                }
                it = m_modbusClients.erase(it);

            } else {
                ++it;
            }
        }

        // 检测心跳
        {
            std::lock_guard<std::mutex> mapLock(m_mapMutex);
            auto now = std::chrono::steady_clock::now();
            for (auto it = m_energyDataMap.begin(); it != m_energyDataMap.end();) {
                // 固定设备不检查心跳超时
                if (m_useFixedDevices) {
                    bool isFixedDevice = false;
                    for (const auto &device : m_fixedDevices) {
                        if (device.dbAddr == it->first) {
                            isFixedDevice = true;
                            break;
                        }
                    }
                    if (isFixedDevice) {
                        ++it;
                        continue;
                    }
                }

                if (now - it->second.lastUpdate > m_timeoutDuration) {
                    auto ip = it->second.ip;
                    auto dbAddr = it->first;

                    LOG_WARNING << "数据库地址为 " << dbAddr << " 的服务端心跳超时";

                    if (m_modbusClients.find(ip) != m_modbusClients.end()) {
                        m_modbusClients[ip]->disconnect();

                        if (!m_modbusClients[ip]->reConnect()) {
                            // 关闭与该IP地址相关的Modbus连接
                            m_modbusClients.erase(ip);
                            it = m_energyDataMap.erase(it);
                            if (isFormalDevice(dbAddr)) {
                                std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
                                if (m_offlineDevices.find(dbAddr) == m_offlineDevices.end()) {
                                    m_offlineDevices.insert(dbAddr);

                                    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
                                    dbAdapter.getAccess().insert(m_offlineDeviceTableName, {{"db_addr", std::to_string(dbAddr)}});
                                }
                            }

                            LOG_INFO << "重连失败，关闭与 " << ip << " 的Modbus-TCP连接";
                        } else {
                            ++it;
                        }
                    } else {
                        // 不在同一网段
                        it = m_energyDataMap.erase(it);
                        LOG_WARNING << "已断开数据库地址为 " << dbAddr
                                    << " 的 UDP 通讯"; // 注：不可用 it->first来直接输出， 已删除该对象
                    }
                } else {
                    ++it;
                }
            }
        }
    }
}

bool UdpClientDataHandler::isClientOffline(uint32_t dbAddr) {
    std::lock_guard<std::mutex> offlineLock(m_offlineDevicesMutex);
    return m_offlineDevices.find(dbAddr) != m_offlineDevices.end();
}

std::shared_ptr<ModbusTcpClient> UdpClientDataHandler::getModbusClient(const std::string &ip) {
    auto modbusClient = m_modbusClients.find(ip);

    if (modbusClient != m_modbusClients.end()) {
        return modbusClient->second;
    }

    return nullptr;
}

std::unordered_map<uint32_t, EnergyDataWithIP> UdpClientDataHandler::getEnergyDataMap() {
    std::lock_guard<std::mutex> mapLock(m_mapMutex);
    // 拷贝一份
    return m_energyDataMap;
}

bool UdpClientDataHandler::isFormalDevice(uint32_t dbAddr) {
    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
    std::string deviceInfoTableName = dbAdapter.getDeviceInfoTable();
    std::string query = "SELECT COUNT(*) FROM " + deviceInfoTableName + " WHERE db_addr = " + std::to_string(dbAddr) + ";";
    auto result = dbAdapter.getAccess().query(query);

    if (result.empty() || result[0].empty()) {
        return false;
    }

    try {
        int count = std::stoi(result[0][0]);
        return count > 0;
    } catch (const std::exception &e) {
        LOG_ERROR << "查询 db_addr 失败: " << e.what();
        return false;
    }
}

bool UdpClientDataHandler::isTempClientOffline(uint32_t dbAddr) {
    std::lock_guard<std::mutex> lock(m_offlineTempMutex);
    return m_offlineTempDevices.find(dbAddr) != m_offlineTempDevices.end();
}