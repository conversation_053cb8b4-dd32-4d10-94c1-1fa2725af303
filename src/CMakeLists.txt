# 添加MySQL相关的源文件
set(SRC_SOURCES
    UdpBroadcastReceiver.cpp 
    UdpClientDataHandler.cpp 
    TaskStartup.cpp 
    UdpTask.cpp 
    TcpTask.cpp
    MdbsTcpDataHandler.cpp
    MdbsTcpDataManager.cpp
    MdbsRtuDataHandler.cpp
    MdbsRtuDataManager.cpp
    RtuTask.cpp
    qSQLiteDB.cpp
    qMySQLDB.cpp
    DatabaseAdapter.cpp
    qModbusTcpSlave.cpp
)

# 添加库
add_library(src_lib STATIC ${SRC_SOURCES})

# 设置库文件后缀（静态库）
set(CMAKE_FIND_LIBRARY_SUFFIXES ".a")

# 查找 yaml-cpp 库
find_package(yaml-cpp REQUIRED)

# 查找 SQLite3 库
find_package(SQLite3 REQUIRED)

# paho.mqtt.cpp 库已在根CMakeLists.txt中查找

# 添加包含目录
target_include_directories(src_lib PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR} 
    ../common 
    ${SQLite3_INCLUDE_DIRS} 
    ${MYSQL_INCLUDE_DIRS}
    ${PAHO_MQTT_CPP_INCLUDE_DIR}
)

# 链接库
target_link_libraries(src_lib PRIVATE 
    yaml-cpp 
    sqlite3 
    ${MYSQL_LIBRARIES}
    ${PAHO_MQTT_CPP_LIBRARIES}
    ${PAHO_MQTT_C_LIBRARIES}
    spdlog::spdlog
)
