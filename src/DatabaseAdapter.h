#ifndef DATABASE_ADAPTER_H
#define DATABASE_ADAPTER_H

#include "DatabaseAccess.h"
#include "qMySQLDB.h"
#include "qSQLiteDB.h"
#include <memory>
#include <string>

/**
 * @brief 数据库适配器类，用于将现有的qSQLiteDB和qMySQLDB类适配到新的数据库接口
 *
 * 该类实现了适配器模式，为上层应用提供统一的数据库操作接口。
 * 同时，通过DatabaseAccess成员，提供了数据库性能监控功能。
 *
 * 注意：直接调用qMySQLDB或qSQLiteDB的方法不会被性能监控系统记录，
 * 应尽量通过DatabaseAdapter或DatabaseAccess进行数据库操作。
 */
class DatabaseAdapter {
  public:
    /**
     * @brief 获取数据库适配器单例
     *
     * @return DatabaseAdapter& 数据库适配器单例
     */
    static DatabaseAdapter &getInstance() {
        static DatabaseAdapter instance;
        return instance;
    }

    /**
     * @brief 获取数据库访问对象
     *
     * @return DatabaseAccess& 数据库访问对象
     */
    DatabaseAccess &getAccess() {
        return m_access;
    }

    /**
     * @brief 获取SQLite数据库对象
     *
     * @param dbPath 数据库路径
     * @return qSQLiteDB& SQLite数据库对象
     */
    qSQLiteDB &getSQLiteDB(const std::string &dbPath = "data.db") const {
        return qSQLiteDB::getInstance(dbPath);
    }

    /**
     * @brief 获取当前使用的数据库类型
     *
     * @return DBConfig::DBType 数据库类型
     */
    DBConfig::DBType getDBType() const {
        return m_dbType;
    }

    /**
     * @brief 创建设备表
     *
     * @param dbAddr 设备地址
     * @param monthStr 月份字符串
     * @param isTemp 是否为临时设备
     */
    void createDeviceTable(uint32_t dbAddr, const std::string &monthStr, bool isTemp = false) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->createDeviceTable(dbAddr, monthStr, isTemp);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().createDeviceTable(dbAddr, monthStr, isTemp);
        }
    }

    /**
     * @brief 删除设备表
     *
     * @param dbAddr 设备地址
     * @param isTemp 是否为临时设备
     */
    void removeDeviceTable(uint32_t dbAddr, bool isTemp = false) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->removeDeviceTable(dbAddr, isTemp);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().removeDeviceTable(dbAddr, isTemp);
        }
    }

    /**
     * @brief 将临时设备数据插入到正式设备表中
     *
     * @param tempDbAddr 临时设备地址
     * @param formalDbAddr 正式设备地址
     */
    void insertTempData2Formal(uint32_t tempDbAddr, uint32_t formalDbAddr) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->insertTempData2Formal(tempDbAddr, formalDbAddr);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().insertTempData2Formal(tempDbAddr, formalDbAddr);
        }
    }

    /**
     * @brief 重命名设备表
     *
     * @param oldDbAddr 旧设备地址
     * @param newDbAddr 新设备地址
     * @param isTemp 是否为临时设备
     */
    void renameDeviceTables(uint32_t oldDbAddr, uint32_t newDbAddr, bool isTemp) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->renameDeviceTables(oldDbAddr, newDbAddr, isTemp);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().renameDeviceTables(oldDbAddr, newDbAddr, isTemp);
        }
    }

    /**
     * @brief 将临时设备表转换为正式设备表
     *
     * @param dbAddr 设备地址
     */
    void Temp2FormalTable(uint32_t dbAddr) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->Temp2FormalTable(dbAddr);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().Temp2FormalTable(dbAddr);
        }
    }

    /**
     * @brief 插入设备数据（带压缩）
     *
     * @param values 数据值
     * @param dataBytes 二进制数据
     * @param isTemp 是否为临时设备
     */
    void insertDeviceDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                         const std::vector<uint8_t> &dataBytes,
                                         bool isTemp) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->insertDeviceDataWithCompression(values, dataBytes, isTemp);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().insertDeviceDataWithCompression(values, dataBytes, isTemp);
        }
    }

    /**
     * @brief 插入设备数据
     *
     * @param dbAddr 设备地址
     * @param values 数据值
     * @param blobFieldName 二进制字段名
     * @param blobData 二进制数据
     * @param isTemp 是否为临时设备
     */
    void insertDeviceData(uint32_t dbAddr,
                          const std::vector<std::pair<std::string, int64_t>> &values,
                          const std::string &blobFieldName,
                          const std::vector<uint8_t> &blobData,
                          bool isTemp = false) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->insertDeviceData(dbAddr, values, blobFieldName, blobData, isTemp);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().insertDeviceData(dbAddr, values, blobFieldName, blobData, isTemp);
        }
    }

    /**
     * @brief 创建设备信息
     *
     * @param id 设备ID
     * @param dbAddr 设备地址
     * @param currentSn 当前序列号
     */
    void creatDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->creatDeviceInfo(id, dbAddr, currentSn);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().creatDeviceInfo(id, dbAddr, currentSn);
        }
    }

    /**
     * @brief 更新设备信息
     *
     * @param id 设备ID
     * @param dbAddr 设备地址
     * @param currentSn 当前序列号
     */
    void updateDeviceInfo(int id, uint32_t dbAddr, const std::string &currentSn) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->updateDeviceInfo(id, dbAddr, currentSn);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().updateDeviceInfo(id, dbAddr, currentSn);
        }
    }

    /**
     * @brief 更新系统序列号到设备信息
     *
     * @param id 设备ID
     * @param currentSn 当前序列号
     */
    void insertSysSn2DevInfo(int id, const std::string &currentSn) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->insertSysSn2DevInfo(id, currentSn);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().insertSysSn2DevInfo(id, currentSn);
        }
    }

    /**
     * @brief 删除过期的设备表
     *
     * @param keepMonths 保留的月份数
     */
    void removeExpiredDeviceTables(int keepMonths = 24) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->removeExpiredDeviceTables(keepMonths);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().removeExpiredDeviceTables(keepMonths);
        }
    }

    /**
     * @brief 查询设备数据
     *
     * @param dbAddr 设备地址
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return std::vector<DataRecord> 数据记录列表
     */
    std::vector<DataRecord> queryDeviceData(uint32_t dbAddr, int64_t startTime, int64_t endTime) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            return m_access.execute<std::vector<DataRecord>>([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    return mysqlConn->queryDeviceData(dbAddr, startTime, endTime);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().queryDeviceData(dbAddr, startTime, endTime);
        }
    }

    /**
     * @brief 获取设备信息表名
     *
     * @return std::string 设备信息表名
     */
    std::string getDeviceInfoTable() {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            return m_access.execute<std::string>([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    return mysqlConn->getDeviceInfoTable();
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().getDeviceInfoTable();
        }
    }

    /**
     * @brief 获取离线设备表名
     *
     * @return std::string 离线设备表名
     */
    std::string getOfflineDeviceTable() {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            return m_access.execute<std::string>([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    return mysqlConn->getOfflineDeviceTable();
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().getOfflineDeviceTable();
        }
    }

    /**
     * @brief 获取仪表数据表名
     *
     * @return std::string 仪表数据表名
     */
    // std::string getMeterDataTable() {
    //     if (m_dbType == DBConfig::DBType::MYSQL) {
    //         return m_access.execute<std::string>([&](std::shared_ptr<IDatabaseInterface> conn) {
    //             auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
    //             if (mysqlConn) {
    //                 return mysqlConn->getMeterDataTable();
    //             } else {
    //                 throw std::runtime_error("无法将连接转换为qMySQLDB类型");
    //             }
    //         });
    //     } else {
    //         return getSQLiteDB().getMeterDataTable();
    //     }
    // }

    /**
     * @brief 创建电表数据分月表
     *
     * @param monthStr 月份字符串，格式为YYYYMM
     */
    void createMeterTable(const std::string &monthStr) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->createMeterTable(monthStr);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().createMeterTable(monthStr);
        }
    }

    /**
     * @brief 删除电表数据分月表
     *
     * @param monthStr 月份字符串，格式为YYYYMM
     */
    void removeMeterTable(const std::string &monthStr) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->removeMeterTable(monthStr);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().removeMeterTable(monthStr);
        }
    }

    /**
     * @brief 删除过期的电表数据表
     *
     * @param keepMonths 保留的月份数，例如24表示保留最近24个月的数据表
     */
    // void removeExpiredMeterTables(int keepMonths = 24) {
    //     if (m_dbType == DBConfig::DBType::MYSQL) {
    //         m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
    //             auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
    //             if (mysqlConn) {
    //                 mysqlConn->removeExpiredMeterTables(keepMonths);
    //             } else {
    //                 throw std::runtime_error("无法将连接转换为qMySQLDB类型");
    //             }
    //         });
    //     } else {
    //         return getSQLiteDB().removeExpiredMeterTables(keepMonths);
    //     }
    // }

    /**
     * @brief 向电表数据表中插入数据
     *
     * @param values 插入的数据列名和值
     * @param dataBytes 二进制数据
     */
    void insertMeterDataWithCompression(const std::vector<std::pair<std::string, int64_t>> &values,
                                        const std::vector<uint8_t> &dataBytes) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            m_access.execute([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    mysqlConn->insertMeterDataWithCompression(values, dataBytes);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().insertMeterDataWithCompression(values, dataBytes);
        }
    }

    /**
     * @brief 查询电表在指定时间范围内的数据
     *
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 数据记录列表
     */
    std::vector<DataRecord> queryMeterData(int64_t startTime, int64_t endTime) {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            return m_access.execute<std::vector<DataRecord>>([&](std::shared_ptr<IDatabaseInterface> conn) {
                auto mysqlConn = std::dynamic_pointer_cast<qMySQLDB>(conn);
                if (mysqlConn) {
                    return mysqlConn->queryMeterData(startTime, endTime);
                } else {
                    throw std::runtime_error("无法将连接转换为qMySQLDB类型");
                }
            });
        } else {
            return getSQLiteDB().queryMeterData(startTime, endTime);
        }
    }

    /**
     * @brief 初始化数据库
     *
     * @return bool 是否初始化成功
     */
    bool initializeDatabase();

  private:
    /**
     * @brief 构造函数
     */
    DatabaseAdapter();

    /**
     * @brief 析构函数
     */
    ~DatabaseAdapter() = default;

    // 禁止拷贝和赋值
    DatabaseAdapter(const DatabaseAdapter &) = delete;
    DatabaseAdapter &operator=(const DatabaseAdapter &) = delete;

    DatabaseAccess m_access;   // 数据库访问对象 （用于调用通用接口）
    DBConfig::DBType m_dbType; // 数据库类型
};

#endif // DATABASE_ADAPTER_H