#include "RtuTask.h"
#include "ConfigManager.h"
#include "DatabaseAdapter.h"
#include "Logger.h"
#include <ctime>

RtuTask::RtuTask() : m_dataManager(MdbsRtuDataManager::getInstance()) {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    RS485Config rs485Config;

    // 解析 RS485-1 配置信息
    if (!parseRS485Config(config, "RS485-1", rs485Config)) {
        LOG_ERROR << "解析 RS485-1 失败";
    }

    // 初始化 Modbus RTU 主站
    m_rtuMaster = std::make_unique<ModbusRtuMaster>(
        rs485Config.device, rs485Config.baud, rs485Config.parity, rs485Config.dataBit, rs485Config.stopBit, rs485Config.slaveId);
    if (!m_rtuMaster->connect()) {
        m_rtuMaster->disconnect();
        LOG_ERROR << "无法连接到 Modbus RTU 设备。";
    }

    // 从配置表读取数据库路径
    m_dbPath = config["dbPath"].as<std::string>("data.db");
    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
}

RtuTask::~RtuTask() {
    if (m_rtuMaster) {
        m_rtuMaster->disconnect();
    }
}

void RtuTask::RtuCommTask() {
    try {
        // 读写都在同一线程
        if (!m_rtuMaster || !m_rtuMaster->isConnected()) {
            if (!m_rtuMaster->reConnect()) {
                LOG_ERROR << "Modbus RTU 连接未初始化。";
                return;
            }
        }

        // 检测从站是否正常通讯 (主要防止从站还没开启就执行下面业务)
        if (!m_rtuMaster->monitorConnection()) {
            LOG_ERROR << "Error: 与电表连接丢失 ";
            return;
        }

        // 从数据管理器获取数据处理器
        std::shared_ptr<MdbsRtuDataHandler> m_dataHandler = m_dataManager.getHandler();
        if (!m_dataHandler) {
            LOG_ERROR << "数据处理器未初始化。";
            return;
        }

        // 查看是否需要写数据
        auto pendingWrite = m_dataHandler->getPendingWrite();
        if (pendingWrite) {
            if (!handleWriteOperation(*pendingWrite)) {
                LOG_ERROR << "写入操作失败。";
            }
        }

        // 读取数据
        // readModbusData("输入寄存器");
        readModbusData("保持寄存器");

        // 数据解析
        auto dataBatch = m_dataHandler->getCacheDataBatch(20); // 每次处理20条数据
        for (auto &data : dataBatch) {
            m_dataHandler->parseData(data);
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "发生异常: " << e.what();
    }
}

void RtuTask::readModbusData(const std::string &registerType) {
    // 从数据管理器获取数据处理器
    std::shared_ptr<MdbsRtuDataHandler> m_dataHandler = m_dataManager.getHandler();
    if (!m_dataHandler) {
        LOG_ERROR << "数据处理器未初始化，无法读取数据。";
        return;
    }

    try {
        // 获取寄存器地址块配置
        const auto &configs = m_dataHandler->getRegConfigMap(registerType);

        for (const auto &config : configs) {
            auto currentTime = std::chrono::steady_clock::now();
            bool isRead = true;
            bool isSave = true;
            std::string key = registerType + "/" + std::to_string(config.addr); // 地址块路径
            {
                // 加锁以保护 m_lastReadTimes 的访问
                std::lock_guard<std::mutex> lock(m_lastReadTimesMutex);
                // 检查读取周期
                if (m_lastReadTimes.find(key) != m_lastReadTimes.end()) {
                    auto readElapsedTime =
                        std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - m_lastReadTimes[key]).count();
                    if (readElapsedTime < config.intervalTime) {
                        isRead = false;
                    }
                } else {
                    // 初始化
                    m_lastReadTimes[key] = currentTime;
                }

                if (isRead) {
                    // 更新上次读取时间
                    m_lastReadTimes[key] = currentTime;
                }
            }

            {
                // 加锁以保护 m_lastSaveDbTimes 的访问
                std::lock_guard<std::mutex> lock(m_lastSaveDbTimesMutex);
                // 检查保存数据库周期
                if (m_lastSaveDbTimes.find(key) != m_lastSaveDbTimes.end()) {
                    auto elapsedTime =
                        std::chrono::duration_cast<std::chrono::minutes>(currentTime - m_lastSaveDbTimes[key]).count();
                    if (elapsedTime < config.saveDbTime) {
                        // 未达到保存数据库时间
                        isSave = false;
                        if (isRead) {
                            goto readBlock;
                        } else {
                            continue; // 读数据到缓冲的时间和保存到数据库的时间均未达到时间间隔，跳过读取
                        }
                    }
                }
                // 更新上次保存数据库时间
                m_lastSaveDbTimes[key] = currentTime;
            }

        readBlock:
            readData(registerType, config.addr, config.length, isRead, isSave);
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << e.what();
    }
}

void RtuTask::readData(const std::string &registerType, uint16_t startAddress, size_t length, bool isRead, bool isSave) {
    std::vector<uint16_t> buffer(length);
    int ret = -1;
    uint8_t registerTypeCode = -1;

    if (isRead || isSave) {
        if (registerType == "输入寄存器") {
            registerTypeCode = MODBUS_FC_READ_INPUT_REGISTERS;
            ret = m_rtuMaster->readInputRegisters(startAddress, length, buffer);
        } else if (registerType == "保持寄存器") {
            registerTypeCode = MODBUS_FC_READ_HOLDING_REGISTERS;
            ret = m_rtuMaster->readHoldingRegisters(startAddress, length, buffer);
        } else {
            LOG_ERROR << "未知的寄存器类型: " << registerType;
            return;
        }
    }

    if (ret > 0) {
        // 从数据管理器获取数据处理器
        std::shared_ptr<MdbsRtuDataHandler> m_dataHandler = m_dataManager.getHandler();
        if (!m_dataHandler) {
            LOG_ERROR << "数据处理器未初始化。";
            return;
        }

        // 注意: 保存和读取数据的顺序不能改变，如果先读取数据再保存，buffer 会被清空 std::move(buffer)
        if (isSave) {
            LOG_ERROR << "Error: (Meter) 数据存入数据库 " << registerType;
            // 将读取到的数据存入数据库
            saveDataToDatabase(registerTypeCode, startAddress, ret, buffer);
        }

        if (isRead) {
            LOG_ERROR << "Error: (Meter) 数据存入缓存 " << registerType;
            ModbusData mdbsData;
            mdbsData.timestamp = getCurrentTimestamp();
            mdbsData.registerType = registerType;
            mdbsData.startAddress = startAddress;
            mdbsData.length = ret;
            mdbsData.dataBuffer = std::move(buffer);
            mdbsData.dbAddr = 0; // 这里 dbAddr 置为 0

            // 把数据存入缓存当中
            m_dataHandler->addCacheData(mdbsData);
        }

    } else {
        LOG_ERROR << "读取 " << registerType << " 数据失败。";
    }
}

bool RtuTask::handleWriteOperation(const std::vector<std::pair<int, std::any>> &writeBatch) {
    if (writeBatch.empty()) {
        return false;
    }

    // 确保 writeBatch 是连续的
    bool isBatchWrite = writeBatch.size() > 1;

    if (isBatchWrite) {
        // 准备批量写入的数据
        std::vector<uint16_t> values;
        for (const auto &[address, data] : writeBatch) {
            std::vector<uint16_t> tempRegisters;
            if (!convertToValues(data, tempRegisters)) {
                LOG_ERROR << "转换数据失败，地址：" << address;
                return false;
            }
            values.insert(values.end(), tempRegisters.begin(), tempRegisters.end());
        }

        // 使用批量写入
        return m_rtuMaster->writeMultipleRegisters(writeBatch.front().first, values);
    } else {
        // 单个写入
        const auto &[address, data] = writeBatch.front();
        std::vector<uint16_t> registers;

        if (!convertToValues(data, registers)) {
            LOG_ERROR << "转换数据失败，地址：" << address;
            return false;
        }

        bool success = false;
        // if (registers.size() == 1) {
        //     success = m_rtuMaster->writeSingleRegister(address, registers[0]);
        // } else {
        //     success = m_rtuMaster->writeMultipleRegisters(address, registers);
        // }

        // 电表协议规定只有写多个寄存器功能码(10H)
        success = m_rtuMaster->writeMultipleRegisters(address, registers);

        if (!success) {
            LOG_ERROR << "写入寄存器失败，地址：" << address;
        }

        return success;
    }
}

bool RtuTask::convertToValues(const std::any &data, std::vector<uint16_t> &registers) {
    // 检查写操作的值类型并处理

    registers.clear();

    if (data.type() == typeid(uint16_t)) {
        registers.push_back(std::any_cast<uint16_t>(data));
    } else if (data.type() == typeid(int16_t)) {
        registers.push_back(static_cast<uint16_t>(std::any_cast<int16_t>(data)));
    } else if (data.type() == typeid(uint32_t)) {
        uint32_t temp = std::any_cast<uint32_t>(data);
        registers.push_back(static_cast<uint16_t>(temp >> 16));
        registers.push_back(static_cast<uint16_t>(temp & 0xFFFF));
    } else if (data.type() == typeid(int32_t)) {
        int32_t temp = std::any_cast<int32_t>(data);
        uint32_t utemp = static_cast<uint32_t>(temp);
        registers.push_back(static_cast<uint16_t>(utemp >> 16));
        registers.push_back(static_cast<uint16_t>(utemp & 0xFFFF));
    } else if (data.type() == typeid(std::string)) {
        std::string value = std::any_cast<std::string>(data);
        for (size_t i = 0; i < value.size(); i += 2) {
            uint16_t combined = 0;
            combined |= static_cast<uint16_t>(value[i]);
            if (i + 1 < value.size()) {
                combined |= static_cast<uint16_t>(value[i + 1]) << 8;
            }
            registers.push_back(combined);
        }
    } else if (data.type() == typeid(std::vector<uint16_t>)) {
        registers = std::any_cast<std::vector<uint16_t>>(data);
    } else if (data.type() == typeid(std::bitset<16>)) {
        std::bitset<16> bits = std::any_cast<std::bitset<16>>(data);
        registers.push_back(static_cast<uint16_t>(bits.to_ulong()));
    } else if (data.type() == typeid(float)) {
        float f = std::any_cast<float>(data);
        uint32_t temp = *reinterpret_cast<uint32_t *>(&f);
        registers.push_back(static_cast<uint16_t>(temp >> 16));
        registers.push_back(static_cast<uint16_t>(temp & 0xFFFF));
    } else {
        LOG_ERROR << "不支持的数据类型。";
        return false;
    }

    return true;
}

std::string RtuTask::getCurrentTimestamp() {
    // 获取当前时间（单位：毫秒）
    auto now = std::chrono::system_clock::now();
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
    return std::to_string(milliseconds.count());
}

bool RtuTask::parseRS485Config(const YAML::Node &config, const std::string &rs485Name, RS485Config &outConfig) {
    const YAML::Node &serialPortConfig = config["serialPort"];
    if (!serialPortConfig) {
        LOG_ERROR << "无法找到 SerialPort 配置";
        return false;
    }

    const YAML::Node &rs485Config = serialPortConfig[rs485Name];
    if (!rs485Config) {
        LOG_ERROR << "无法找到 " << rs485Name << " 配置";
        return false;
    }

    // 解析字段
    outConfig.device = rs485Config["Path"].as<std::string>();
    outConfig.slaveId = rs485Config["slave_id"].as<int>();
    outConfig.baud = rs485Config["baudRate"].as<int>();
    outConfig.parity = rs485Config["parity"].as<std::string>()[0]; // 提取第一个字符
    outConfig.dataBit = rs485Config["dataBit"].as<int>();
    outConfig.stopBit = rs485Config["stopBit"].as<int>();

    return true;
}

void RtuTask::saveDataToDatabase(uint8_t registerTypeCode, uint16_t startAddress, uint16_t length, const std::vector<uint16_t> &data) {
    // 准备数据
    std::vector<std::pair<std::string, int64_t>> values = {{"timestamp", static_cast<int64_t>(std::stoll(getCurrentTimestamp()))},
                                                           {"register_type", static_cast<int64_t>(registerTypeCode)},
                                                           {"start_address", static_cast<int64_t>(startAddress)},
                                                           {"length", static_cast<int64_t>(length)}};

    // 将uint16_t数据转换为uint8_t数据
    std::vector<uint8_t> byteData;
    for (const auto &value : data) {
        // 每个uint16_t拆分为两个uint8_t（高字节和低字节）
        byteData.push_back(static_cast<uint8_t>(value & 0xFF));        // 低字节
        byteData.push_back(static_cast<uint8_t>((value >> 8) & 0xFF)); // 高字节
    }

    // 插入数据库
    DatabaseAdapter &dbAdapter = DatabaseAdapter::getInstance();
    dbAdapter.insertMeterDataWithCompression(values, byteData);
}
