#include "DatabaseAdapter.h"
#include "MySQLMigration.h"
#include "Logger.h"

DatabaseAdapter::DatabaseAdapter() : m_access(DatabaseFactory::getConnectionPool()) {
    // 从配置文件读取数据库类型
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    std::string dbTypeStr = config["database"]["type"].as<std::string>("sqlite");
    if (dbTypeStr == "mysql") {
        m_dbType = DBConfig::DBType::MYSQL;
    } else {
        m_dbType = DBConfig::DBType::SQLITE;
    }
}

bool DatabaseAdapter::initializeDatabase() {
    try {
        if (m_dbType == DBConfig::DBType::MYSQL) {
            // 使用MySQL迁移类初始化数据库
            auto result = MySQLMigration::getInstance().initializeDatabase(m_access);
            if (result) {
                DatabaseMonitor::getInstance().startMonitoring(m_access.getConnectionPool(), 300000); // 300秒
            }
            return result;
        } else {
            // SQLite数据库不需要初始化，直接返回成功
            return true;
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "初始化数据库失败: " << e.what();
        return false;
    }
}