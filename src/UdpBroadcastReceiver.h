#ifndef UDP_BROADCAST_RECEIVER_H
#define UDP_BROADCAST_RECEIVER_H

#include "StructDefs.h"
#include <netinet/in.h>
#include <string>
#include <sys/epoll.h>
#include <vector>

class UdpBroadcastReceiver {
  public:
    UdpBroadcastReceiver(int port);
    ~UdpBroadcastReceiver();
    bool init();
    void receive();

    std::vector<ReceivedPacket> m_dataPackets; // 用来存储接收到的所有数据包

  private:
    int m_sockfd;
    int m_port;
    struct sockaddr_in m_serverAddr;
};

#endif // UDP_BROADCAST_RECEIVER_H