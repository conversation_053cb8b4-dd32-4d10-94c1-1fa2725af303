#ifndef SQLITE_DB_H
#define SQLITE_DB_H

#include "IDatabaseInterface.h"
#include <mutex>
#include <sqlite3.h>
#include <stdexcept>
#include <string>
#include <vector>

class iSQLiteDB : public IDatabaseInterface {
  public:
    /**
     * @brief 构造函数
     */
    iSQLiteDB(const std::string &dbPath);

    /**
     * @brief 虚析构函数
     */
    virtual ~iSQLiteDB() {
        close();
    }

    // 禁止拷贝
    iSQLiteDB(const iSQLiteDB &) = delete;
    iSQLiteDB &operator=(const iSQLiteDB &) = delete;

    // 允许移动
    iSQLiteDB(iSQLiteDB &&) noexcept = default;
    iSQLiteDB &operator=(iSQLiteDB &&) noexcept = default;

    /**
     * @brief 带锁的执行非查询 SQL 命令（例如 CREATE、INSERT、UPDATE、DELETE）。
     *
     * @param query 要执行的 SQL 命令。
     * @throws std::runtime_error 如果 SQL 命令执行失败。
     */
    void executeWithLock(const std::string &query) override;

    /**
     * @brief 执行 SQL 查询并返回结果。
     *
     * @param query 要执行的 SQL 查询。
     * @return 查询结果，返回一个行向量，其中每行是一个字符串向量。
     * @throws std::runtime_error 如果 SQL 查询执行失败。
     */
    std::vector<std::vector<std::string>> query(const std::string &query) override;

    /**
     * @brief 向表中插入数据。
     *
     * @param table 表名。
     * @param values 要插入的列-值对向量。
     */
    void insert(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values) override;

    /**
     * @brief 更新表中的数据。
     *
     * @param table 表名。
     * @param values 要更新的列-值对向量。
     * @param condition 更新的SQL条件。
     */
    void update(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values, const std::string &condition);

    /**
     * @brief 从表中删除数据。
     *
     * @param table 表名。
     * @param condition 删除的SQL条件。
     */
    void remove(const std::string &table, const std::string &condition) override;

    /**
     * @brief 检查数据库连接是否正常
     *
     * @return bool 连接正常返回true，否则返回false
     */
    bool isConnected() override;

    /**
     * @brief 重新连接数据库
     *
     * @return bool 连接成功返回true，否则返回false
     */
    bool reconnect() override;

    /**
     * @brief 获取最后执行的SQL语句
     *
     * @return std::string 最后执行的SQL语句
     */
    std::string getLastExecutedSql() const override;

    /**
     * @brief 存入最新的sql语句
     *
     * @param sql 最新的sql语句
     */
    void saveLastExecutedSql(const std::string &sql) override;

  protected:
    sqlite3 *m_db;
    mutable std::mutex m_db_mutex;
    std::string m_dbPath;
    std::string m_lastExecutedSql; // 存储最后执行的SQL语句

    /**
     * @brief 打开数据库。
     *
     * @param dbPath SQLite 数据库文件的路径。
     * @throws std::runtime_error 如果数据库无法打开。
     */
    void open(const std::string &dbPath);

    /**
     * @brief 关闭数据库。
     */
    void close();

    /**
     * @brief 执行非查询 SQL 命令（例如 CREATE、INSERT、UPDATE、DELETE）。
     *
     * @param query 要执行的 SQL 命令。
     * @throws std::runtime_error 如果 SQL 命令执行失败。
     */
    void execute(const std::string &query);

    /**
     * @brief 处理查询结果的回调函数。
     *
     * @param data 结果容器的指针。
     * @param argc 结果行中的列数。
     * @param argv 列值数组。
     * @param azColName 列名数组。
     * @return 成功返回 0。
     */
    static int callback(void *data, int argc, char **argv, char **azColName);
};

#endif // SQLITE_DB_H