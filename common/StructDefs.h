#ifndef STRUCT_DEFS_H
#define STRUCT_DEFS_H

#include <cstdint>
#include <vector>
#include <string>
#include <memory>
#include <functional>
#include <chrono>
#include <variant>
#include <unordered_map>
#include <any>
#include <sys/socket.h>
#include <netinet/in.h>

struct DataRecord {
    int64_t timestamp;
    uint32_t dbAddr;
    int registerType;
    int startAddress;
    int length;
    std::vector<uint8_t> data;
};

// 来自 ModbusDataHandler.h 的结构体
struct ModbusData {
    std::string timestamp;
    std::string registerType;
    uint16_t startAddress;
    size_t length; // 寄存器个数
    std::vector<uint16_t> dataBuffer;
    uint32_t dbAddr;
};

struct RegisterInfo {
    std::string name;
    int address;
    int length;
    bool readOnly;
    std::shared_ptr<class RegisterData> data; // 使用前向声明
};

struct RegisterPath {
    std::string registerType; // 输入寄存器或保持寄存器
    std::string category;     // 系统数据, 液冷数据, PCS数据, 系统数据2
};

struct RegisterConfig {
    uint16_t addr; // 起始地址
    size_t length;
    int intervalTime; // 读取周期 ms
    int saveDbTime;   // 存储数据库周期 min
};

struct ConditionalParseConfig {
    std::string registerName;
    uint16_t registerAddr;
    uint16_t startAddr;
    uint16_t endAddr;
    std::unordered_map<int, std::vector<RegisterInfo>> parseMap; // 键：协议类型序号 值：该协议数据
};

struct DataAttributes {
    int offset;
    double conversionFactor;
    size_t size;
};

// 来自 TaskManager.h 的结构体
struct TaskConfig {
    std::string name;
    int cycleTime;
    std::function<void()> taskFunction;
};

// 来自 RtuTask.h 的结构体
struct RS485Config {
    std::string device = "/dev/ttyRS485-1"; // 默认设备路径
    int slaveId = 1;                        // 默认从机地址
    int baud = 9600;                        // 默认波特率
    char parity = 'N';                      // 默认无校验
    int dataBit = 8;                        // 默认 8 个数据位
    int stopBit = 1;                        // 默认 1 个停止位
};

// 来自 UdpClientDataHandler.h 的结构体
struct EnergyData {
    uint16_t version;   // 版本
    uint32_t dbAddress; // 数据库地址
    uint16_t status;    // 设备状态
    int16_t power;      // 当前功率
    uint16_t soc;       // 池SOC
};

struct EnergyDataWithIP {
    std::string ip;
    EnergyData data;
    std::chrono::steady_clock::time_point lastUpdate; // 记录最后更新时间
};

// 来自 UdpBroadcastReceiver.h 的结构体
struct ReceivedPacket {
    std::vector<char> data;
    int length;
    sockaddr_in source;

    ReceivedPacket(int len, const char *buffer, const sockaddr_in &src)
        : length(len), source(src), data(buffer, buffer + len) {
    }
};

// 来自 MdbsTcpDataManager.h 的结构体
using ModbusDataType = std::variant<uint16_t, uint32_t>;

struct SummaryInfo {
    double value;
    int offset;              // 偏移量
    double conversionFactor; // 单位因子
    size_t size;             // 数据大小，16 位或 32 位
    std::string description; // 汇总信息的描述
    bool readOnly;           // 只读属性
};

enum class InputSummaryIndex : uint16_t {
    TOTAL_PCS_POWER = 1,          // 总实时PCS有功功率
    TOTAL_PCS_REACTIVE_POWER = 2, // 总实时PCS无功功率
    TOTAL_VOLTAGE = 3,            // 总电压
    TOTAL_CURRENT = 4,            // 总电流
    AVG_SOC = 5,                  // 总SOC
    AVG_CELL_TEMP = 6,            // 电池平均温度
    MAX_CELL_TEMP_DIFF = 7,       // 电池最大温差
    MAX_CELL_VOLTAGE = 8,         // 最高单体电压值
    MIN_CELL_VOLTAGE = 9,         // 最低单体电压值
    SYSTEM_STATE = 10,            // 系统状态
};

enum class HoldingSummaryIndex : uint16_t {
    TOTAL_STARTUP_SHUTDOWN = 1, // 总启停指令
    TOTAL_WORK_MODE = 2,        // 并离网模式
    TOTAL_SCRAM = 3,            // 急停按钮
    TOTAL_POWER = 4,            // 总有功功率
    TOTAL_REACTIVE_POWER = 5,   // 总无功功率
};

struct InputSummaryData {
    double totalPcsPower;
    double totalPcsReactivePower;
    double totalSoc;
    double totalVoltage;    // 内部总电压"的平均值
    double totalCurrent;    // 内部总电流"的总和
    double avgCellTemp;     // 电池平均温度的平均值
    double maxCellTempDiff; // 最大温差
    double maxCellVoltage;  // 最高单体电压
    double minCellVoltage;  // 最低单体电压
    double systemState;     // 系统状态
};

struct HolidingSummaryData {
    double totalPower;
    double totalReactivePower;
};

// 定义常量枚举
enum DeviceType { FORMAL = 0, TEMP };
enum class RegisterType { InputRegister, HoldingRegister };

#endif // STRUCT_DEFS_H