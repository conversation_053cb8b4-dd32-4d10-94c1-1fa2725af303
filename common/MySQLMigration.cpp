#include "MySQLMigration.h"
#include "Logger.h"

bool MySQLMigration::initializeDatabase(DatabaseAccess &dbAccess) {
    try {
        // 创建设备信息表
        if (!createDeviceInfoTable(dbAccess)) {
            return false;
        }

        // 创建离线设备表
        if (!createOfflineDeviceTable(dbAccess)) {
            return false;
        }

        // 创建电表数据表
        // if (!createMeterDataTable(dbAccess)) {
        //     return false;
        // }

        return true;
    } catch (const std::exception &e) {
        LOG_ERROR << "初始化MySQL数据库失败: " << e.what();
        return false;
    }
}

bool MySQLMigration::createDeviceInfoTable(DatabaseAccess &dbAccess) {
    std::string sql = R"(
        CREATE TABLE IF NOT EXISTS device_info (
            id INT NOT NULL AUTO_INCREMENT,
            db_addr INT NOT NULL,
            current_sn VARCHAR(50) NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY (db_addr)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    )";

    return executeSql(dbAccess, sql);
}

bool MySQLMigration::createOfflineDeviceTable(DatabaseAccess &dbAccess) {
    std::string sql = R"(
        CREATE TABLE IF NOT EXISTS offline_device (
            id INT NOT NULL AUTO_INCREMENT,
            db_addr INT NOT NULL,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY (db_addr)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    )";

    return executeSql(dbAccess, sql);
}

bool MySQLMigration::createMeterDataTable(DatabaseAccess &dbAccess) {
    std::string sql = R"(
        CREATE TABLE IF NOT EXISTS meter_data (
            id INT NOT NULL AUTO_INCREMENT,
            timestamp BIGINT NOT NULL,
            register_type VARCHAR(50) NOT NULL,
            start_address INT NOT NULL,
            length INT NOT NULL,
            data TEXT NOT NULL,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX (timestamp),
            INDEX (start_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    )";

    return executeSql(dbAccess, sql);
}

bool MySQLMigration::createDeviceTable(DatabaseAccess &dbAccess, uint32_t dbAddr, const std::string &monthStr, bool isTemp) {
    std::string tableName = isTemp ? "temp_device_" : "device_";
    tableName += std::to_string(dbAddr) + "_" + monthStr;

    std::string sql = R"(
        CREATE TABLE IF NOT EXISTS )" +
                      tableName + R"( (
            id INT NOT NULL AUTO_INCREMENT,
            timestamp BIGINT NOT NULL,
            register_type VARCHAR(50) NOT NULL,
            start_address INT NOT NULL,
            length INT NOT NULL,
            data TEXT NOT NULL,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (timestamp, db_addr),
            INDEX (start_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    )";

    return executeSql(dbAccess, sql);
}

bool MySQLMigration::executeSql(DatabaseAccess &dbAccess, const std::string &sql) {
    try {
        dbAccess.execute([&sql](auto conn) {
            conn->executeWithLock(sql);
        });
        return true;
    } catch (const std::exception &e) {
        LOG_ERROR << "执行SQL失败: " << e.what() << "\nSQL: " << sql;
        return false;
    }
}