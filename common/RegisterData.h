#ifndef REGISTERDATA_H
#define REGISTERDATA_H

#include <bitset>
#include <cstdint>
#include <iostream>
#include <sstream>
#include <unordered_map>
#include <vector>

class RegisterData {
  public:
    virtual ~RegisterData() = default;
    virtual void setData(const uint16_t *data) = 0;
    virtual void setDataEndian(uint16_t *data) = 0;
    virtual std::string getData() const = 0;
    virtual const std::vector<uint16_t> &getRawData() const = 0;
    virtual const int getDataLength() const = 0;
    virtual int getOffset() const = 0;
    virtual double getConversionFactor() const = 0;
    virtual size_t getSize() const = 0;
};

class Int16Data : public RegisterData {
  public:
    Int16Data(double factor = 1.0, int offset = 0);

    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override {}
    std::string getData() const override;
    const std::vector<uint16_t> &getRawData() const override;
    const int getDataLength() const override;

    int getOffset() const override { return m_offset_; }
    double getConversionFactor() const override { return m_conversionFactor; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    double m_value;
    double m_conversionFactor;
    int m_offset_;
    const int m_length = 1; // 寄存器个数
    std::vector<uint16_t> m_rawData;
};

class UInt16Data : public RegisterData {
  public:
    UInt16Data(double factor = 1.0, int offset = 0);

    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override {}
    std::string getData() const override;
    const std::vector<uint16_t> &getRawData() const override;
    const int getDataLength() const override;

    int getOffset() const override { return m_offset_; }
    double getConversionFactor() const override { return m_conversionFactor; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    double m_value;
    double m_conversionFactor;
    int m_offset_;
    const int m_length = 1; // 寄存器个数
    std::vector<uint16_t> m_rawData;
};

class Int32Data : public RegisterData {
  public:
    Int32Data(double factor = 1.0, int offset = 0);

    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override;
    std::string getData() const override;
    const std::vector<uint16_t> &getRawData() const override;
    const int getDataLength() const override;

    int getOffset() const override { return m_offset_; }
    double getConversionFactor() const override { return m_conversionFactor; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    double m_value;
    double m_conversionFactor;
    int m_offset_;
    const int m_length = 2; // 寄存器个数
    std::vector<uint16_t> m_rawData;
};

class UInt32Data : public RegisterData {
  public:
    UInt32Data(double factor = 1.0, int offset = 0);

    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override;
    std::string getData() const override;
    const std::vector<uint16_t> &getRawData() const override;
    const int getDataLength() const override;

    int getOffset() const override { return m_offset_; }
    double getConversionFactor() const override { return m_conversionFactor; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    double m_value;
    double m_conversionFactor;
    int m_offset_;
    const int m_length = 2; // 寄存器个数
    std::vector<uint16_t> m_rawData;
};

class BitData : public RegisterData {
  public:
    BitData(int len, double factor = 1.0);
    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override {}
    // 此类不适用该方法
    std::string getData() const override { return std::string(); }

    /**
     * @brief 获取bit值
     *
     * @param bitIndex bit值位置。
     * @param bitLength bit值所占长度。
     * @return 获取成功返回 bit值，失败则返回 空字符串。
     */
    std::string getData(size_t bitIndex, size_t bitLength) const;
    const std::vector<uint16_t> &getRawData() const override;
    void setOffset(size_t bitIndex, int offset);
    const int getDataLength() const override;

    int getOffset() const override { return m_length; }
    double getConversionFactor() const override { return m_conversionFactor; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    std::bitset<32> m_value;
    double m_conversionFactor;
    std::unordered_map<size_t, int> bitOffsets;
    int m_length; // 寄存器个数
    std::vector<uint16_t> m_rawData;
};

class StringData : public RegisterData {
  public:
    StringData(int len = 16);

    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override {}
    std::string getData() const override;
    const std::vector<uint16_t> &getRawData() const override;
    const int getDataLength() const override;

    int getOffset() const override { return 0; }
    double getConversionFactor() const override { return 0; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    std::string m_value;
    int m_length;
    std::vector<uint16_t> m_rawData;
};

class MultiUInt16Data : public RegisterData {
  public:
    MultiUInt16Data(size_t len);

    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override {}
    std::string getData() const override;
    const std::vector<uint16_t> &getRawData() const override;
    const int getDataLength() const override;

    int getOffset() const override { return 0; }
    double getConversionFactor() const override { return 0; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    std::vector<uint16_t> m_values;
    size_t m_length;
    std::vector<uint16_t> m_rawData;
};

class Float32Data : public RegisterData {
  public:
    Float32Data(double factor = 1.0, int offset = 0);

    void setData(const uint16_t *data) override;
    void setDataEndian(uint16_t *data) override;
    std::string getData() const override;
    const std::vector<uint16_t> &getRawData() const override;
    const int getDataLength() const override;

    int getOffset() const override { return m_offset_; }
    double getConversionFactor() const override { return m_conversionFactor; }
    size_t getSize() const override { return 16 * m_length; }

  private:
    float m_value;
    double m_conversionFactor;
    int m_offset_;
    const int m_length = 2; // 寄存器个数，32位浮点数需要2个16位寄存器
    std::vector<uint16_t> m_rawData;
};
#endif // REGISTERDATA_H