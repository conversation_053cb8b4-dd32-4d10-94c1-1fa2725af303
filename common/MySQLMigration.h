#ifndef MYSQL_MIGRATION_H
#define MYSQL_MIGRATION_H

#include "DatabaseAccess.h"
#include <memory>
#include <string>
#include <vector>

/**
 * @brief MySQL数据库迁移类，用于在MySQL数据库中创建必要的表结构
 */
class MySQLMigration {
  public:
    /**
     * @brief 获取MySQL数据库迁移类单例
     *
     * @return MySQLMigration& MySQL数据库迁移类单例
     */
    static MySQLMigration &getInstance() {
        static MySQLMigration instance;
        return instance;
    }

    /**
     * @brief 初始化MySQL数据库，创建必要的表结构
     *
     * @param dbAccess 数据库访问对象
     * @return bool 是否初始化成功
     */
    bool initializeDatabase(DatabaseAccess &dbAccess);

    /**
     * @brief 创建设备信息表
     *
     * @param dbAccess 数据库访问对象
     * @return bool 是否创建成功
     */
    bool createDeviceInfoTable(DatabaseAccess &dbAccess);

    /**
     * @brief 创建离线设备表
     *
     * @param dbAccess 数据库访问对象
     * @return bool 是否创建成功
     */
    bool createOfflineDeviceTable(DatabaseAccess &dbAccess);

    /**
     * @brief 创建电表数据表
     *
     * @param dbAccess 数据库访问对象
     * @return bool 是否创建成功
     */
    bool createMeterDataTable(DatabaseAccess &dbAccess);

    /**
     * @brief 创建设备数据表
     *
     * @param dbAccess 数据库访问对象
     * @param dbAddr 设备地址
     * @param monthStr 月份字符串
     * @param isTemp 是否为临时设备
     * @return bool 是否创建成功
     */
    bool createDeviceTable(DatabaseAccess &dbAccess, uint32_t dbAddr, const std::string &monthStr, bool isTemp = false);

  private:
    /**
     * @brief 构造函数
     */
    MySQLMigration() = default;

    /**
     * @brief 析构函数
     */
    ~MySQLMigration() = default;

    // 禁止拷贝和赋值
    MySQLMigration(const MySQLMigration &) = delete;
    MySQLMigration &operator=(const MySQLMigration &) = delete;

    /**
     * @brief 执行SQL语句
     *
     * @param dbAccess 数据库访问对象
     * @param sql SQL语句
     * @return bool 是否执行成功
     */
    bool executeSql(DatabaseAccess &dbAccess, const std::string &sql);
};

#endif // MYSQL_MIGRATION_H