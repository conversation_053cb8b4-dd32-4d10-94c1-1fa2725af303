#ifndef IDATABASE_INTERFACE_H
#define IDATABASE_INTERFACE_H

#include <string>
#include <vector>
#include <utility>

/**
 * @brief 数据库接口类，定义了数据库操作的通用接口
 */
class IDatabaseInterface {
  public:
    virtual ~IDatabaseInterface() = default;

    /**
     * @brief 执行查询操作
     *
     * @param query SQL查询语句
     * @return std::vector<std::vector<std::string>> 查询结果
     */
    virtual std::vector<std::vector<std::string>> query(const std::string &query) = 0;

    /**
     * @brief 执行非查询操作
     *
     * @param query SQL非查询语句
     */
    virtual void executeWithLock(const std::string &query) = 0;

    /**
     * @brief 插入数据
     *
     * @param table 表名
     * @param values 要插入的列-值对
     */
    virtual void insert(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values) = 0;

    /**
     * @brief 更新数据
     *
     * @param table 表名
     * @param values 要更新的列-值对
     * @param condition 更新条件
     */
    virtual void update(const std::string &table,
                        const std::vector<std::pair<std::string, std::string>> &values,
                        const std::string &condition) = 0;

    /**
     * @brief 删除数据
     *
     * @param table 表名
     * @param condition 删除条件
     */
    virtual void remove(const std::string &table, const std::string &condition) = 0;

    /**
     * @brief 检查连接是否正常
     *
     * @return bool 连接是否正常
     */
    virtual bool isConnected() = 0;

    /**
     * @brief 重新连接数据库
     *
     * @return bool 是否重连成功
     */
    virtual bool reconnect() = 0;

    /**
     * @brief 获取最后执行的SQL语句
     *
     * @return std::string 最后执行的SQL语句
     */
    virtual std::string getLastExecutedSql() const = 0;

    /**
     * @brief 存入最新的sql语句
     *
     * @param sql 最新的sql语句
     */
    virtual void saveLastExecutedSql(const std::string &sql) = 0;
};

#endif // IDATABASE_INTERFACE_H