#include "ModbusTcpClient.h"
#include "Logger.h"
#include <unistd.h>
#include <fcntl.h>
#include <fstream>
#include <sstream>
#include <regex>

ModbusTcpClient::ModbusTcpClient(const std::string &ip, int port)
    : m_ip_(ip), m_port_(port), m_ctx_(nullptr), m_connected_(false), m_valid_(true) {
    m_ctx_ = modbus_new_tcp(ip.c_str(), port);

    if (m_ctx_ != nullptr) {
        modbus_set_slave(m_ctx_, 1);
    } else {
        LOG_ERROR << "Unable to allocate libmodbus context";
    }

    // modbus_set_debug(m_ctx_, TRUE);
}

ModbusTcpClient::~ModbusTcpClient() {
    disconnect();
}

bool ModbusTcpClient::connect() {
    if (m_ctx_ == nullptr) {
        return false;
    }
    if (modbus_connect(m_ctx_) == -1) {
        LOG_ERROR << "Connection failed: " << modbus_strerror(errno);
        modbus_free(m_ctx_); // 释放上下文
        return false;
    }

    // 设置响应超时时间为 500 毫秒
    struct timeval timeout;
    timeout.tv_sec = 0;       // 秒
    timeout.tv_usec = 500000; // 微秒

    // 如果设置响应超时失败，清理资源
    if (modbus_set_response_timeout(m_ctx_, timeout.tv_sec, timeout.tv_usec) == -1) {
        LOG_ERROR << "Failed to set response timeout: " << modbus_strerror(errno);
        modbus_close(m_ctx_); // 关闭连接
        modbus_free(m_ctx_);  // 释放上下文
        m_ctx_ = nullptr;
        return false;
    }

    m_connected_ = true;
    return true;
}

void ModbusTcpClient::disconnect() {
    if (m_connected_) {
        modbus_close(m_ctx_);
        modbus_free(m_ctx_);
        m_connected_ = false;
        m_ctx_ = nullptr; // 避免悬挂指针
    }
}

bool ModbusTcpClient::reConnect() {
    // 确保在创建新 m_ctx_ 之前释放旧的 m_ctx_
    if (m_ctx_ != nullptr) {
        modbus_free(m_ctx_);
        m_ctx_ = nullptr;
    }

    m_ctx_ = modbus_new_tcp(m_ip_.c_str(), m_port_);

    if (m_ctx_ != nullptr) {
        modbus_set_slave(m_ctx_, 1);
    } else {
        LOG_ERROR << "Unable to allocate libmodbus context";
        return false;
    }

    if (modbus_connect(m_ctx_) == -1) {
        LOG_ERROR << "Connection failed: " << modbus_strerror(errno);
        modbus_free(m_ctx_);
        m_ctx_ = nullptr;
        return false;
    }

    // 设置响应超时时间为 100 毫秒
    struct timeval timeout;
    timeout.tv_sec = 0;       // 秒
    timeout.tv_usec = 100000; // 微秒

    // 如果设置响应超时失败，清理资源
    if (modbus_set_response_timeout(m_ctx_, timeout.tv_sec, timeout.tv_usec) == -1) {
        LOG_ERROR << "Failed to set response timeout: " << modbus_strerror(errno);
        modbus_close(m_ctx_); // 关闭连接
        modbus_free(m_ctx_);  // 释放上下文
        m_ctx_ = nullptr;
        return false;
    }

    m_connected_ = true;
    return true;
}

void ModbusTcpClient::reset() {
    LOG_ERROR << "ModbusTcpClient instance reset due to severe network error";
    delete this; // 删除当前实例
}

void ModbusTcpClient::monitorConnection() {
    std::lock_guard<std::mutex> lock(m_mutex_);
    // std::shared_lock<std::mutex> lock(m_mutex_);

    if (!m_connected_) {
        return;
    }

    // 检查连接
    uint16_t test = 0;
    int rc = modbus_read_registers(m_ctx_, 1, 1, &test);
    if (rc == -1) {
        if (errno == ECONNRESET || errno == ECONNREFUSED || errno == EHOSTUNREACH || errno == ENETDOWN ||
            errno == ENETUNREACH || errno == ETIMEDOUT || errno == EPIPE || errno == ENOTSOCK) {
            disconnect();
            m_valid_ = false;
        }
        LOG_ERROR << "Connection lost: " << modbus_strerror(errno);
    }
}

int ModbusTcpClient::readHoldingRegisters(int addr, int nb, std::vector<uint16_t> &dest) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    // std::shared_lock<std::mutex> lock(m_mutex_);

    if (!m_connected_) {
        LOG_ERROR << "Not connected to Modbus server";
        return -1;
    }

    dest.resize(nb);
    int rc;

    // 确保每次读取不超过125个字节
    for (int i = 0; i < nb; i += m_maxRegisters) {
        int current_nb = std::min(m_maxRegisters, nb - i);
        rc = modbus_read_registers(m_ctx_, addr + i, current_nb, &dest[i]);

        if (rc == -1) {
            LOG_ERROR << "读取失败 - 起始地址: " << (addr + i) << ", errno值: " << errno
                      << ", 错误描述: " << modbus_strerror(errno);

            // 如果是超时错误，清空socket缓冲区避免旧数据干扰
            if (errno == ETIMEDOUT) {
                modbus_flush(m_ctx_);
            }

            // 创建临时文件
            char temp_filename[] = "/tmp/modbus_debug_XXXXXX";
            int temp_fd = mkstemp(temp_filename);
            if (temp_fd != -1) {
                // 刷新所有输出流
                fflush(stdout);
                fflush(stderr);

                // 保存原始stdout和stderr
                int original_stdout = dup(STDOUT_FILENO);
                int original_stderr = dup(STDERR_FILENO);

                // 重定向stdout和stderr到临时文件
                dup2(temp_fd, STDOUT_FILENO);
                dup2(temp_fd, STDERR_FILENO);

                // 启用调试并执行操作
                modbus_set_debug(m_ctx_, TRUE);
                int debug_rc = modbus_read_registers(m_ctx_, addr + i, current_nb, &dest[i]);
                modbus_set_debug(m_ctx_, FALSE);

                // 强制刷新缓冲区
                fflush(stdout);
                fflush(stderr);
                fsync(temp_fd);

                // 恢复stdout和stderr
                dup2(original_stdout, STDOUT_FILENO);
                dup2(original_stderr, STDERR_FILENO);
                close(original_stdout);
                close(original_stderr);
                close(temp_fd);

                // 读取调试信息并输出到日志
                std::ifstream debug_file(temp_filename);
                std::ostringstream rawOutput;
                std::string line;
                while (std::getline(debug_file, line)) {
                    rawOutput << line << "\n";
                }
                debug_file.close();

                // 应用过滤器移除Logger输出
                std::string filteredOutput = filterModbusDebugOutput(rawOutput.str());
                if (!filteredOutput.empty()) {
                    // 按行输出过滤后的内容
                    std::istringstream filteredStream(filteredOutput);
                    while (std::getline(filteredStream, line)) {
                        LOG_ERROR << "调试数据包( 非本次报文，重发请求 ): " << line;
                    }
                }

                // 清理临时文件
                unlink(temp_filename);

            } else {
                LOG_ERROR << "无法创建临时文件进行调试";
            }

            if (errno == ECONNRESET || errno == ECONNREFUSED || errno == EHOSTUNREACH || errno == ENETDOWN ||
                errno == ENETUNREACH || errno == ETIMEDOUT || errno == EPIPE || errno == ENOTSOCK) {
                disconnect();
                m_valid_ = false;
            }
            return rc;
        }
    }
    return rc;
}

int ModbusTcpClient::readInputRegisters(int addr, int nb, std::vector<uint16_t> &dest) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    // std::shared_lock<std::mutex> lock(m_mutex_);

    if (!m_connected_) {
        LOG_ERROR << "Not connected to Modbus server";
        return -1;
    }

    dest.resize(nb);
    int rc;

    // 确保每次读取不超过125个字节
    for (int i = 0; i < nb; i += m_maxRegisters) {
        int current_nb = std::min(m_maxRegisters, nb - i);
        rc = modbus_read_input_registers(m_ctx_, addr + i, current_nb, &dest[i]);

        if (rc == -1) {
            LOG_ERROR << "读取失败 - 起始地址: " << (addr + i) << ", errno值: " << errno
                      << ", 错误描述: " << modbus_strerror(errno);

            // 如果是超时错误，清空socket缓冲区避免旧数据干扰
            if (errno == ETIMEDOUT) {
                modbus_flush(m_ctx_);
            }

            // 创建临时文件
            char temp_filename[] = "/tmp/modbus_debug_XXXXXXX";
            int temp_fd = mkstemp(temp_filename);
            if (temp_fd != -1) {
                // 刷新所有输出流
                fflush(stdout);
                fflush(stderr);

                // 保存原始stdout和stderr
                int original_stdout = dup(STDOUT_FILENO);
                int original_stderr = dup(STDERR_FILENO);

                // 重定向stdout和stderr到临时文件
                dup2(temp_fd, STDOUT_FILENO);
                dup2(temp_fd, STDERR_FILENO);

                // 启用调试并执行操作
                modbus_set_debug(m_ctx_, TRUE);
                int debug_rc = modbus_read_input_registers(m_ctx_, addr + i, current_nb, &dest[i]);
                modbus_set_debug(m_ctx_, FALSE);

                // 强制刷新缓冲区
                fflush(stdout);
                fflush(stderr);
                fsync(temp_fd);

                // 恢复stdout和stderr
                dup2(original_stdout, STDOUT_FILENO);
                dup2(original_stderr, STDERR_FILENO);
                close(original_stdout);
                close(original_stderr);
                close(temp_fd);

                // 读取调试信息并输出到日志
                std::ifstream debug_file(temp_filename);
                std::ostringstream rawOutput;
                std::string line;
                while (std::getline(debug_file, line)) {
                    rawOutput << line << "\n";
                }
                debug_file.close();

                // 应用过滤器移除Logger输出
                std::string filteredOutput = filterModbusDebugOutput(rawOutput.str());
                if (!filteredOutput.empty()) {
                    // 按行输出过滤后的内容
                    std::istringstream filteredStream(filteredOutput);
                    while (std::getline(filteredStream, line)) {
                        LOG_ERROR << "调试数据包( 非本次报文，重发请求 ): " << line;
                    }
                }

                // 清理临时文件
                unlink(temp_filename);

            } else {
                LOG_ERROR << "无法创建临时文件进行调试";
            }

            if (errno == ECONNRESET || errno == ECONNREFUSED || errno == EHOSTUNREACH || errno == ENETDOWN ||
                errno == ENETUNREACH || errno == ETIMEDOUT || errno == EPIPE || errno == ENOTSOCK) {
                disconnect();
                m_valid_ = false;
            }
            return rc;
        }
    }
    return rc;
}

bool ModbusTcpClient::writeSingleRegister(int addr, uint16_t value) {
    // std::shared_lock<std::mutex> lock(m_mutex_);
    std::lock_guard<std::mutex> lock(m_mutex_);

    if (!m_connected_) {
        LOG_ERROR << "Not connected to Modbus server";
        return false;
    }

    int rc = modbus_write_register(m_ctx_, addr, value);
    if (rc == -1) {
        LOG_ERROR << "写入失败 - errno值: " << errno << ", 错误描述: " << modbus_strerror(errno);
        if (errno == ECONNRESET || errno == ECONNREFUSED || errno == EHOSTUNREACH || errno == ENETDOWN ||
            errno == ENETUNREACH || errno == ETIMEDOUT || errno == EPIPE || errno == ENOTSOCK) {
            disconnect();
            m_valid_ = false;
        }
        return false;
    }
    return true;
}

bool ModbusTcpClient::writeMultipleRegisters(int addr, const std::vector<uint16_t> &values) {
    // std::shared_lock<std::mutex> lock(m_mutex_);
    std::lock_guard<std::mutex> lock(m_mutex_);

    if (!m_connected_) {
        LOG_ERROR << "Not connected to Modbus server";
        return false;
    }

    int rc = modbus_write_registers(m_ctx_, addr, values.size(), values.data());
    if (rc == -1) {
        LOG_ERROR << "写入失败 - errno值: " << errno << ", 错误描述: " << modbus_strerror(errno);
        if (errno == ECONNRESET || errno == ECONNREFUSED || errno == EHOSTUNREACH || errno == ENETDOWN ||
            errno == ENETUNREACH || errno == ETIMEDOUT || errno == EPIPE || errno == ENOTSOCK) {
            disconnect();
            m_valid_ = false;
        }
        return false;
    }
    return true;
}

std::string ModbusTcpClient::filterModbusDebugOutput(const std::string &rawOutput) {
    std::istringstream iss(rawOutput);
    std::ostringstream filtered;
    std::string line;

    // 定义Logger输出的正则表达式模式
    // 匹配格式：YYYY-MM-DD HH:MM:SS.mmm [level] [file:line]: message
    std::regex loggerPattern(R"(^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3} \[(trace|debug|info|warning|error|critical)\])");

    while (std::getline(iss, line)) {
        // 跳过空行
        if (line.empty()) {
            continue;
        }

        // 检查是否是Logger输出格式
        if (std::regex_search(line, loggerPattern)) {
            continue; // 跳过Logger输出行
        }

        // 保留非Logger输出的行（真正的Modbus调试信息）
        if (!filtered.str().empty()) {
            filtered << "\n";
        }
        filtered << line;
    }

    return filtered.str();
}