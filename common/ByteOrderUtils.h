#ifndef BYTE_ORDER_UTILS_H
#define BYTE_ORDER_UTILS_H

#include <cstdint>
#include <vector>

class ByteOrderUtils {
  public:
    // 获取单例实例
    static ByteOrderUtils &getInstance();

    // 判断当前系统是否为小端序
    bool isLittleEndian() const;

    // 16位整数 - 主机字节序转大端序
    uint16_t hostToBigEndian16(uint16_t value) const;

    // 16位整数 - 大端序转主机字节序
    uint16_t bigEndianToHost16(uint16_t value) const;

    // 32位整数 - 主机字节序转大端序
    uint32_t hostToBigEndian32(uint32_t value) const;

    // 32位整数 - 大端序转主机字节序
    uint32_t bigEndianToHost32(uint32_t value) const;

    // 将寄存器数据（uint16_t数组）转换为大端序字节数组
    std::vector<uint8_t> registersToBytesBigEndian(const uint16_t *registers, size_t count) const;

    // 从大端序字节数组转换为寄存器数据（uint16_t数组）
    std::vector<uint16_t> bytesToRegistersBigEndian(const uint8_t *bytes, size_t byteCount) const;

    // 禁用拷贝构造和赋值操作
    ByteOrderUtils(const ByteOrderUtils &) = delete;
    ByteOrderUtils &operator=(const ByteOrderUtils &) = delete;

  private:
    // 私有构造函数
    ByteOrderUtils() = default;

    // 私有析构函数
    ~ByteOrderUtils() = default;
};

#endif // BYTE_ORDER_UTILS_H