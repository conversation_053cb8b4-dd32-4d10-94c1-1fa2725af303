#ifndef DATABASE_FACTORY_H
#define DATABASE_FACTORY_H

#include "IDatabaseInterface.h"
#include "ConnectionPool.h"
#include <memory>
#include <string>
#include <mutex>

/**
 * @brief 数据库配置类
 */
class DBConfig {
  public:
    enum class DBType { SQLITE, MYSQL };

    DBType type;

    // SQLite配置
    std::string sqliteFilePath;

    // MySQL配置
    std::string mysqlHost;
    std::string mysqlUser;
    std::string mysqlPassword;
    std::string mysqlDatabase;
    unsigned int mysqlPort;
    std::string mysqlCharset;
    std::string mysqlConfigFile;

    // 连接池配置
    size_t maxConnections = 10;
    int connectionTimeout = 30000; // 毫秒
};

/**
 * @brief 数据库工厂类，用于创建数据库实例
 */
class DatabaseFactory {
  public:
    /**
     * @brief 创建数据库实例
     *
     * @param config 数据库配置
     * @return std::shared_ptr<IDatabaseInterface> 数据库接口指针
     */
    static std::shared_ptr<IDatabaseInterface> createDatabase(const DBConfig &config);

    /**
     * @brief 从配置文件创建数据库实例
     *
     * @return std::shared_ptr<IDatabaseInterface> 数据库接口指针
     */
    static std::shared_ptr<IDatabaseInterface> createDatabaseFromConfig();

    /**
     * @brief 创建数据库连接池
     *
     * @param config 数据库配置
     * @return std::shared_ptr<IConnectionPool> 连接池接口指针
     */
    static std::shared_ptr<IConnectionPool> createConnectionPool(const DBConfig &config);

    /**
     * @brief 从配置文件创建数据库连接池
     *
     * @return std::shared_ptr<IConnectionPool> 连接池接口指针
     */
    static std::shared_ptr<IConnectionPool> createConnectionPoolFromConfig();

    /**
     * @brief 获取数据库连接池单例
     *
     * @return std::shared_ptr<IConnectionPool> 连接池接口指针
     */
    static std::shared_ptr<IConnectionPool> getConnectionPool();

  private:
    static std::shared_ptr<IConnectionPool> s_connectionPool; // 连接池单例
    static std::once_flag s_initFlag;                         // 添加 once_flag 静态成员变量
};

#endif // DATABASE_FACTORY_H