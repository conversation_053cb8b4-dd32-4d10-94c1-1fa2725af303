#ifndef DATABASE_MONITOR_H
#define DATABASE_MONITOR_H

#include <atomic>
#include <chrono>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>
#include "ConnectionPool.h"
#include "IDatabaseInterface.h"
/**
 * @class DatabaseMonitor
 * @brief 数据库性能监控类
 *
 * 实现对MySQL数据库的实时性能监控、健康检查和维护功能。
 * 主要功能包括：
 * 1. 收集和分析数据库关键性能指标（连接、缓冲池、锁等）
 * 2. 监控慢查询并进行统计分析
 * 3. 自动执行数据库维护任务（优化表、更新统计信息等）
 * 4. 提供健康状态报告和优化建议
 *
 * 设计为单例模式，确保全局只有一个监控实例。
 */
class DatabaseMonitor {
  public:
    /**
     * @brief 获取单例实例
     * @return DatabaseMonitor& 监控器实例引用
     */
    static DatabaseMonitor &getInstance() {
        static DatabaseMonitor instance;
        return instance;
    }

    /**
     * @brief 启动监控，指定连接池和间隔时间（秒）
     *
     * @param connectionPool 数据库连接池
     * @param interval 监控间隔时间（秒）
     */
    void startMonitoring(std::shared_ptr<IConnectionPool> connectionPool, int interval = 60);

    /**
     * @brief 停止监控
     */
    void stopMonitoring();

    /**
     * @brief 获取连接池状态
     *
     * @return std::string 连接池状态信息
     */
    std::string getPoolStatus() const;

    /**
     * @brief 获取查询性能统计
     *
     * @return std::string 查询性能统计信息
     */
    std::string getQueryStats() const;

    /**
     * @brief 记录查询执行时间
     *
     * @param query 查询语句
     * @param duration 执行时间（毫秒）
     */
    void recordQueryTime(const std::string &query, long duration);

    /**
     * @brief 获取慢查询列表
     *
     * @param threshold 慢查询阈值（毫秒）
     * @return std::vector<std::pair<std::string, long>> 慢查询列表
     */
    std::vector<std::pair<std::string, long>> getSlowQueries(long threshold = 1000) const;

    /**
     * @brief 监控任务函数
     */
    void monitorTask();

    /**
     * @struct ConnectionMetrics
     * @brief 数据库连接相关指标
     */
    struct ConnectionMetrics {
        int threadsConnected;   // 当前连接数
        int threadsRunning;     // 当前运行线程数
        int totalConnections;   // 总连接尝试次数
        int maxUsedConnections; // 最大并发连接数
        int maxConnections;     // 最大允许连接数
    };

    /**
     * @brief 获取连接指标
     * @return ConnectionMetrics 当前连接指标
     */
    ConnectionMetrics getConnectionMetrics() const;

    /**
     * @struct BufferPoolMetrics
     * @brief 缓冲池相关指标
     */
    struct BufferPoolMetrics {
        long reads;        // 从磁盘读取的页数
        long readRequests; // 从缓冲池读取的页请求数
        long pagesFree;    // 缓冲池中的空闲页数
        long pagesTotal;   // 缓冲池总页数
        double hitRatio;   // 缓冲池命中率
    };

    /**
     * @brief 获取缓冲池指标
     * @return BufferPoolMetrics 当前缓冲池指标
     */
    BufferPoolMetrics getBufferPoolMetrics() const;

    /**
     * @struct LockMetrics
     * @brief 锁相关指标
     */
    struct LockMetrics {
        long rowLockWaits;   // 行锁等待次数
        long rowLockTimeAvg; // 行锁平均等待时间
        long tableLockWaits; // 表锁等待次数
    };

    /**
     * @brief 获取锁指标
     * @return LockMetrics 当前锁指标
     */
    LockMetrics getLockMetrics() const;

    /**
     * @brief 优化表结构
     * @param tableName 要优化的表名
     * @return 操作是否成功
     */
    bool optimizeTable(const std::string &tableName);

    /**
     * @brief 更新表统计信息
     * @param tableName 要分析的表名
     * @return 操作是否成功
     */
    bool analyzeTable(const std::string &tableName);

    /**
     * @brief 检查表完整性
     * @param tableName 要检查的表名
     * @return 表是否完整无损
     */
    bool checkTable(const std::string &tableName);

    /**
     * @brief 修复损坏的表
     * @param tableName 要修复的表名
     * @return 修复是否成功
     */
    bool repairTable(const std::string &tableName);

    /**
     * @struct AlertThresholds
     * @brief 性能警报阈值设置
     */
    struct AlertThresholds {
        int maxConnectionsPercent = 80;      // 连接数占最大允许连接数的百分比
        double minBufferPoolHitRatio = 90.0; // 最小缓冲池命中率
        long maxRowLockWaits = 10;           // 最大行锁等待次数
        long maxSlowQueryCount = 10;         // 最大慢查询数量
        long slowQueryThreshold = 1000;      // 慢查询阈值(毫秒)
    };

    /**
     * @brief 设置性能警报阈值
     * @param thresholds 警报阈值设置
     */
    void setAlertThresholds(const AlertThresholds &thresholds);

    /**
     * @struct HealthReport
     * @brief 数据库健康状态报告
     */
    struct HealthReport {
        bool healthy;                             // 整体健康状态
        std::vector<std::string> warnings;        // 警告列表
        std::vector<std::string> recommendations; // 优化建议
    };

    /**
     * @brief 获取数据库健康状态报告
     * @return HealthReport 健康状态报告
     */
    HealthReport getDatabaseHealthReport() const;

    /**
     * @brief 获取完整的指标报告
     *
     * 汇总所有监控指标，包括连接池状态、查询统计、性能指标等，生成可读性好的报告
     *
     * @param includeQueryStats 是否包含详细的查询统计信息
     * @return 格式化的完整指标报告
     */
    std::string getMetricsReport(bool includeQueryStats = true) const;

  private:
    /**
     * @brief 构造函数
     */
    DatabaseMonitor();

    /**
     * @brief 析构函数
     */
    ~DatabaseMonitor() {
        stopMonitoring();
    }

    // 禁止拷贝和赋值
    DatabaseMonitor(const DatabaseMonitor &) = delete;
    DatabaseMonitor &operator=(const DatabaseMonitor &) = delete;

    std::shared_ptr<IConnectionPool> m_connectionPool; // 连接池
    std::atomic<bool> m_isRunning;                     // 是否正在运行
    int m_interval;                                    // 监控间隔（毫秒）

    // 查询性能统计
    mutable std::mutex m_statsMutex;                                 // 统计信息互斥锁
    std::unordered_map<std::string, std::vector<long>> m_queryTimes; // 查询执行时间统计

    // 性能指标缓存
    mutable std::mutex m_metricsMutex;     // 性能指标互斥锁
    ConnectionMetrics m_connectionMetrics; // 连接指标
    BufferPoolMetrics m_bufferPoolMetrics; // 缓冲池指标
    LockMetrics m_lockMetrics;             // 锁指标
    AlertThresholds m_alertThresholds;     // 告警阈值设置

    std::chrono::time_point<std::chrono::steady_clock> m_lastMonitorTime;       // 上次监控时间
    std::chrono::time_point<std::chrono::steady_clock> m_lastMetricsUpdateTime; // 上次指标更新时间

    /**
     * @brief 更新性能指标
     */
    void updateMetrics();

    /**
     * @brief 检查是否需要发送警报
     */
    void checkAlerts();

    /**
     * @brief 定期维护任务
     */
    void scheduleMaintenance();

    /**
     * @brief 执行SQL并获取结果
     *
     * @param query 要执行的SQL查询
     * @return 查询结果的二维映射表
     */
    std::vector<std::unordered_map<std::string, std::string>> executeQuery(const std::string &query);

    /**
     * @brief 打印当前的指标报告
     *
     * @param printQueryStats 是否打印详细的查询统计
     */
    void printMetricsReport(bool printQueryStats = false);
};

#endif // DATABASE_MONITOR_H