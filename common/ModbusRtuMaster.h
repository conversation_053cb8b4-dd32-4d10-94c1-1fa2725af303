#ifndef MODBUSRTUSLAVE_H
#define MODBUSRTUSLAVE_H

#include <atomic>
#include <modbus/modbus.h>
#include <mutex>
#include <string>
#include <vector>

/**
 * @brief Modbus RTU 客户端类，用于与 Modbus RTU 设备通信
 */
class ModbusRtuMaster {
  public:
    /**
     * @brief 构造函数，初始化 ModbusRtuMaster 对象
     * @param device 串口设备名，例如 "/dev/ttyUSB0"
     * @param baud 波特率
     * @param parity 校验方式，'N'（无校验）、'E'（偶校验）、'O'（奇校验）
     * @param data_bit 数据位，7 或 8
     * @param stop_bit 停止位，1 或 2
     */
    ModbusRtuMaster(const std::string &device, int baud, char parity, int data_bit, int stop_bit, int slaveId);

    /**
     * @brief 析构函数，释放资源
     */
    ~ModbusRtuMaster();

    /**
     * @brief 连接到 Modbus 设备
     * @return 成功则返回 true，否则返回 false
     */
    bool connect();

    /**
     * @brief 断开与 Modbus 设备的连接
     */
    void disconnect();

    /**
     * @brief 重新连接到 Modbus 设备
     * @return 成功则返回 true，否则返回 false
     */
    bool reConnect();

    /**
     * @brief 检查是否已连接到 Modbus 设备
     * @return 已连接则返回 true，否则返回 false
     */
    inline bool isConnected() const { return m_connected_; };

    /**
     * @brief 读取保持寄存器
     * @param addr 起始地址
     * @param nb 读取的寄存器数量
     * @param dest 存储读取结果的向量
     * @return 读取成功则返回读取长度，否则返回 -1
     */
    int readHoldingRegisters(int addr, int nb, std::vector<uint16_t> &dest);

    /**
     * @brief 读取输入寄存器
     * @param addr 起始地址
     * @param nb 读取的寄存器数量
     * @param dest 存储读取结果的向量
     * @return 读取成功则返回读取长度，否则返回 -1
     */
    int readInputRegisters(int addr, int nb, std::vector<uint16_t> &dest);

    /**
     * @brief 写单个保持寄存器
     * @param addr 寄存器地址
     * @param value 要写入的值
     * @return 写入成功则返回 true，否则返回 false
     */
    bool writeSingleRegister(int addr, uint16_t value);

    /**
     * @brief 写多个保持寄存器
     * @param addr 起始地址
     * @param values 要写入的值的向量
     * @return 写入成功则返回 true，否则返回 false
     */
    bool writeMultipleRegisters(int addr, const std::vector<uint16_t> &values);

    /**
     * @brief 监控连接状态，检测是否出现错误
     */
    bool monitorConnection();

  private:
    std::string m_device_;
    int m_baud_;
    char m_parity_;
    int m_data_bit_;
    int m_stop_bit_;
    int m_slaveId;

    modbus_t *m_ctx_ = nullptr;
    std::atomic<bool> m_connected_;
    const int m_maxRegisters = 125;

    std::atomic<bool> m_isReadAble;
    std::mutex m_mutex_;
};

#endif // MODBUSRTUSLAVE_H