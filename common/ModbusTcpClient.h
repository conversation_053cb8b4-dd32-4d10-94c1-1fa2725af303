#ifndef MODBUS_TCP_CLIENT_H
#define MODBUS_TCP_CLIENT_H

#include <atomic>
#include <modbus/modbus.h>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <vector>

class ModbusTcpClient {
  public:
    /**
     * @brief 构造函数，初始化 ModbusTcpClient 对象
     * @param ip Modbus 服务器的 IP 地址
     * @param port Modbus 服务器的端口号
     */
    ModbusTcpClient(const std::string &ip, int port);

    /**
     * @brief 析构函数，释放资源
     */
    ~ModbusTcpClient();

    /**
     * @brief 连接到 Modbus 服务器
     * @return 成功则返回 true，否则返回 false
     */
    bool connect();

    /**
     * @brief 断开与 Modbus 服务器的连接
     */
    void disconnect();

    bool reConnect();

    /**
     * @brief 释放实例内存
     * 注：外部使用指向该实例的智能指针时，会自动释放实例内存，
     * 请慎重考虑使用该方法，避免重复释放导致报错，尤其是unique_ptr一定不能使用
     */
    void reset();

    /**
     * @brief 检查是否已连接到 Modbus 服务器
     * @return 已连接则返回 true，否则返回 false
     */
    inline bool isConnected() const {
        return m_connected_;
    };

    /**
     * @brief 获取 Modbus 服务器的 IP 地址
     * @return 返回 IP 地址
     */
    std::string getIp() const {
        return m_ip_;
    };

    /**
     * @brief 读取保持寄存器
     * @param addr 起始地址
     * @param nb 读取的寄存器数量
     * @param dest 存储读取结果的向量
     * @return 读取成功则返回 读取长度，否则返回 -1
     */
    int readHoldingRegisters(int addr, int nb, std::vector<uint16_t> &dest);

    /**
     * @brief 读取输入寄存器
     * @param addr 起始地址
     * @param nb 读取的寄存器数量
     * @param dest 存储读取结果的向量
     * @return 读取成功则返回 读取长度，否则返回 -1
     */
    int readInputRegisters(int addr, int nb, std::vector<uint16_t> &dest);

    /**
     * @brief 写单个保持寄存器
     * @param addr 寄存器地址
     * @param value 要写入的值
     * @return 写入成功则返回 true，否则返回 false
     */
    bool writeSingleRegister(int addr, uint16_t value);

    /**
     * @brief 写多个保持寄存器
     * @param addr 起始地址
     * @param values 要写入的值的向量
     * @return 写入成功则返回 true，否则返回 false
     */
    bool writeMultipleRegisters(int addr, const std::vector<uint16_t> &values);

    inline bool checkValid() const {
        return m_valid_;
    };

    void monitorConnection();

  private:
    std::string m_ip_;
    int m_port_;
    modbus_t *m_ctx_ = nullptr;
    std::atomic<bool> m_connected_;
    const int m_maxRegisters = 125;
    std::atomic<bool> m_valid_; // 实例可用标识

    mutable std::mutex m_mutex_;

    /**
     * @brief 过滤Modbus调试输出，移除Logger系统输出
     * @param rawOutput 原始调试输出字符串
     * @return 过滤后的调试输出字符串
     */
    static std::string filterModbusDebugOutput(const std::string &rawOutput);
};

#endif // MODBUS_TCP_CLIENT_H