#ifndef IMYSQL_DB_H
#define IMYSQL_DB_H

#include "IDatabaseInterface.h"
#include <mysql/mysql.h>
#include <mutex>
#include <stdexcept>
#include <string>
#include <vector>

/**
 * @brief MySQL数据库接口类
 */
class IMySQLDB : public IDatabaseInterface {
  public:
    /**
     * @brief 构造函数
     *
     * @param host 数据库主机地址
     * @param user 用户名
     * @param password 密码
     * @param database 数据库名
     * @param port 端口号
     * @param configFile MySQL配置文件路径
     */
    IMySQLDB(const std::string &host,
             const std::string &user,
             const std::string &password,
             const std::string &database,
             const std::string &configFile,
             const std::string &charset,
             unsigned int port = 3306);

    /**
     * @brief 虚析构函数
     */
    virtual ~IMySQLDB() {
        close();
    }

    // 禁止拷贝
    IMySQLDB(const IMySQLDB &) = delete;
    IMySQLDB &operator=(const IMySQLDB &) = delete;

    // 允许移动
    IMySQLDB(IMySQLDB &&) noexcept = default;
    IMySQLDB &operator=(IMySQLDB &&) noexcept = default;

    /**
     * @brief 带锁的执行非查询 SQL 命令（例如 CREATE、INSERT、UPDATE、DELETE）。
     *
     * @param query 要执行的 SQL 命令。
     * @throws std::runtime_error 如果 SQL 命令执行失败。
     */
    void executeWithLock(const std::string &query) override;

    /**
     * @brief 执行 SQL 查询并返回结果。
     *
     * @param query 要执行的 SQL 查询。
     * @return 查询结果，返回一个行向量，其中每行是一个字符串向量。
     * @throws std::runtime_error 如果 SQL 查询执行失败。
     */
    std::vector<std::vector<std::string>> query(const std::string &query) override;

    /**
     * @brief 向表中插入数据。
     *
     * @param table 表名。
     * @param values 要插入的列-值对向量。
     */
    void insert(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values) override;

    /**
     * @brief 更新表中的数据。
     *
     * @param table 表名。
     * @param values 要更新的列-值对向量。
     * @param condition 更新的SQL条件。
     */
    void update(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values, const std::string &condition) override;

    /**
     * @brief 从表中删除数据。
     *
     * @param table 表名。
     * @param condition 删除的SQL条件。
     */
    void remove(const std::string &table, const std::string &condition) override;

    /**
     * @brief 检查数据库连接是否正常
     *
     * @return bool 连接正常返回true，否则返回false
     */
    bool isConnected() override;

    /**
     * @brief 重新连接数据库
     *
     * @return bool 连接成功返回true，否则返回false
     */
    bool reconnect() override;

    /**
     * @brief 获取上次插入操作的ID
     *
     * @return unsigned long 上次插入的ID
     */
    unsigned long getLastInsertId();

    /**
     * @brief 获取上次操作影响的行数
     *
     * @return unsigned long 影响的行数
     */
    unsigned long getAffectedRows();

    /**
     * @brief 执行ping操作，检查连接是否活跃
     *
     * @return bool ping成功返回true，否则返回false
     */
    bool ping();

    /**
     * @brief 获取最后执行的SQL语句
     *
     * @return std::string 最后执行的SQL语句
     */
    std::string getLastExecutedSql() const override;

    /**
     * @brief 存入最新的sql语句
     *
     * @param sql 最新的sql语句
     */
    void saveLastExecutedSql(const std::string &sql) override;

  protected:
    MYSQL *m_mysql;
    mutable std::mutex m_db_mutex;
    std::string m_host;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    unsigned int m_port;
    std::string m_configFile;
    std::string m_charset;
    std::string m_lastExecutedSql; // 存储最后执行的SQL语句

    /**
     * @brief 打开数据库连接
     *
     * @param connectionString 连接字符串
     * @throws std::runtime_error 如果数据库无法打开
     */
    void open(const std::string &connectionString);

    /**
     * @brief 关闭数据库连接
     */
    void close();

    /**
     * @brief 执行非查询 SQL 命令
     *
     * @param query SQL命令
     * @throws std::runtime_error 如果SQL命令执行失败
     */
    void execute(const std::string &query);

    /**
     * @brief 转义SQL字符串，防止SQL注入
     *
     * @param str 需要转义的字符串
     * @return std::string 转义后的字符串
     */
    std::string escapeString(const std::string &str);
};

#endif // IMYSQL_DB_H