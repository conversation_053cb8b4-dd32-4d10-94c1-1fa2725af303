#include "ModbusRtuMaster.h"
#include "Logger.h"
#include <algorithm>
#include <cstring>
#include <errno.h>

ModbusRtuMaster::ModbusRtuMaster(const std::string &device, int baud, char parity, int data_bit, int stop_bit, int slaveId)
    : m_device_(device), m_baud_(baud), m_parity_(parity), m_data_bit_(data_bit), m_stop_bit_(stop_bit),
      m_ctx_(nullptr), m_connected_(false), m_slaveId(slaveId), m_isReadAble(false) {
    m_ctx_ = modbus_new_rtu(device.c_str(), baud, parity, data_bit, stop_bit);
    if (m_ctx_ != nullptr) {
        modbus_set_slave(m_ctx_, slaveId); // 设置从站地址
    } else {
        LOG_ERROR << "无法分配 libmodbus 上下文";
    }
}

ModbusRtuMaster::~ModbusRtuMaster() {
    disconnect();
}

bool ModbusRtuMaster::connect() {
    if (m_ctx_ == nullptr) {
        return false;
    }
    if (modbus_connect(m_ctx_) == -1) {
        LOG_ERROR << "连接失败: " << modbus_strerror(errno);
        modbus_free(m_ctx_);
        m_ctx_ = nullptr;
        return false;
    }

    // // 设置响应超时时间为 1 秒
    // struct timeval response_timeout;
    // response_timeout.tv_sec = 1;
    // response_timeout.tv_usec = 0;

    // if (modbus_set_response_timeout(m_ctx_, response_timeout.tv_sec, response_timeout.tv_usec) == -1) {
    //     std::cerr << "设置响应超时失败: " << modbus_strerror(errno) << std::endl;
    //     modbus_close(m_ctx_);
    //     modbus_free(m_ctx_);
    //     m_ctx_ = nullptr;
    //     return false;
    // }

    m_connected_ = true;
    return true;
}

void ModbusRtuMaster::disconnect() {
    if (m_connected_) {
        modbus_close(m_ctx_);
        modbus_free(m_ctx_);
        m_connected_ = false;
        m_ctx_ = nullptr;
    }
}

bool ModbusRtuMaster::reConnect() {
    disconnect();
    m_ctx_ = modbus_new_rtu(m_device_.c_str(), m_baud_, m_parity_, m_data_bit_, m_stop_bit_);
    if (m_ctx_ != nullptr) {
        modbus_set_slave(m_ctx_, 1);
    } else {
        LOG_ERROR << "无法分配 libmodbus 上下文";
        return false;
    }
    return connect();
}

bool ModbusRtuMaster::monitorConnection() {
    std::lock_guard<std::mutex> lock(m_mutex_);
    if (!m_connected_) {
        return false;
    }
    if (!m_isReadAble) {
        // 简单的测试读操作，检测连接是否正常
        std::vector<uint16_t> test(1);
        int rc = modbus_read_registers(m_ctx_, 0, 1, test.data());
        if (rc == -1) {
            if (errno == EIO || errno == EINVAL || errno == ETIMEDOUT || errno == EBADF || errno == ENODEV) {
                // std::cerr << "连接丢失: " << modbus_strerror(errno) << std::endl;
                return false;
            }
        }
        m_isReadAble = true;
        return true;
    }

    return true;
}

int ModbusRtuMaster::readHoldingRegisters(int addr, int nb, std::vector<uint16_t> &dest) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    if (!m_connected_) {
        if (!reConnect()) {
            LOG_ERROR << "未连接到 Modbus 设备";
            return -1;
        }
    }

    dest.resize(nb);
    int rc;
    for (int i = 0; i < nb; i += m_maxRegisters) {
        int current_nb = std::min(m_maxRegisters, nb - i);
        rc = modbus_read_registers(m_ctx_, addr + i, current_nb, &dest[i]);
        if (rc == -1) {
            LOG_ERROR << "读取失败: " << modbus_strerror(errno);
            if (errno == EIO || errno == EINVAL || errno == ETIMEDOUT || errno == EBADF || errno == ENODEV) {
                m_isReadAble = false;
                disconnect();
                return rc;
            }
        }
    }
    return rc;
}

int ModbusRtuMaster::readInputRegisters(int addr, int nb, std::vector<uint16_t> &dest) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    if (!m_connected_) {
        if (!reConnect()) {
            LOG_ERROR << "未连接到 Modbus 设备";
            return -1;
        }
    }

    dest.resize(nb);
    int rc;
    for (int i = 0; i < nb; i += m_maxRegisters) {
        int current_nb = std::min(m_maxRegisters, nb - i);
        rc = modbus_read_input_registers(m_ctx_, addr + i, current_nb, &dest[i]);
        if (rc == -1) {
            LOG_ERROR << "读取失败: " << modbus_strerror(errno);
            if (errno == EIO || errno == EINVAL || errno == ETIMEDOUT || errno == EBADF || errno == ENODEV) {
                m_isReadAble = false;
                disconnect();
                return rc;
            }
        }
    }
    return rc;
}

bool ModbusRtuMaster::writeSingleRegister(int addr, uint16_t value) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    if (!m_connected_) {
        if (!reConnect()) {
            m_isReadAble = false;
            LOG_ERROR << "未连接到 Modbus 设备";
            return -1;
        }
    }

    int rc = modbus_write_register(m_ctx_, addr, value);
    if (rc == -1) {
        LOG_ERROR << "写入失败: " << modbus_strerror(errno);
        if (errno == EIO || errno == EINVAL || errno == ETIMEDOUT || errno == EBADF || errno == ENODEV) {
            m_isReadAble = false;
            disconnect();
        }
        return false;
    }
    return true;
}

bool ModbusRtuMaster::writeMultipleRegisters(int addr, const std::vector<uint16_t> &values) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    if (!m_connected_) {
        if (!reConnect()) {
            LOG_ERROR << "未连接到 Modbus 设备";
            return -1;
        }
    }

    int rc = modbus_write_registers(m_ctx_, addr, values.size(), values.data());
    if (rc == -1) {
        LOG_ERROR << "写入失败: " << modbus_strerror(errno);
        if (errno == EIO || errno == EINVAL || errno == ETIMEDOUT || errno == EBADF || errno == ENODEV) {
            m_isReadAble = false;
            disconnect();
        }
        return false;
    }
    return true;
}