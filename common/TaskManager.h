#ifndef TASKMANAGER_H
#define TASKMANAGER_H

#include "Watchdog.h"
#include "StructDefs.h" // 添加对集中结构体定义文件的引用
#include <functional>
#include <string>
#include <thread>
#include <vector>
#include <map>

class TaskManager {
  public:
    TaskManager();
    ~TaskManager();
    void addTask(const std::vector<TaskConfig> &configs);
    void startAllTasks();

  private:
    void startTask(const TaskConfig &config, int taskId);
    std::map<int, TaskConfig> m_tasks_;        // 任务ID与任务配置的映射
    std::map<int, std::thread> m_taskThreads_; // 记录任务ID和对应线程的映射
    Watchdog m_watchdog_;
    int m_nextTaskId_ = 1; // 开始的任务ID
};

#endif // TASKMANAGER_H
