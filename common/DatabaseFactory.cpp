#include "DatabaseFactory.h"
#include "ConfigManager.h"
#include "Logger.h"
#include "iSQLiteDB.h"
#include "qMySQLDB.h"


// 初始化静态成员变量
// 如果静态成员在头文件中完全初始化，那么每个包含该头文件的cpp文件都会创建该变量的一个副本，导致链接错误
std::shared_ptr<IConnectionPool> DatabaseFactory::s_connectionPool = nullptr;
std::once_flag DatabaseFactory::s_initFlag;

std::shared_ptr<IDatabaseInterface> DatabaseFactory::createDatabase(const DBConfig &config) {
    try {
        switch (config.type) {
        case DBConfig::DBType::SQLITE:
            return std::make_shared<iSQLiteDB>(config.sqliteFilePath);

        case DBConfig::DBType::MYSQL:
            return std::make_shared<qMySQLDB>(config.mysqlHost,
                                              config.mysqlUser,
                                              config.mysqlPassword,
                                              config.mysqlDatabase,
                                              config.mysqlConfigFile,
                                              config.mysqlCharset,
                                              config.mysqlPort);
        default:
            throw std::runtime_error("未知的数据库类型");
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "创建数据库实例失败: " << e.what();
        throw;
    }
}

std::shared_ptr<IDatabaseInterface> DatabaseFactory::createDatabaseFromConfig() {
    try {
        ConfigManager &configManager = ConfigManager::getInstance();
        const YAML::Node &config = configManager.getConfig();

        std::string dbTypeStr = config["database"]["type"].as<std::string>("sqlite");
        if (dbTypeStr == "mysql") {
            return std::make_shared<qMySQLDB>(
                config["database"]["mysql"]["host"].as<std::string>("localhost"),
                config["database"]["mysql"]["user"].as<std::string>("root"),
                config["database"]["mysql"]["password"].as<std::string>(""),
                config["database"]["mysql"]["database"].as<std::string>("blockbox"),
                config["database"]["mysql"]["config_file"].as<std::string>("/etc/mysql/mysql.conf.d/mysqld.cnf"),
                config["database"]["mysql"]["charset"].as<std::string>("utf8mb4"),
                config["database"]["mysql"]["port"].as<unsigned int>(3306));
        } else {
            return std::make_shared<iSQLiteDB>(config["database"]["sqlite"]["filename"].as<std::string>("data.db"));
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "从配置文件创建数据库实例失败: " << e.what();
        throw;
    }
}

std::shared_ptr<IConnectionPool> DatabaseFactory::createConnectionPool(const DBConfig &config) {
    try {
        switch (config.type) {
        case DBConfig::DBType::SQLITE:
            return std::make_shared<ConnectionPool<iSQLiteDB>>(config.maxConnections, config.sqliteFilePath);
        case DBConfig::DBType::MYSQL:
            return std::make_shared<ConnectionPool<qMySQLDB>>(config.maxConnections,
                                                              config.mysqlHost,
                                                              config.mysqlUser,
                                                              config.mysqlPassword,
                                                              config.mysqlDatabase,
                                                              config.mysqlConfigFile,
                                                              config.mysqlCharset,
                                                              config.mysqlPort);

        default:
            throw std::runtime_error("未知的数据库类型");
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "创建数据库连接池失败: " << e.what();
        throw;
    }
}

std::shared_ptr<IConnectionPool> DatabaseFactory::createConnectionPoolFromConfig() {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    DBConfig dbConfig;

    // 读取数据库类型
    std::string dbTypeStr = config["database"]["type"].as<std::string>("sqlite");
    if (dbTypeStr == "mysql") {
        dbConfig.type = DBConfig::DBType::MYSQL;

        // 读取MySQL配置
        dbConfig.mysqlHost = config["database"]["mysql"]["host"].as<std::string>("localhost");
        dbConfig.mysqlUser = config["database"]["mysql"]["user"].as<std::string>("root");
        dbConfig.mysqlPassword = config["database"]["mysql"]["password"].as<std::string>("");
        dbConfig.mysqlDatabase = config["database"]["mysql"]["database"].as<std::string>("blockbox");
        dbConfig.mysqlPort = config["database"]["mysql"]["port"].as<unsigned int>(3306);
        dbConfig.mysqlCharset = config["database"]["mysql"]["charset"].as<std::string>("utf8mb4");
        dbConfig.mysqlConfigFile = config["database"]["mysql"]["config_file"].as<std::string>(
            "/etc/mysql/mysql.conf.d/mysqld.cnf");

        // 读取连接池配置
        dbConfig.maxConnections = config["database"]["mysql"]["max_connections"].as<size_t>(10);
        dbConfig.connectionTimeout = config["database"]["mysql"]["timeout"].as<int>(30) * 1000; // 转换为毫秒
    } else {
        dbConfig.type = DBConfig::DBType::SQLITE;

        // 读取SQLite配置
        dbConfig.sqliteFilePath = config["database"]["sqlite"]["filename"].as<std::string>("data.db");
    }

    return createConnectionPool(dbConfig);
}

std::shared_ptr<IConnectionPool> DatabaseFactory::getConnectionPool() {
    std::call_once(s_initFlag, []() {
        s_connectionPool = createConnectionPoolFromConfig();
    });
    return s_connectionPool;
}
