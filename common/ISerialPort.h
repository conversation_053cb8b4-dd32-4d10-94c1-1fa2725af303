#ifndef SERIALPORT_H
#define SERIALPORT_H

#include <iostream>
#include <string>

class ISerialPort {
  public:
    ISerialPort(const std::string &portName) : m_portName_(portName), m_fd_(-1) {}
    virtual ~ISerialPort() {}

    // 纯虚函数
    virtual void configurePort() = 0;
    virtual ssize_t writeData(const char *buffer, size_t size) = 0;
    virtual ssize_t readData(char *buffer, size_t size) = 0;

  protected:
    std::string m_portName_;
    int m_fd_;
    virtual void openPort() = 0;
};

#endif // SERIALPORT_H