#include "RegisterData.h"
#include "Logger.h"
#include <iostream> // 这行可以考虑删除，如果不再使用 std::cerr/cout

Int16Data::Int16Data(double factor, int offset) : m_conversionFactor(factor), m_offset_(offset) {
    m_rawData.resize(m_length);
}

void Int16Data::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    m_rawData[0] = data[0];
    m_value = (static_cast<int16_t>(data[0]) - m_offset_) * m_conversionFactor;
}

std::string Int16Data::getData() const {
    return std::to_string(m_value);
}

const std::vector<uint16_t> &Int16Data::getRawData() const {
    return m_rawData;
}

const int Int16Data::getDataLength() const {
    return m_length;
}

UInt16Data::UInt16Data(double factor, int offset) : m_conversionFactor(factor), m_offset_(offset) {
    m_rawData.resize(m_length);
}

void UInt16Data::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    m_rawData[0] = data[0];
    m_value = (static_cast<uint16_t>(data[0]) - m_offset_) * m_conversionFactor;
}

std::string UInt16Data::getData() const {
    return std::to_string(m_value);
}

const std::vector<uint16_t> &UInt16Data::getRawData() const {
    return m_rawData;
}

const int UInt16Data::getDataLength() const {
    return m_length;
}

Int32Data::Int32Data(double factor, int offset) : m_conversionFactor(factor), m_offset_(offset) {
    m_rawData.resize(m_length);
}

void Int32Data::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    for (size_t i = 0; i < m_length; ++i) {
        m_rawData[i] = data[i];
    }

    m_value = ((((static_cast<int32_t>(data[0]) << 16) | data[1]) - m_offset_) * m_conversionFactor);
}

void Int32Data::setDataEndian(uint16_t *data) {
    // 中端模式转成大端模式：16位之间小端模式（低寄存器在前 高寄存器在后） 16位内大端模式
    uint16_t temp = data[0];
    data[0] = data[1];
    data[1] = temp;
}

std::string Int32Data::getData() const {
    return std::to_string(m_value);
}

const std::vector<uint16_t> &Int32Data::getRawData() const {
    return m_rawData;
}

const int Int32Data::getDataLength() const {
    return m_length;
}

UInt32Data::UInt32Data(double factor, int offset) : m_conversionFactor(factor), m_offset_(offset) {
    m_rawData.resize(m_length);
}

void UInt32Data::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    for (size_t i = 0; i < m_length; ++i) {
        m_rawData[i] = data[i];
    }

    m_value = ((((static_cast<uint32_t>(data[0]) << 16) | data[1]) - m_offset_) * m_conversionFactor);
}

void UInt32Data::setDataEndian(uint16_t *data) {
    // 中端模式转成大端模式：16位之间小端模式（低寄存器在前 高寄存器在后） 16位内大端模式
    uint16_t temp = data[0];
    data[0] = data[1];
    data[1] = temp;
}

std::string UInt32Data::getData() const {
    return std::to_string(m_value);
}

const std::vector<uint16_t> &UInt32Data::getRawData() const {
    return m_rawData;
}

const int UInt32Data::getDataLength() const {
    return m_length;
}

BitData::BitData(int len, double factor) : m_conversionFactor(factor), m_length(len) {
    m_rawData.resize(len);
}

void BitData::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    for (size_t i = 0; i < m_length; ++i) {
        m_rawData[i] = data[i];
    }

    if (m_length == 1) {
        // 将 data[0] 的值放入低16位，高16位自动为0
        m_value = std::bitset<32>(data[0]);
    } else if (m_length == 2) {
        // 16位之间小端模式（低寄存器在前 高寄存器在后） 16位内大端模式
        uint32_t combinedData = (static_cast<uint32_t>(data[1]) << 16) | static_cast<uint32_t>(data[0]);
        m_value = std::bitset<32>(combinedData);
    } else {
        LOG_ERROR << "m_length out of range.";
    }
}

std::string BitData::getData(size_t bitIndex, size_t bitLength) const {
    if (bitIndex + bitLength > m_value.size()) {
        LOG_ERROR << "Bit index or m_length out of range.";
        return std::string();
    }

    uint16_t result = 0;
    for (size_t i = 0; i < bitLength; ++i) {
        // 位集中的特定位是否被设置为 1
        if (m_value.test(bitIndex + i)) {
            result |= (1 << i);
        }
    }

    // 检查是否有偏移位
    auto it = bitOffsets.find(bitIndex);
    if (it != bitOffsets.end()) {
        result += it->second;
    }

    result = result * m_conversionFactor;
    return std::to_string(result);
}

const std::vector<uint16_t> &BitData::getRawData() const {
    return m_rawData;
}

void BitData::setOffset(size_t bitIndex, int offset) {
    bitOffsets[bitIndex] = offset;
}

const int BitData::getDataLength() const {
    return m_length;
}

StringData::StringData(int len) : m_length(len) {
    m_rawData.resize(len);
}

void StringData::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    for (size_t i = 0; i < m_length; ++i) {
        m_rawData[i] = data[i];
    }

    m_value.clear();
    for (size_t i = 0; i < m_length; ++i) {
        char lowByte = static_cast<char>(data[i] & 0x00FF);
        char highByte = static_cast<char>((data[i] >> 8) & 0x00FF);
        m_value.push_back(lowByte);
        m_value.push_back(highByte);
    }

    size_t end = m_value.find('\0'); // 查找字符串结束符
    if (end != std::string::npos) {  // 如果找到结束符
        m_value.erase(end);          // 删除结束符及其后的内容
    }
}

std::string StringData::getData() const {
    return m_value;
}

const std::vector<uint16_t> &StringData::getRawData() const {
    return m_rawData;
}

const int StringData::getDataLength() const {
    return m_length;
}

MultiUInt16Data::MultiUInt16Data(size_t len) : m_length(len) {
    m_values.resize(len);
    m_rawData.resize(len);
}

void MultiUInt16Data::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    for (size_t i = 0; i < m_length; ++i) {
        m_rawData[i] = data[i];
    }

    for (size_t i = 0; i < m_length; ++i) {
        m_values[i] = data[i];
    }
}

std::string MultiUInt16Data::getData() const {
    std::ostringstream oss;
    for (const auto &m_value : m_values) {
        oss << m_value << " ";
    }
    return oss.str();
}

const std::vector<uint16_t> &MultiUInt16Data::getRawData() const {
    return m_rawData;
}

const int MultiUInt16Data::getDataLength() const {
    return m_length;
}

Float32Data::Float32Data(double factor, int offset) : m_conversionFactor(factor), m_offset_(offset) {
    m_rawData.resize(m_length);
}

void Float32Data::setData(const uint16_t *data) {
    if (data == nullptr) {
        return;
    }

    // 原始数据 未处理字节序
    for (size_t i = 0; i < m_length; ++i) {
        m_rawData[i] = data[i];
    }

    // 将两个16位寄存器组合成32位浮点数
    uint32_t combinedData = (static_cast<uint32_t>(data[0]) << 16) | data[1];
    // 模式重解释,浮点数的二进制表示由符号位、指数位和尾数位组成
    float *floatPtr = reinterpret_cast<float *>(&combinedData);
    m_value = (*floatPtr - m_offset_) * m_conversionFactor;
}

void Float32Data::setDataEndian(uint16_t *data) {
    // 中端模式转成大端模式：16位之间小端模式（低寄存器在前 高寄存器在后） 16位内大端模式
    uint16_t temp = data[0];
    data[0] = data[1];
    data[1] = temp;
}

std::string Float32Data::getData() const {
    return std::to_string(m_value);
}

const std::vector<uint16_t> &Float32Data::getRawData() const {
    return m_rawData;
}

const int Float32Data::getDataLength() const {
    return m_length;
}