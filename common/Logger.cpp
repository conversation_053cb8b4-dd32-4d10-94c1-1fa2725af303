#include "Logger.h"
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <filesystem>
#include <sstream>

// 获取单例实例
Logger &Logger::getInstance() {
    static Logger instance;
    return instance;
}

// 构造函数
Logger::Logger()
    : m_initialized(false), m_globalDupFilterEnabled(false), m_globalDupFilterTimeWindow(std::chrono::seconds(5)),
      m_logCallCount(0), m_lastCleanupTime(std::chrono::steady_clock::now()) {
    // 初始化日志系统
    initialize("BlackBox_logger");

    // 开启全局过滤功能
    enableGlobalDuplicateFiltering(std::chrono::seconds(60)); // 60s 过滤一次

    // 添加文件输出（若目录创建失败则写入根目录）
    addFileOutput("log/BlackBox.log", true, 5 * 1024 * 1024, 15);
}

// 析构函数
Logger::~Logger() {
    // spdlog会自动处理资源释放
}

// 确保日志系统已初始化
void Logger::ensureInitialized() {
    if (!m_initialized) {
        initialize("BlackBox_logger");
    }
}

// 初始化日志系统
void Logger::initialize(const std::string &loggerName) {
    if (m_initialized) {
        return;
    }

    // 尝试获取现有的日志记录器，如果不存在则创建新的
    m_logger = spdlog::get(loggerName);
    if (!m_logger) {
        // 创建控制台输出器
        auto consoleSink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        m_logger = std::make_shared<spdlog::logger>(loggerName, consoleSink);

        // 设置日志格式: 时间 [日志级别] 消息 (不显示logger名称)
        m_logger->set_pattern("%Y-%m-%d %H:%M:%S.%e [%^%l%$] [%s:%#] %v");

        // 设置默认日志级别为INFO
        m_logger->set_level(spdlog::level::info);

        // 注册日志记录器
        spdlog::register_logger(m_logger);

        // 设置全局默认日志记录器
        spdlog::set_default_logger(m_logger);
    }

    m_initialized = true;
}

// 设置日志级别
void Logger::setLogLevel(Logger::LogLevel level) {
    ensureInitialized();
    m_logger->set_level(static_cast<spdlog::level::level_enum>(level));
}

// 添加控制台输出
void Logger::addConsoleOutput() {
    ensureInitialized();

    // 检查是否已有控制台输出
    for (auto &sink : m_logger->sinks()) {
        if (dynamic_cast<spdlog::sinks::stdout_color_sink_mt *>(sink.get())) {
            return; // 已有控制台输出，无需添加
        }
    }

    // 创建控制台输出器
    auto consoleSink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    consoleSink->set_pattern("%Y-%m-%d %H:%M:%S.%e [%^%l%$] [%s:%#] %v");

    // 直接添加到 logger
    m_logger->sinks().push_back(consoleSink);
}

// 添加文件输出
bool Logger::addFileOutput(const std::string &filename, bool rotate, size_t maxFileSize, size_t maxFiles) {
    ensureInitialized();

    std::filesystem::path targetPath(filename);
    std::string effectivePath = filename; // 默认使用调用方提供的完整路径

    try {
        if (targetPath.has_parent_path() && !targetPath.parent_path().empty()) {
            std::error_code ec;
            if (!std::filesystem::exists(targetPath.parent_path(), ec)) {
                if (!std::filesystem::create_directories(targetPath.parent_path(), ec)) {
                    spdlog::warn(
                        "无法创建日志目录: {}. 错误: {}. 日志将写入当前目录。", targetPath.parent_path().string(), ec.message());
                    effectivePath = targetPath.filename().string(); // 若目录创建失败则写入根目录
                }
            }
        }
    } catch (const std::exception &ex) {
        spdlog::warn("创建日志目录时发生异常: {}. 日志将写入当前目录。", ex.what());
        effectivePath = targetPath.filename().string();
    }

    try {
        std::shared_ptr<spdlog::sinks::rotating_file_sink_mt> fileSink;

        if (rotate) {
            // 使用循环文件输出
            fileSink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(effectivePath, maxFileSize, maxFiles);
        } else {
            // 使用基本文件输出
            fileSink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(effectivePath, 0, 1); // 不循环
        }

        fileSink->set_pattern("%Y-%m-%d %H:%M:%S.%e [%^%l%$] [%s:%#] %v");

        // 直接添加到 logger
        m_logger->sinks().push_back(fileSink);

        return true;
    } catch (const spdlog::spdlog_ex &ex) {
        spdlog::error("添加文件日志失败: {}", ex.what());
        return false;
    }
}

// LogStream 构造函数 - 现在在外部实现
LogStream::LogStream(const char *file, int line, Logger::LogLevel level) : m_file(file), m_line(line), m_level(level) {
    // 确保Logger被初始化(logger内部初始化)
    Logger::getInstance();
}

LogStream::~LogStream() {
    // 1. 如果消息为空，则不记录
    if (m_buffer.str().empty()) {
        return;
    }

    // 2. 检查是否应该过滤此消息
    if (Logger::getInstance().shouldFilterDuplicate(m_buffer.str())) {
        return; // 消息被过滤，不输出
    }

    // 3. 创建spdlog需要的源码位置对象
    spdlog::source_loc location(m_file, m_line, SPDLOG_FUNCTION);

    // 4. 获取你配置好的logger实例 (不要用全局函数)
    auto logger = Logger::getInstance().getLogger();

    // 5. 调用log方法，将元数据(level, location)和纯净的消息负载(m_buffer)分开传递
    logger->log(location, static_cast<spdlog::level::level_enum>(m_level), m_buffer.str());
}

// 判断是否应该过滤重复日志
bool Logger::shouldFilterDuplicate(const std::string &message) {
    std::lock_guard<std::mutex> lock(m_dupFilterMutex);

    if (!m_globalDupFilterEnabled) {
        return false;
    }

    auto now = std::chrono::steady_clock::now();

    // 定期清理过期条目（每100次调用或每分钟）
    if (++m_logCallCount >= 100 || std::chrono::duration_cast<std::chrono::minutes>(now - m_lastCleanupTime).count() >= 1) {
        cleanExpiredEntries();
        m_logCallCount = 0;
        m_lastCleanupTime = now;
    }

    // 查找消息是否存在于缓存中
    auto it = m_messageTimestamps.find(message);
    if (it != m_messageTimestamps.end()) {
        // 检查时间差
        auto timeDiff = std::chrono::duration_cast<std::chrono::seconds>(now - it->second);
        if (timeDiff < m_globalDupFilterTimeWindow) {
            // 在时间窗口内，应该过滤
            return true;
        }
        // 更新时间戳
        it->second = now;
    } else {
        // 新消息，添加到缓存
        m_messageTimestamps[message] = now;
    }

    return false;
}

// 清理过期的缓存条目
void Logger::cleanExpiredEntries() {
    auto now = std::chrono::steady_clock::now();
    auto it = m_messageTimestamps.begin();

    while (it != m_messageTimestamps.end()) {
        auto timeDiff = std::chrono::duration_cast<std::chrono::seconds>(now - it->second);
        if (timeDiff > m_globalDupFilterTimeWindow) {
            it = m_messageTimestamps.erase(it);
        } else {
            ++it;
        }
    }
}

// 启用全局重复日志过滤
void Logger::enableGlobalDuplicateFiltering(std::chrono::seconds timeWindow) {
    std::lock_guard<std::mutex> lock(m_dupFilterMutex);
    m_globalDupFilterEnabled = true;
    m_globalDupFilterTimeWindow = timeWindow;
}

// 禁用全局重复日志过滤
void Logger::disableGlobalDuplicateFiltering() {
    std::lock_guard<std::mutex> lock(m_dupFilterMutex);
    m_globalDupFilterEnabled = false;
    m_messageTimestamps.clear();
}

// 设置全局去重时间窗口
void Logger::setGlobalDuplicateFilterWindow(std::chrono::seconds timeWindow) {
    std::lock_guard<std::mutex> lock(m_dupFilterMutex);
    m_globalDupFilterTimeWindow = timeWindow;
}
