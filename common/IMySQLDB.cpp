#include "IMySQLDB.h"
#include "Logger.h"

IMySQLDB::IMySQLDB(const std::string &host,
                   const std::string &user,
                   const std::string &password,
                   const std::string &database,
                   const std::string &configFile,
                   const std::string &charset,
                   unsigned int port)
    : m_mysql(nullptr), m_host(host), m_user(user), m_password(password), m_database(database), m_port(port),
      m_configFile(configFile), m_charset(charset) {
    // 初始化MySQL连接
    m_mysql = mysql_init(nullptr);
    if (!m_mysql) {
        throw std::runtime_error("MySQL初始化失败");
    }

    // 显式指定配置文件路径
    mysql_options(m_mysql, MYSQL_READ_DEFAULT_FILE, m_configFile.c_str());

    // 打开连接
    open("");
}

void IMySQLDB::open(const std::string &connectionString) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    try {
        // 连接到MySQL服务器
        if (!mysql_real_connect(m_mysql, m_host.c_str(), m_user.c_str(), m_password.c_str(), m_database.c_str(), m_port, nullptr, 0)) {
            std::string error = mysql_error(m_mysql);
            throw std::runtime_error("无法连接到MySQL数据库: " + error);
        }

        // 设置默认字符集为utf8mb4
        mysql_set_character_set(m_mysql, m_charset.c_str());
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }
}

void IMySQLDB::close() {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    if (m_mysql) {
        mysql_close(m_mysql);
        m_mysql = nullptr;
    }
}

bool IMySQLDB::isConnected() {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    return m_mysql != nullptr && ping();
}

bool IMySQLDB::reconnect() {
    close();
    m_mysql = mysql_init(nullptr);
    if (!m_mysql) {
        return false;
    }

    open("");
    return isConnected();
}

void IMySQLDB::execute(const std::string &query) {
    try {
        // 保存最后执行的SQL语句
        m_lastExecutedSql = query;

        if (mysql_query(m_mysql, query.c_str()) != 0) {
            std::string error = mysql_error(m_mysql);
            throw std::runtime_error("SQL 错误: " + error);
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }
}

void IMySQLDB::executeWithLock(const std::string &query) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    try {
        // 保存最后执行的SQL语句
        m_lastExecutedSql = query;

        if (mysql_query(m_mysql, query.c_str()) != 0) {
            std::string error = mysql_error(m_mysql);
            throw std::runtime_error("SQL 错误: " + error);
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }
}

std::vector<std::vector<std::string>> IMySQLDB::query(const std::string &query) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::vector<std::vector<std::string>> result;
    try {
        // 保存最后执行的SQL语句
        m_lastExecutedSql = query;

        if (mysql_query(m_mysql, query.c_str()) != 0) {
            std::string error = mysql_error(m_mysql);
            throw std::runtime_error("SQL 错误: " + error);
        }

        MYSQL_RES *res = mysql_store_result(m_mysql);
        if (!res) {
            std::string error = mysql_error(m_mysql);
            throw std::runtime_error("无法获取查询结果: " + error);
        }

        int num_fields = mysql_num_fields(res);
        MYSQL_ROW row;

        while ((row = mysql_fetch_row(res))) {
            std::vector<std::string> rowData;
            for (int i = 0; i < num_fields; i++) {
                rowData.push_back(row[i] ? row[i] : "NULL");
            }
            result.push_back(rowData);
        }

        mysql_free_result(res);
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }

    return result;
}

void IMySQLDB::insert(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::string query = "INSERT INTO " + table + " (";
    std::string valStr = " VALUES (";

    for (const auto &pair : values) {
        query += pair.first + ",";
        valStr += "'" + escapeString(pair.second) + "',";
    }

    // 移除最后的逗号并添加右括号
    query.back() = ')';
    valStr.back() = ')';

    // 完成 SQL 语句的拼接
    query += valStr;

    // 保存最后执行的SQL语句
    m_lastExecutedSql = query;

    execute(query);
}

void IMySQLDB::update(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values, const std::string &condition) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::string query = "UPDATE " + table + " SET ";

    for (const auto &pair : values) {
        query += pair.first + "='" + escapeString(pair.second) + "',";
    }

    query.back() = ' ';
    query += "WHERE " + condition;

    // 保存最后执行的SQL语句
    m_lastExecutedSql = query;

    execute(query);
}

void IMySQLDB::remove(const std::string &table, const std::string &condition) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::string query = "DELETE FROM " + table + " WHERE " + condition;

    // 保存最后执行的SQL语句
    m_lastExecutedSql = query;

    execute(query);
}

unsigned long IMySQLDB::getLastInsertId() {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    return mysql_insert_id(m_mysql);
}

unsigned long IMySQLDB::getAffectedRows() {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    return mysql_affected_rows(m_mysql);
}

bool IMySQLDB::ping() {
    return mysql_ping(m_mysql) == 0;
}

std::string IMySQLDB::escapeString(const std::string &str) {
    char *escaped = new char[str.length() * 2 + 1];
    mysql_real_escape_string(m_mysql, escaped, str.c_str(), str.length());
    std::string result(escaped);
    delete[] escaped;
    return result;
}

std::string IMySQLDB::getLastExecutedSql() const {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    return m_lastExecutedSql;
}

void IMySQLDB::saveLastExecutedSql(const std::string &sql) {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    m_lastExecutedSql = sql;
}
