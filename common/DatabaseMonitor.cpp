#include "DatabaseMonitor.h"
#include "Logger.h"
#include <algorithm>
#include <iomanip>
#include <iostream>
#include <numeric>
#include <sstream>
#include <thread>
#include <chrono>

/**
 * @brief 构造函数，初始化监控器
 *
 * 初始化监控状态和默认告警阈值
 */
DatabaseMonitor::DatabaseMonitor() : m_isRunning(false) {
    // 初始化默认告警阈值
    m_alertThresholds.maxConnectionsPercent = 80;
    m_alertThresholds.minBufferPoolHitRatio = 90.0;
    m_alertThresholds.maxRowLockWaits = 10;
    m_alertThresholds.maxSlowQueryCount = 10;
    m_alertThresholds.slowQueryThreshold = 1000;
}

/**
 * @brief 启动数据库监控
 *
 * @param connectionPool 连接池指针
 * @param interval 监控间隔（毫秒）
 *
 * 初始化监控器并启动后台监控线程
 */
void DatabaseMonitor::startMonitoring(std::shared_ptr<IConnectionPool> connectionPool, int interval) {
    if (m_isRunning) {
        return;
    }

    m_connectionPool = connectionPool;
    m_interval = interval;
    m_isRunning = true;
    m_lastMonitorTime = std::chrono::steady_clock::now();
    m_lastMetricsUpdateTime = m_lastMonitorTime;

    // 启动监控线程
    std::thread monitorThread(&DatabaseMonitor::monitorTask, this);
    monitorThread.detach();
}

/**
 * @brief 停止数据库监控
 *
 * 设置停止标志，让监控线程自然结束
 */
void DatabaseMonitor::stopMonitoring() {
    m_isRunning = false;
}

std::string DatabaseMonitor::getPoolStatus() const {
    if (!m_connectionPool) {
        return "连接池未初始化";
    }

    std::ostringstream oss;
    oss << "连接池状态:\n";
    oss << "  活跃连接数: " << m_connectionPool->getActiveConnections() << "\n";
    oss << "  空闲连接数: " << m_connectionPool->getIdleConnections() << "\n";
    oss << "  最大连接数: " << m_connectionPool->getMaxConnections() << "\n";

    return oss.str();
}

/**
 * @brief 获取查询统计信息
 * @return 格式化的查询统计字符串
 */
std::string DatabaseMonitor::getQueryStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);

    if (m_queryTimes.empty()) {
        return "暂无查询统计信息";
    }

    // 计算所有查询的聚合信息
    struct QueryInfo {
        std::string query;
        size_t count;
        long totalTime;
        long minTime;
        long maxTime;
        long avgTime;
    };

    std::vector<QueryInfo> queryInfos;
    queryInfos.reserve(m_queryTimes.size());

    for (const auto &pair : m_queryTimes) {
        if (pair.second.empty()) {
            continue;
        }

        QueryInfo info;
        info.query = pair.first;
        info.count = pair.second.size();

        // 计算总时间、最小时间、最大时间和平均时间
        info.totalTime = std::accumulate(pair.second.begin(), pair.second.end(), 0L);
        info.minTime = *std::min_element(pair.second.begin(), pair.second.end());
        info.maxTime = *std::max_element(pair.second.begin(), pair.second.end());
        info.avgTime = info.totalTime / info.count;

        queryInfos.push_back(info);
    }

    // 按平均执行时间降序排序
    std::sort(queryInfos.begin(), queryInfos.end(), [](const QueryInfo &a, const QueryInfo &b) {
        return a.avgTime > b.avgTime;
    });

    // 格式化输出
    std::ostringstream oss;
    oss << "查询统计信息 (共" << queryInfos.size() << "个查询):\n";

    int count = 0;
    for (const auto &info : queryInfos) {
        // 限制显示的查询数量，避免输出过多
        if (++count > 20) {
            oss << "...(省略" << (queryInfos.size() - 20) << "个查询)\n";
            break;
        }

        // 截断过长的查询语句
        std::string truncatedQuery = info.query;
        if (truncatedQuery.length() > 100) {
            truncatedQuery = truncatedQuery.substr(0, 97) + "...";
        }

        oss << count << ". [平均: " << info.avgTime << "ms, "
            << "最小: " << info.minTime << "ms, "
            << "最大: " << info.maxTime << "ms, "
            << "执行次数: " << info.count << "] " << truncatedQuery << "\n";
    }

    return oss.str();
}

/**
 * @brief 记录查询执行时间
 *
 * @param query SQL查询语句
 * @param duration 执行时间（毫秒）
 *
 * 记录查询的执行时间以便识别慢查询
 */
void DatabaseMonitor::recordQueryTime(const std::string &query, long duration) {
    std::lock_guard<std::mutex> lock(m_statsMutex);

    // 记录查询执行时间
    m_queryTimes[query].push_back(duration);

    // 限制每个查询的历史记录数量，防止内存无限增长
    const size_t MAX_HISTORY = 100;
    if (m_queryTimes[query].size() > MAX_HISTORY) {
        m_queryTimes[query].erase(m_queryTimes[query].begin());
    }
}

/**
 * @brief 获取慢查询列表
 *
 * @param threshold 慢查询阈值（毫秒）
 * @return 慢查询列表，包含查询语句和平均执行时间
 *
 * 根据平均执行时间识别慢查询，返回超过阈值的查询列表
 */
std::vector<std::pair<std::string, long>> DatabaseMonitor::getSlowQueries(long threshold) const {
    // std::lock_guard<std::mutex> lock(m_statsMutex);

    std::vector<std::pair<std::string, long>> slowQueries;

    for (const auto &pair : m_queryTimes) {
        if (!pair.second.empty()) {
            // 计算平均执行时间
            long total = std::accumulate(pair.second.begin(), pair.second.end(), 0L);
            long avg = static_cast<long>(static_cast<double>(total) / pair.second.size());

            // 如果平均执行时间超过阈值，添加到慢查询列表
            if (avg > threshold) {
                slowQueries.emplace_back(pair.first, avg);
            }
        }
    }

    // 按执行时间降序排序
    std::sort(slowQueries.begin(), slowQueries.end(), [](const auto &a, const auto &b) {
        return a.second > b.second;
    });

    return slowQueries;
}

/**
 * @brief 获取完整的指标报告
 *
 * @param includeQueryStats 是否包含详细的查询统计信息
 * @return 格式化的完整指标报告
 */
std::string DatabaseMonitor::getMetricsReport(bool includeQueryStats) const {
    std::ostringstream report;

    // 添加时间戳
    auto now = std::chrono::system_clock::now();
    auto time_t_now = std::chrono::system_clock::to_time_t(now);
    std::tm time_info;
    localtime_r(&time_t_now, &time_info);

    report << "=========== 数据库监控报告 ===========\n";
    report << "时间: " << std::put_time(&time_info, "%Y-%m-%d %H:%M:%S") << "\n\n";

    // 添加连接池状态
    report << getPoolStatus() << "\n";

    // 添加连接指标
    {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        report << "连接指标:\n";
        report << "  当前连接数: " << m_connectionMetrics.threadsConnected << "\n";
        report << "  当前运行线程数: " << m_connectionMetrics.threadsRunning << "\n";
        report << "  总连接尝试数: " << m_connectionMetrics.totalConnections << "\n";
        report << "  最大并发连接数: " << m_connectionMetrics.maxUsedConnections << "\n";
        report << "  最大允许连接数: " << m_connectionMetrics.maxConnections << "\n";

        if (m_connectionMetrics.maxConnections > 0) {
            double connectionPercent = 100.0 * m_connectionMetrics.threadsConnected / m_connectionMetrics.maxConnections;
            report << "  连接使用率: " << std::fixed << std::setprecision(2) << connectionPercent << "%";

            if (connectionPercent > m_alertThresholds.maxConnectionsPercent) {
                report << " [警告: 超过阈值 " << m_alertThresholds.maxConnectionsPercent << "%]";
            }
            report << "\n";
        }

        report << "\n缓冲池指标:\n";
        report << "  磁盘读取页数: " << m_bufferPoolMetrics.reads << "\n";
        report << "  缓冲池读取请求数: " << m_bufferPoolMetrics.readRequests << "\n";
        report << "  缓冲池空闲页数: " << m_bufferPoolMetrics.pagesFree << "\n";
        report << "  缓冲池总页数: " << m_bufferPoolMetrics.pagesTotal << "\n";
        report << "  缓冲池命中率: " << std::fixed << std::setprecision(2) << m_bufferPoolMetrics.hitRatio << "%";

        if (m_bufferPoolMetrics.hitRatio < m_alertThresholds.minBufferPoolHitRatio) {
            report << " [警告: 低于阈值 " << m_alertThresholds.minBufferPoolHitRatio << "%]";
        }
        report << "\n";

        report << "\n锁指标:\n";
        report << "  行锁等待次数: " << m_lockMetrics.rowLockWaits;
        if (m_lockMetrics.rowLockWaits > m_alertThresholds.maxRowLockWaits) {
            report << " [警告: 超过阈值 " << m_alertThresholds.maxRowLockWaits << "]";
        }
        report << "\n";
        report << "  行锁平均等待时间: " << m_lockMetrics.rowLockTimeAvg << " ms\n";
        report << "  表锁等待次数: " << m_lockMetrics.tableLockWaits << "\n";
    }

    // 添加慢查询统计
    auto slowQueries = getSlowQueries(m_alertThresholds.slowQueryThreshold);
    report << "\n慢查询统计:\n";
    report << "  慢查询数量: " << slowQueries.size();
    if (slowQueries.size() > m_alertThresholds.maxSlowQueryCount) {
        report << " [警告: 超过阈值 " << m_alertThresholds.maxSlowQueryCount << "]";
    }
    report << "\n";

    if (!slowQueries.empty()) {
        report << "  前" << std::min(slowQueries.size(), size_t(5)) << "个最慢查询:\n";
        int count = 0;
        for (const auto &query : slowQueries) {
            if (count++ >= 5)
                break;

            // 截断过长的查询语句
            std::string truncatedQuery = query.first;
            if (truncatedQuery.length() > 100) {
                truncatedQuery = truncatedQuery.substr(0, 97) + "...";
            }

            report << "    - " << query.second << " ms: " << truncatedQuery << "\n";
        }
    }

    // 如果需要，添加所有查询的详细统计
    if (includeQueryStats) {
        report << "\n" << getQueryStats() << "\n";
    }

    report << "====================================\n";

    return report.str();
}

/**
 * @brief 打印当前的指标报告
 *
 * @param printQueryStats 是否打印详细的查询统计
 */
void DatabaseMonitor::printMetricsReport(bool printQueryStats) {
    LOG_INFO << getMetricsReport(printQueryStats);
}

/**
 * @brief 监控任务主函数
 *
 * 后台线程的主循环，定期执行监控任务：
 * 1. 更新性能指标
 * 2. 检查告警条件
 * 3. 定期安排维护任务
 * 4. 打印指标报告
 */
void DatabaseMonitor::monitorTask() {
    if (!m_isRunning) {
        return;
    }

    try {
        auto now = std::chrono::steady_clock::now();
        auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastMonitorTime).count();

        if (elapsedMs >= m_interval) {
            // 更新性能指标
            updateMetrics();

            // 检查告警
            checkAlerts();

            // 打印指标报告 (每5个周期打印一次详细的查询统计)
            static int reportCounter = 0;
            printMetricsReport((++reportCounter % 5) == 0);

            // 每天执行一次维护任务（86400000毫秒 = 24小时）
            static const long MAINTENANCE_INTERVAL_MS = 86400000;
            auto sinceMaintenance = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastMetricsUpdateTime).count();

            if (sinceMaintenance >= MAINTENANCE_INTERVAL_MS) {
                scheduleMaintenance();
                m_lastMetricsUpdateTime = now;
            }

            m_lastMonitorTime = now;
        }

        // 休眠一段时间，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    } catch (const std::exception &e) {
        LOG_ERROR << "Error in monitor task: " << e.what();
        // 出错后短暂休眠，避免错误状态下无限循环消耗资源
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
}

/**
 * @brief 更新数据库性能指标
 *
 * 从数据库获取最新的性能指标，包括连接指标、缓冲池指标和锁指标
 * 通过SQL查询获取各类性能统计信息并更新到相应的内部结构中
 */
void DatabaseMonitor::updateMetrics() {
    if (!m_connectionPool || m_connectionPool->getIdleConnections() <= 0) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_metricsMutex);

    try {
        // 获取连接指标
        auto connectionResults = executeQuery("SHOW GLOBAL STATUS LIKE 'Threads_%';");
        auto connectionResults2 = executeQuery("SHOW GLOBAL STATUS LIKE 'Connection%';");
        auto maxUsedConnectionsResult = executeQuery("SHOW GLOBAL STATUS LIKE 'Max_used_connections';");
        auto maxConnectionsResult = executeQuery("SHOW VARIABLES LIKE 'max_connections';");

        // 解析Threads相关指标
        for (const auto &row : connectionResults) {
            if (row.find("Variable_name") != row.end() && row.find("Value") != row.end()) {
                const std::string &name = row.at("Variable_name");
                const std::string &value = row.at("Value");

                if (name == "Threads_connected") {
                    m_connectionMetrics.threadsConnected = std::stoi(value);
                } else if (name == "Threads_running") {
                    m_connectionMetrics.threadsRunning = std::stoi(value);
                }
            }
        }

        // 解析Connection相关指标
        for (const auto &row : connectionResults2) {
            if (row.find("Variable_name") != row.end() && row.find("Value") != row.end()) {
                const std::string &name = row.at("Variable_name");
                const std::string &value = row.at("Value");

                if (name == "Connections") {
                    m_connectionMetrics.totalConnections = std::stoi(value);
                }
            }
        }

        // 解析最大已用连接数
        if (!maxUsedConnectionsResult.empty() &&
            maxUsedConnectionsResult[0].find("Value") != maxUsedConnectionsResult[0].end()) {
            m_connectionMetrics.maxUsedConnections = std::stoi(maxUsedConnectionsResult[0].at("Value"));
        }

        // 解析最大允许连接数
        if (!maxConnectionsResult.empty() && maxConnectionsResult[0].find("Value") != maxConnectionsResult[0].end()) {
            m_connectionMetrics.maxConnections = std::stoi(maxConnectionsResult[0].at("Value"));
        }

        // 获取缓冲池指标
        auto bufferPoolResults = executeQuery("SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_%';");

        // 解析缓冲池指标
        for (const auto &row : bufferPoolResults) {
            if (row.find("Variable_name") != row.end() && row.find("Value") != row.end()) {
                const std::string &name = row.at("Variable_name");
                const std::string &value = row.at("Value");

                if (name == "Innodb_buffer_pool_reads") {
                    m_bufferPoolMetrics.reads = std::stol(value);
                } else if (name == "Innodb_buffer_pool_read_requests") {
                    m_bufferPoolMetrics.readRequests = std::stol(value);
                } else if (name == "Innodb_buffer_pool_pages_free") {
                    m_bufferPoolMetrics.pagesFree = std::stol(value);
                } else if (name == "Innodb_buffer_pool_pages_total") {
                    m_bufferPoolMetrics.pagesTotal = std::stol(value);
                }
            }
        }

        // 计算缓冲池命中率
        if (m_bufferPoolMetrics.readRequests > 0) {
            m_bufferPoolMetrics.hitRatio = 100.0 * (1.0 - static_cast<double>(m_bufferPoolMetrics.reads) /
                                                              static_cast<double>(m_bufferPoolMetrics.readRequests));
        } else {
            m_bufferPoolMetrics.hitRatio = 100.0;
        }

        // 获取锁指标
        auto lockResults = executeQuery("SHOW GLOBAL STATUS LIKE '%lock%';");

        // 解析锁指标
        for (const auto &row : lockResults) {
            if (row.find("Variable_name") != row.end() && row.find("Value") != row.end()) {
                const std::string &name = row.at("Variable_name");
                const std::string &value = row.at("Value");

                if (name == "Innodb_row_lock_waits") {
                    m_lockMetrics.rowLockWaits = std::stol(value);
                } else if (name == "Innodb_row_lock_time_avg") {
                    m_lockMetrics.rowLockTimeAvg = std::stol(value);
                } else if (name == "Table_locks_waited") {
                    m_lockMetrics.tableLockWaits = std::stol(value);
                }
            }
        }
    } catch (const std::exception &e) {
        LOG_ERROR << "Error updating metrics: " << e.what();
    }
}

/**
 * @brief 检查性能指标并发出警报
 *
 * 检查各项性能指标是否超过警报阈值，如果超过则生成警报信息
 * 警报包括连接数、缓冲池命中率、锁等待和慢查询数量
 */
void DatabaseMonitor::checkAlerts() {
    std::lock(m_metricsMutex, m_statsMutex);                                  // 锁定多个互斥锁
    std::lock_guard<std::mutex> lockMetrics(m_metricsMutex, std::adopt_lock); // 不会尝试锁定互斥量，而是接管已经锁定的互斥量
    std::lock_guard<std::mutex> lockStats(m_statsMutex, std::adopt_lock); // 不会尝试锁定互斥量，而是接管已经锁定的互斥量

    std::vector<std::string> warnings;

    // 检查连接数
    if (m_connectionMetrics.maxConnections > 0) {
        double connectionPercent = 100.0 * m_connectionMetrics.threadsConnected / m_connectionMetrics.maxConnections;
        if (connectionPercent > m_alertThresholds.maxConnectionsPercent) {
            std::ostringstream oss;
            oss << "连接数警告: 当前连接数 " << m_connectionMetrics.threadsConnected << " 占最大连接数 "
                << m_connectionMetrics.maxConnections << " 的 " << std::fixed << std::setprecision(2)
                << connectionPercent << "%";
            warnings.push_back(oss.str());
        }
    }

    // 检查缓冲池命中率
    if (m_bufferPoolMetrics.hitRatio < m_alertThresholds.minBufferPoolHitRatio) {
        std::ostringstream oss;
        oss << "缓冲池命中率警告: 当前命中率 " << std::fixed << std::setprecision(2) << m_bufferPoolMetrics.hitRatio
            << "% 低于阈值 " << m_alertThresholds.minBufferPoolHitRatio << "%";
        warnings.push_back(oss.str());
    }

    // 检查行锁等待
    if (m_lockMetrics.rowLockWaits > m_alertThresholds.maxRowLockWaits) {
        std::ostringstream oss;
        oss << "行锁等待警告: 当前行锁等待次数 " << m_lockMetrics.rowLockWaits << " 超过阈值 " << m_alertThresholds.maxRowLockWaits;
        warnings.push_back(oss.str());
    }

    // 检查慢查询数量
    auto slowQueries = getSlowQueries(m_alertThresholds.slowQueryThreshold);
    if (slowQueries.size() > m_alertThresholds.maxSlowQueryCount) {
        std::ostringstream oss;
        oss << "慢查询警告: 检测到 " << slowQueries.size() << " 个慢查询(阈值: " << m_alertThresholds.maxSlowQueryCount << ")";
        warnings.push_back(oss.str());
    }

    // 输出警告信息
    if (!warnings.empty()) {
        // 获取当前时间并格式化
        auto now = std::chrono::system_clock::now();
        auto time_t_now = std::chrono::system_clock::to_time_t(now);
        std::tm time_info;
        localtime_r(&time_t_now, &time_info);

        std::ostringstream timeStr;
        timeStr << std::put_time(&time_info, "%Y-%m-%d %H:%M:%S");

        LOG_ERROR << "===== 数据库性能警告 (" << timeStr.str() << ") =====";

        for (const auto &warning : warnings) {
            LOG_ERROR << warning;
        }

        LOG_ERROR << "=========================";
    }
}

/**
 * @brief 安排定期维护任务
 *
 * 为数据库表执行定期维护操作，包括分析表、检查表完整性、优化表结构和修复损坏的表
 * 这些操作有助于保持数据库的健康状态和优化性能
 */
void DatabaseMonitor::scheduleMaintenance() {
    // 获取所有表
    auto tables = executeQuery("SHOW TABLES;");

    for (const auto &row : tables) {
        // 根据我们对SHOW TABLES结果列名的设置，表名应该在"Table_name"键下
        try {
            if (row.find("Table_name") != row.end()) {
                std::string tableName = row.at("Table_name");

                // 记录维护操作开始
                LOG_INFO << "开始维护表: " << tableName;

                // 分析表 - 更新统计信息
                analyzeTable(tableName);

                // 检查表 - 确保完整性
                if (checkTable(tableName)) {
                    // 优化表 - 减少碎片
                    optimizeTable(tableName);
                } else {
                    // 如果检查失败，尝试修复
                    repairTable(tableName);
                }

                // 记录维护操作完成
                LOG_INFO << "表维护完成: " << tableName;
            }
        } catch (const std::exception &e) {
            LOG_ERROR << "Error scheduling maintenance: " << e.what();
        }
    }
}

/**
 * @brief 优化表结构
 *
 * @param tableName 要优化的表名
 * @return 操作是否成功
 *
 * 执行OPTIMIZE TABLE命令，重新组织表数据和索引，减少碎片并提高查询速度
 */
bool DatabaseMonitor::optimizeTable(const std::string &tableName) {
    try {
        std::string query = "OPTIMIZE TABLE " + tableName;
        auto result = executeQuery(query);

        for (const auto &row : result) {
            // 根据对TABLE操作结果列名的设置，消息类型和内容应该在"Msg_type"和"Msg_text"键下
            if (row.find("Msg_type") != row.end() && row.find("Msg_text") != row.end()) {
                if (row.at("Msg_type") == "error") {
                    LOG_ERROR << "Error optimizing table " << tableName << ": " << row.at("Msg_text");
                    return false;
                }
            }
        }
        return true;
    } catch (const std::exception &e) {
        LOG_ERROR << "Error optimizing table " << tableName << ": " << e.what();
        return false;
    }
}

/**
 * @brief 分析表并更新统计信息
 *
 * @param tableName 要分析的表名
 * @return 操作是否成功
 *
 * 执行ANALYZE TABLE命令，更新表的统计信息，帮助查询优化器选择更高效的执行计划
 */
bool DatabaseMonitor::analyzeTable(const std::string &tableName) {
    try {
        std::string query = "ANALYZE TABLE " + tableName;
        auto result = executeQuery(query);

        for (const auto &row : result) {
            // 根据对TABLE操作结果列名的设置，消息类型和内容应该在"Msg_type"和"Msg_text"键下
            if (row.find("Msg_type") != row.end() && row.find("Msg_text") != row.end()) {
                if (row.at("Msg_type") == "error") {
                    LOG_ERROR << "Error analyzing table " << tableName << ": " << row.at("Msg_text");
                    return false;
                }
            }
        }
        return true;
    } catch (const std::exception &e) {
        LOG_ERROR << "Error analyzing table " << tableName << ": " << e.what();
        return false;
    }
}

/**
 * @brief 检查表的完整性
 *
 * @param tableName 要检查的表名
 * @return 表是否完整无损
 *
 * 执行CHECK TABLE命令，检查表是否存在损坏，尤其是在异常关机或服务器崩溃后
 */
bool DatabaseMonitor::checkTable(const std::string &tableName) {
    try {
        std::string query = "CHECK TABLE " + tableName;
        auto result = executeQuery(query);

        for (const auto &row : result) {
            // 根据我们对TABLE操作结果列名的设置，消息类型和内容应该在"Msg_type"和"Msg_text"键下
            if (row.find("Msg_type") != row.end() && row.find("Msg_text") != row.end()) {
                if (row.at("Msg_type") == "error") {
                    LOG_ERROR << "Error checking table " << tableName << ": " << row.at("Msg_text");
                    return false;
                }
            }
        }
        return true;
    } catch (const std::exception &e) {
        LOG_ERROR << "Error checking table " << tableName << ": " << e.what();
        return false;
    }
}

/**
 * @brief 修复损坏的表
 *
 * @param tableName 要修复的表名
 * @return 修复是否成功
 *
 * 当检测到表损坏时执行REPAIR TABLE命令进行修复
 */
bool DatabaseMonitor::repairTable(const std::string &tableName) {
    try {
        std::string query = "REPAIR TABLE " + tableName;
        auto result = executeQuery(query);

        for (const auto &row : result) {
            // 根据我们对TABLE操作结果列名的设置，消息类型和内容应该在"Msg_type"和"Msg_text"键下
            if (row.find("Msg_type") != row.end() && row.find("Msg_text") != row.end()) {
                if (row.at("Msg_type") == "error") {
                    LOG_ERROR << "Error repairing table " << tableName << ": " << row.at("Msg_text");
                    return false;
                }
            }
        }
        return true;
    } catch (const std::exception &e) {
        LOG_ERROR << "Error repairing table " << tableName << ": " << e.what();
        return false;
    }
}

/**
 * @brief 设置性能警报阈值
 *
 * @param thresholds 包含各项警报阈值的结构体
 *
 * 允许应用程序自定义各种性能指标的警报阈值
 */
void DatabaseMonitor::setAlertThresholds(const AlertThresholds &thresholds) {
    std::lock_guard<std::mutex> lock(m_metricsMutex);
    m_alertThresholds = thresholds;
}

/**
 * @brief 获取当前连接指标
 *
 * @return 包含连接相关指标的结构体
 */
DatabaseMonitor::ConnectionMetrics DatabaseMonitor::getConnectionMetrics() const {
    std::lock_guard<std::mutex> lock(m_metricsMutex);
    return m_connectionMetrics;
}

/**
 * @brief 获取缓冲池指标
 *
 * @return 包含缓冲池相关指标的结构体
 */
DatabaseMonitor::BufferPoolMetrics DatabaseMonitor::getBufferPoolMetrics() const {
    std::lock_guard<std::mutex> lock(m_metricsMutex);
    return m_bufferPoolMetrics;
}

/**
 * @brief 获取锁指标
 *
 * @return 包含锁相关指标的结构体
 */
DatabaseMonitor::LockMetrics DatabaseMonitor::getLockMetrics() const {
    std::lock_guard<std::mutex> lock(m_metricsMutex);
    return m_lockMetrics;
}

/**
 * @brief 获取数据库健康状态报告
 *
 * @return 包含健康状态、警告和建议的报告
 *
 * 综合分析各项性能指标，生成数据库健康状态报告，包括潜在问题和优化建议
 */
DatabaseMonitor::HealthReport DatabaseMonitor::getDatabaseHealthReport() const {
    HealthReport report;
    report.healthy = true;

    std::lock(m_metricsMutex, m_statsMutex);                                  // 锁定多个互斥锁
    std::lock_guard<std::mutex> lockMetrics(m_metricsMutex, std::adopt_lock); // 不会尝试锁定互斥量，而是接管已经锁定的互斥量
    std::lock_guard<std::mutex> lockStats(m_statsMutex, std::adopt_lock); // 不会尝试锁定互斥量，而是接管已经锁定的互斥量

    // 检查连接数
    if (m_connectionMetrics.maxConnections > 0) {
        double connectionPercent = 100.0 * m_connectionMetrics.threadsConnected / m_connectionMetrics.maxConnections;
        if (connectionPercent > m_alertThresholds.maxConnectionsPercent) {
            report.healthy = false;
            report.warnings.push_back("连接数过高");
            report.recommendations.push_back("优化应用程序连接池设置，减少空闲连接");
        }
    }

    // 检查缓冲池命中率
    if (m_bufferPoolMetrics.hitRatio < m_alertThresholds.minBufferPoolHitRatio) {
        report.healthy = false;
        report.warnings.push_back("缓冲池命中率过低");

        if (m_bufferPoolMetrics.pagesTotal > 0 && m_bufferPoolMetrics.pagesFree < 0.1 * m_bufferPoolMetrics.pagesTotal) {
            report.recommendations.push_back("增加 innodb_buffer_pool_size 配置");
        } else {
            report.recommendations.push_back("分析并优化频繁访问的表和查询");
        }
    }

    // 检查行锁等待
    if (m_lockMetrics.rowLockWaits > m_alertThresholds.maxRowLockWaits) {
        report.healthy = false;
        report.warnings.push_back("行锁等待次数过多");
        report.recommendations.push_back("优化事务，减少事务持有锁的时间");
        report.recommendations.push_back("检查是否有未使用索引的UPDATE或DELETE操作");
    }

    // 检查慢查询
    auto slowQueries = getSlowQueries(m_alertThresholds.slowQueryThreshold);
    if (!slowQueries.empty()) {
        if (slowQueries.size() > m_alertThresholds.maxSlowQueryCount) {
            report.healthy = false;
            report.warnings.push_back("慢查询数量过多");
        }

        std::ostringstream oss;
        oss << "分析并优化以下慢查询：\n";
        for (size_t i = 0; i < std::min(slowQueries.size(), size_t(3)); ++i) {
            oss << "- 平均执行时间 " << slowQueries[i].second << " ms: "
                << (slowQueries[i].first.length() > 100 ? slowQueries[i].first.substr(0, 97) + "..." : slowQueries[i].first)
                << "\n";
        }
        report.recommendations.push_back(oss.str());
    }

    return report;
}

/**
 * @brief 执行SQL查询并处理结果
 *
 * @param query 要执行的SQL查询
 * @return 查询结果的二维映射表，便于通过列名访问数据
 */
std::vector<std::unordered_map<std::string, std::string>> DatabaseMonitor::executeQuery(const std::string &query) {
    std::vector<std::unordered_map<std::string, std::string>> results;

    // 检查连接池是否可用
    if (!m_connectionPool || m_connectionPool->getIdleConnections() <= 0) {
        throw std::runtime_error("Connection pool is not available");
    }

    // 从连接池获取数据库连接
    auto connection = m_connectionPool->getConnection();
    if (!connection) {
        throw std::runtime_error("Failed to obtain database connection");
    }

    try {
        // 执行查询，获取结果集（二维vector）
        auto queryResult = connection->query(query);

        // 从查询类型判断列名
        std::vector<std::string> columnNames;

        // 处理SHOW语句的列名
        if (query.find("SHOW GLOBAL STATUS") != std::string::npos || query.find("SHOW VARIABLES") != std::string::npos) {
            columnNames = {"Variable_name", "Value"};
        }
        // 处理SHOW TABLES语句
        else if (query.find("SHOW TABLES") != std::string::npos) {
            columnNames = {"Table_name"};
        }
        // 处理OPTIMIZE/ANALYZE/CHECK/REPAIR TABLE语句
        else if (query.find("TABLE") != std::string::npos) {
            columnNames = {"Table", "Op", "Msg_type", "Msg_text"};
        }
        // 如果无法确定列名，就使用数字索引作为列名
        else {
            // 找出结果集中最宽的行，确定列数
            size_t maxColumns = 0;
            for (const auto &row : queryResult) {
                maxColumns = std::max(maxColumns, row.size());
            }

            // 使用数字作为列名
            for (size_t i = 0; i < maxColumns; ++i) {
                columnNames.push_back(std::to_string(i));
            }
        }

        // 处理所有数据行
        for (const auto &dataRow : queryResult) {
            std::unordered_map<std::string, std::string> row;

            // 将每一列数据映射到对应的列名
            for (size_t colIdx = 0; colIdx < columnNames.size() && colIdx < dataRow.size(); ++colIdx) {
                row[columnNames[colIdx]] = dataRow[colIdx];
            }

            results.push_back(row);
        }

        // 归还连接到连接池
        m_connectionPool->releaseConnection(connection);
    } catch (const std::exception &e) {
        // 确保即使发生异常也能释放连接
        m_connectionPool->releaseConnection(connection);
        throw; // 重新抛出异常，由调用者处理
    }

    return results;
}