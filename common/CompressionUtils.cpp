#include "CompressionUtils.h"

CompressionUtils &CompressionUtils::getInstance() {
    static CompressionUtils instance;
    return instance;
}

std::vector<uint8_t> CompressionUtils::compressData(const std::vector<uint8_t> &data) {
    std::lock_guard<std::mutex> lock(m_mutex); // 确保线程安全

    uLongf compressedSize = compressBound(data.size());
    std::vector<uint8_t> compressedData(compressedSize);

    int result = compress(compressedData.data(), &compressedSize, data.data(), data.size());
    if (result != Z_OK) {
        // 处理压缩错误
        throw std::runtime_error("数据压缩失败，错误码: " + std::to_string(result));
    }

    // 调整压缩后的数据大小
    compressedData.resize(compressedSize);
    return compressedData;
}

std::vector<uint8_t> CompressionUtils::decompressData(const std::vector<uint8_t> &data, size_t blobSize,
                                                      size_t expectedUncompressedSize) {
    std::lock_guard<std::mutex> lock(m_mutex); // 确保线程安全

    std::vector<uint8_t> decompressedData(expectedUncompressedSize);
    uLongf decompressedSize = expectedUncompressedSize;

    int result = uncompress(decompressedData.data(), &decompressedSize, data.data(), blobSize);
    if (result != Z_OK) {
        throw std::runtime_error("数据解压失败，错误码: " + std::to_string(result));
    }

    if (decompressedSize != expectedUncompressedSize) {
        throw std::runtime_error("解压后的数据大小与预期不符");
    }

    decompressedData.resize(decompressedSize);
    return decompressedData;
}