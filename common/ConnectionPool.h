#ifndef CONNECTION_POOL_H
#define CONNECTION_POOL_H

#include "IDatabaseInterface.h"
#include <chrono>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>

/**
 * @brief 数据库连接池接口
 */
class IConnectionPool {
  public:
    virtual ~IConnectionPool() = default;

    /**
     * @brief 获取一个数据库连接
     *
     * @param timeout 超时时间（毫秒），0表示不等待
     * @return std::shared_ptr<IDatabaseInterface> 数据库连接
     */
    virtual std::shared_ptr<IDatabaseInterface> getConnection(int timeout = 0) = 0;

    /**
     * @brief 释放一个数据库连接
     *
     * @param conn 要释放的连接
     */
    virtual void releaseConnection(std::shared_ptr<IDatabaseInterface> conn) = 0;

    /**
     * @brief 获取连接池中活跃的连接数
     *
     * @return size_t 活跃连接数
     */
    virtual size_t getActiveConnections() const = 0;

    /**
     * @brief 获取连接池中空闲的连接数
     *
     * @return size_t 空闲连接数
     */
    virtual size_t getIdleConnections() const = 0;

    /**
     * @brief 获取连接池的最大连接数
     *
     * @return size_t 最大连接数
     */
    virtual size_t getMaxConnections() const = 0;
};

/**
 * @brief 数据库连接池实现
 */
template <typename T> class ConnectionPool : public IConnectionPool {
  public:
    /**
     * @brief 构造函数
     *
     * @param maxConnections 最大连接数
     * @param args 创建连接的参数
     */
    template <typename... Args>
    ConnectionPool(size_t maxConnections, Args &&...args) : m_maxConnections(maxConnections), m_activeConnections(0) {
        // 初始化连接池
        for (size_t i = 0; i < maxConnections / 2; ++i) {
            // 初始创建的连接数为最大连接数的一半 （5个）
            m_connections.push(std::make_shared<T>(std::forward<Args>(args)...));
        }

        // 保存创建连接的参数
        m_createConnection = [args...]() {
            return std::make_shared<T>(args...);
        };
    }

    /**
     * @brief 析构函数
     */
    ~ConnectionPool() override {
        // 清空连接池
        while (!m_connections.empty()) {
            m_connections.pop();
        }
    }

    /**
     * @brief 获取一个数据库连接
     *
     * @param timeout 超时时间（毫秒），0表示不等待
     * @return std::shared_ptr<IDatabaseInterface> 数据库连接
     */
    std::shared_ptr<IDatabaseInterface> getConnection(int timeout = 0) override {
        std::unique_lock<std::mutex> lock(m_mutex);

        // 如果连接池为空且活跃连接数小于最大连接数，则创建新连接
        if (m_connections.empty()) {
            if (m_activeConnections < m_maxConnections) {
                ++m_activeConnections;
                return m_createConnection();
            }

            // 如果已达到最大连接数，则等待连接释放
            if (timeout > 0) {
                auto status = m_cv.wait_for(lock, std::chrono::milliseconds(timeout), [this]() {
                    return !m_connections.empty();
                });

                if (!status) {
                    // 超时
                    return nullptr;
                }
            } else if (timeout == 0) {
                // 不等待
                return nullptr;
            } else {
                // 无限等待
                m_cv.wait(lock, [this]() {
                    return !m_connections.empty();
                });
            }
        }

        // 获取一个连接
        auto conn = m_connections.front();
        m_connections.pop();
        ++m_activeConnections;

        return conn;
    }

    /**
     * @brief 释放一个数据库连接
     *
     * @param conn 要释放的连接
     */
    void releaseConnection(std::shared_ptr<IDatabaseInterface> conn) override {
        if (!conn) {
            return;
        }

        std::unique_lock<std::mutex> lock(m_mutex);

        // 检查连接是否正常
        if (conn->isConnected()) {
            m_connections.push(std::static_pointer_cast<T>(conn));
        } else {
            // 如果连接不正常，则尝试重新连接
            if (conn->reconnect()) {
                m_connections.push(std::static_pointer_cast<T>(conn));
            } else {
                // 如果重连失败，则创建新连接
                m_connections.push(m_createConnection());
            }
        }

        --m_activeConnections;
        m_cv.notify_one();
    }

    /**
     * @brief 获取连接池中活跃的连接数
     *
     * @return size_t 活跃连接数
     */
    size_t getActiveConnections() const override {
        std::unique_lock<std::mutex> lock(m_mutex);
        return m_activeConnections;
    }

    /**
     * @brief 获取连接池中空闲的连接数
     *
     * @return size_t 空闲连接数
     */
    size_t getIdleConnections() const override {
        std::unique_lock<std::mutex> lock(m_mutex);
        return m_connections.size();
    }

    /**
     * @brief 获取连接池的最大连接数
     *
     * @return size_t 最大连接数
     */
    size_t getMaxConnections() const override {
        return m_maxConnections;
    }

  private:
    size_t m_maxConnections;                                // 最大连接数
    size_t m_activeConnections;                             // 活跃连接数
    std::queue<std::shared_ptr<T>> m_connections;           // 连接池
    std::function<std::shared_ptr<T>()> m_createConnection; // 创建连接的函数
    mutable std::mutex m_mutex;                             // 互斥锁
    std::condition_variable m_cv;                           // 条件变量
};

#endif // CONNECTION_POOL_H