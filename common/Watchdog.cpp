#include "Watchdog.h"
#include "Logger.h"
#include <thread>

void Watchdog::registerTask(int taskId, const std::string &taskName, int timeout) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    m_tasks_[taskId] = std::make_tuple(std::chrono::system_clock::now(), timeout, taskName);
}

void Watchdog::feed(int taskId) {
    std::lock_guard<std::mutex> lock(m_mutex_);
    if (m_tasks_.find(taskId) != m_tasks_.end()) {
        std::get<0>(m_tasks_[taskId]) = std::chrono::system_clock::now();
    }
}

void Watchdog::start(std::function<void()> onTimeout) {
    std::thread([this, onTimeout]() {
        while (true) {
            std::unique_lock<std::mutex> lock(m_mutex_);
            auto now = std::chrono::system_clock::now();
            for (auto &task : m_tasks_) {
                if (std::get<1>(task.second) > 0) {
                    if (std::chrono::duration_cast<std::chrono::milliseconds>(now - std::get<0>(task.second)).count() >
                        std::get<1>(task.second)) {
                        LOG_ERROR << "Task '" << std::get<2>(task.second) << "' timeout. System reset needed!!";
                        onTimeout();
                    }
                }
            }
            lock.unlock();
            std::this_thread::sleep_for(std::chrono::milliseconds(300)); //  周期执行，每300毫秒检测一次
        }
    }).detach();
}
