#include "ConfigManager.h"
#include "Logger.h"
#include <cstdio>
#include <fstream>

ConfigManager &ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

ConfigManager::ConfigManager() {
    // 尝试打开配置文件
    std::string filename = "configs.yaml";

    std::ifstream config_file(filename);
    if (!config_file.is_open()) {
        LOG_ERROR << "Failed to open " << filename;
        return;
    }

    m_config = YAML::LoadFile(filename);
}