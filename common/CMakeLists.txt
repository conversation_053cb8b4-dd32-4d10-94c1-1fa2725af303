# 添加MySQL相关的源文件
set(COMMON_SOURCES
    ModbusDataHandler.cpp
    ModbusTcpClient.cpp
    CompressionUtils.cpp
    ByteOrderUtils.cpp
    TaskManager.cpp
    Watchdog.cpp
    iSQLiteDB.cpp
    IMySQLDB.cpp
    DatabaseFactory.cpp
    MySQLMigration.cpp
    ConfigManager.cpp
    ModbusRtuMaster.cpp
    RegisterData.cpp
    DatabaseMonitor.cpp
    Logger.cpp
)

# 查找系统安装的spdlog库
find_package(spdlog REQUIRED)

# 添加库
add_library(common_lib STATIC ${COMMON_SOURCES})

# 查找并添加SQLite3的包含目录
find_package(SQLite3 REQUIRED)

# paho.mqtt.cpp 库已在根CMakeLists.txt中查找

# 查找MySQL库
include(FindPkgConfig)
pkg_check_modules(MYSQL REQUIRED mysqlclient)

# 添加包含目录
target_include_directories(common_lib PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${SQLite3_INCLUDE_DIRS}
    ${PAHO_MQTT_CPP_INCLUDE_DIR}
    ${MYSQL_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/src
)

# 链接库
target_link_libraries(common_lib PUBLIC
    ${SQLite3_LIBRARIES}
    ${MYSQL_LIBRARIES}
    ${MODBUS_LIBRARIES}
    pthread
    yaml-cpp
    ${PAHO_MQTT_CPP_LIBRARIES}
    ${PAHO_MQTT_C_LIBRARIES}
    spdlog::spdlog
)