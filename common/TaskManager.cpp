#include "TaskManager.h"
#include <unistd.h>

TaskManager::TaskManager() {}

// 析构函数，确保所有线程被正确终止
TaskManager::~TaskManager() {
    for (auto &pair : m_taskThreads_) {
        if (pair.second.joinable()) {
            pair.second.join();
        }
    }
    m_taskThreads_.clear();
}

void TaskManager::addTask(const std::vector<TaskConfig> &configs) {
    for (const auto &config : configs) {
        m_tasks_[m_nextTaskId_] = config; // 将配置关联到新的任务ID
        m_nextTaskId_++;                  // 更新任务ID
    }
}

void TaskManager::startAllTasks() {
    for (const auto &task : m_tasks_) {
        startTask(task.second, task.first);
        m_watchdog_.registerTask(task.first, task.second.name, task.second.cycleTime);
    }
    m_watchdog_.start([] {
        _exit(1); // 强制退出程序 (可能会导致 I/O
                  // 缓冲未刷新:某些打印输出，日志会丢失。不会走程序安排好的清理资源流程,但是系统内核会回收)
    });
}

void TaskManager::startTask(const TaskConfig &config, int taskId) {

    std::thread taskThread([this, config, taskId]() {
        while (true) {
            config.taskFunction();
            m_watchdog_.feed(taskId);
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); //  线程缓冲100毫秒 必要：释放锁给其他线程
        }
    });

    m_taskThreads_[taskId] = std::move(taskThread);
}