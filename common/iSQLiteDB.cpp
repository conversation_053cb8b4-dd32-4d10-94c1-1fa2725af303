#include "iSQLiteDB.h"
#include "Logger.h"

iSQLiteDB::iSQLiteDB(const std::string &dbPath) : m_db(nullptr), m_dbPath(dbPath) {
    open(dbPath);
}

void iSQLiteDB::open(const std::string &dbPath) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    try {
        if (sqlite3_open(dbPath.c_str(), &m_db) != SQLITE_OK) {
            throw std::runtime_error("无法打开数据库: " + std::string(sqlite3_errmsg(m_db)));
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }
}

void iSQLiteDB::close() {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    if (m_db) {
        sqlite3_close(m_db);
        m_db = nullptr;
    }
}

bool iSQLiteDB::isConnected() {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    return m_db != nullptr;
}

bool iSQLiteDB::reconnect() {
    close();
    open(m_dbPath);
    return isConnected();
}

void iSQLiteDB::execute(const std::string &query) {
    char *errMsg = nullptr;
    try {
        if (sqlite3_exec(m_db, query.c_str(), nullptr, nullptr, &errMsg) != SQLITE_OK) {
            std::string error = errMsg;
            sqlite3_free(errMsg);
            throw std::runtime_error("SQL 错误: " + error);
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }
}

void iSQLiteDB::executeWithLock(const std::string &query) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    char *errMsg = nullptr;
    try {
        if (sqlite3_exec(m_db, query.c_str(), nullptr, nullptr, &errMsg) != SQLITE_OK) {
            std::string error = errMsg;
            sqlite3_free(errMsg);
            throw std::runtime_error("SQL 错误: " + error);
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }
}

std::vector<std::vector<std::string>> iSQLiteDB::query(const std::string &query) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::vector<std::vector<std::string>> result;
    char *errMsg = nullptr;
    try {
        if (sqlite3_exec(m_db, query.c_str(), callback, &result, &errMsg) != SQLITE_OK) {
            std::string error = errMsg;
            sqlite3_free(errMsg);
            throw std::runtime_error("SQL 错误: " + error);
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "Exception caught: " << e.what();
    }
    return result;
}

void iSQLiteDB::insert(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::string query = "INSERT INTO " + table + " (";
    std::string valStr = " VALUES (";

    for (const auto &pair : values) {
        query += pair.first + ",";
        valStr += "'" + pair.second + "',";
    }

    query.back() = ')';
    valStr.back() = ')';
    query += valStr;

    execute(query);
}

void iSQLiteDB::update(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values, const std::string &condition) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::string query = "UPDATE " + table + " SET ";

    for (const auto &pair : values) {
        query += pair.first + "='" + pair.second + "',";
    }

    query.back() = ' ';
    query += "WHERE " + condition;

    execute(query);
}

void iSQLiteDB::remove(const std::string &table, const std::string &condition) {
    std::lock_guard<std::mutex> lock(m_db_mutex);

    std::string query = "DELETE FROM " + table + " WHERE " + condition;
    execute(query);
}

int iSQLiteDB::callback(void *data, int argc, char **argv, char **azColName) {
    auto *result = static_cast<std::vector<std::vector<std::string>> *>(data);
    result->emplace_back(argv, argv + argc);
    return 0;
}

std::string iSQLiteDB::getLastExecutedSql() const {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    return m_lastExecutedSql;
}

void iSQLiteDB::saveLastExecutedSql(const std::string &sql) {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    m_lastExecutedSql = sql;
}