#ifndef MODBUSDATAHANDLER_H
#define MODBUSDATAHANDLER_H

#include "RegisterData.h"
#include "StructDefs.h"
#include <any>
#include <iostream>
#include <memory>
#include <mutex>
#include <optional>
#include <queue>
#include <unordered_map>
#include <vector>
#include <yaml-cpp/yaml.h>

class ModbusDataHandler {
  public:
    ModbusDataHandler(uint32_t dbAddr = 0);

    /**
     * @brief 获取寄存器数据
     * @param path 寄存器路径（结构体）
     * @param name 寄存器名称
     * @return 返回成功：字符串类型的数据。返回失败：空字符串 或者 "-1"
     */
    std::string getDataByCategory(const RegisterPath &path, const std::string &name);

    /**
     * @brief 写寄存器数据
     * @param path 寄存器（结构体）路径
     * @param name 寄存器名称
     * @param value 要写入的值，可以是 uint16_t、int32_t 或 bool 类型
     *
     * 此函数会根据给定的寄存器路径和名称查找寄存器配置，验证数据类型是否匹配，
     * 并将待写入的数据（地址和数据）存入待写队列。
     */
    void writeData(const RegisterPath &path, const std::string &name, const std::string &value);

    /**
     * @brief 写多个连续寄存器数据
     * @param path 寄存器（结构体）路径
     * @param dataList [name,value] :[寄存器名称,要写入的值，可以是 uint16_t、int32_t 或 bool 类型]
     *
     * 此函数会根据给定的寄存器路径和名称查找寄存器配置，验证数据类型是否匹配，
     * 并将待写入的数据（地址和数据）存入待写队列。
     */
    void writeMultipleData(const RegisterPath &path, const std::vector<std::pair<std::string, std::string>> &dataList);

    /**
     * @brief 获取待写数据
     * @return 如果有待写数据，返回包含地址和数据的 std::pair (数据地址，数据内容) ；否则返回 std::nullopt
     *
     * 此函数从待写队列中取出待写的数据。如果队列不为空，返回数据并从队列中移除；
     * 否则返回 std::nullopt。
     */

    // std::optional<std::pair<int, std::any>> getPendingWrite();
    std::optional<std::vector<std::pair<int, std::any>>> getPendingWrite();

    void parseData(ModbusData &data);

    /**
     * @brief 批量获取缓存数据
     * @param batchSize 数据个数
     * @return 返回缓存数据
     */
    std::vector<ModbusData> getCacheDataBatch(size_t batchSize);

    /**
     * @brief 把读到的数据存入缓存数据中
     * @param mdbsData 数据结构体
     */
    void addCacheData(ModbusData &mdbsData);

    // 获取指定寄存器的 DataAttributes
    bool getRegisterAttributes(const RegisterPath &path, const std::string &name, DataAttributes &outAttributes);

    /**
     * @brief  获取寄存器地址块配置
     * @param registerType 寄存器类型
     */
    const std::vector<RegisterConfig> &getRegConfigMap(const std::string &registerType) const;

    /**
     * @brief  获取 BmsBmuDataList 列表
     */
    const std::vector<std::string> &getBmsBmuDataList();

  protected:
    void initialize(); // 非虚函数，用于基类的初始化

    void addRegister(const RegisterPath &path, const RegisterInfo &config);

    void setDataWithEndian(const RegisterPath &path, int address, uint16_t *data);

    virtual void loadConfigFromFile(const YAML::Node &config) = 0;

    void parseConditionalData(const ModbusData &data, const ConditionalParseConfig *conditionalConfig);

    void parseRegisters(const YAML::Node &registerDataList, const std::string &registerType, const std::string &subCategory);

    void parseModuleTemplates(const YAML::Node &modules, const YAML::Node &templateRegisters, const std::string &registerType);

    std::vector<RegisterInfo> parseRegisterConfig(const YAML::Node &registerDataList,
                                                  const std::string &registerType,
                                                  const std::string &subCategory,
                                                  const bool isConditional,
                                                  int conditionValue,
                                                  const ConditionalParseConfig *conditionalConfig = nullptr);

    void parseReadMap(const YAML::Node &readMap);

    std::string getBitByCategory(const RegisterPath &path, const std::string &bitName);

    void parseBitItem(const YAML::Node &bitItem,
                      const std::string &bitKey,
                      std::shared_ptr<BitData> &bitData,
                      const std::string &registerType,
                      const std::string &subCategory,
                      int address,
                      bool isConditional,
                      int conditionValue,
                      const ConditionalParseConfig *conditionalConfig);

    std::string findCategoryByAddress(const std::string &registerType, int address) const;

    std::optional<std::pair<int, std::any>> prepareWriteData(const RegisterPath &path, const std::string &name, const std::string &value);

    // 存放所有（通用）寄存器的映射表：键->寄存器类型，值->映射表 (键->目录/单元，值->映射表
    // (键->寄存器地址，值->该寄存器的值(对象)))
    std::unordered_map<std::string, std::unordered_map<std::string, std::unordered_map<int, std::shared_ptr<RegisterData>>>> m_dataMap;

    // 条件解析配置映射表：键->寄存器类型 + "/" + 类别名称（categoryPath），值->条件解析配置对象
    std::unordered_map<std::string, std::shared_ptr<ConditionalParseConfig>> m_conditionalParseConfigs;

    // 存放所有寄存器的映射表：键->寄存器类型，值->映射表 (键->目录/单元，值->存放该目录下寄存器结构体的容器)
    std::unordered_map<std::string, std::unordered_map<std::string, std::vector<RegisterInfo>>> m_configList;

    // 地址到分类的映射缓存 （所有寄存器 静态 + 动态 即使条件协议号变了，存储的目录也不会变）
    // 键->寄存器类型，值->映射表 (键->地址，值->分类)
    std::unordered_map<std::string, std::unordered_map<int, std::string>> m_addressToCategoryMap;

    // 地址到寄存器信息映射缓存 （注：只存 静态/通用 寄存器）：键->寄存器类型，值->映射表 (键->地址，值->RegisterInfo)
    std::unordered_map<std::string, std::unordered_map<int, RegisterInfo>> m_addrToStaticRegInfoMap;

    // 通用的bit类型的数据映射表：键->数据名称，值->映射表 (键->寄存器类型 + 目录/单元，值->映射表
    // (该bit处在哪个寄存器的地址，该bit处于该寄存器的哪一位，该bit占多少位))
    std::unordered_map<std::string, std::unordered_map<std::string, std::tuple<int, int, int>>> m_bitNameToAddress;

    // 存储条件解析的 bit 信息
    // 键->寄存器类型 + 目录/单元 ，值-> 映射表(键->依赖的寄存器（名称），值->映射表 (键->协议号，值->映射表
    // (键->寄存器名，值->tuple(该bit处在哪个寄存器的地址，该bit处于该寄存器的哪一位，该bit占多少位))))
    std::unordered_map<std::string, std::unordered_map<std::string, std::unordered_map<int, std::unordered_map<std::string, std::tuple<int, int, int>>>>> m_conditionalBitNameToAddress;

    // 用于缓存寄存器地址块配置信息
    std::unordered_map<std::string, std::vector<RegisterConfig>> m_registerConfigMap;

    std::deque<ModbusData> m_dataCache; // modbus数据缓存
    std::mutex m_cacheMutex;            // 缓存锁

    // 存储待写入的数据 [name,value] :[寄存器地址,要写入的值，可以是 uint16_t、int32_t 或 bool 类型]
    std::queue<std::vector<std::pair<int, std::any>>> m_writeQueue;
    std::mutex m_writeQueueMutex;

    const uint32_t m_dbAddress;      // 用于存放数据库的数据库地址
    const uint8_t m_entriesNum = 20; // 读取数据库条目数

    // 存储每个条件配置的上一次条件值：键->categoryPath，值->上一次的条件值
    std::unordered_map<std::string, int> m_lastConditionValues;

    std::vector<std::string> bmsBmuDataList; // 存储所有 bmsBmuData 名称
};

#endif // MODBUSDATAHANDLER_H
