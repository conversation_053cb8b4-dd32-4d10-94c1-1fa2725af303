#ifndef COMPRESSION_UTILS_H
#define COMPRESSION_UTILS_H

#include <cstdint>
#include <mutex>
#include <stdexcept>
#include <vector>
#include <zlib.h>

class CompressionUtils {
  public:
    // 获取单例实例
    static CompressionUtils &getInstance();

    /**
     * @brief 压缩数据
     *
     * @param data 待压缩的数据
     * @return 压缩后的数据
     * @throws std::runtime_error 如果压缩失败
     */
    std::vector<uint8_t> compressData(const std::vector<uint8_t> &data);

    /**
     * @brief 解压缩数据
     *
     * @param data 待解压缩的数据
     * @param blobSize 压缩数据本身的大小
     * @param expectedUncompressedSize 预期解压后数据的大小
     * @return 解压后的数据
     * @throws std::runtime_error 如果解压失败
     */
    std::vector<uint8_t> decompressData(const std::vector<uint8_t> &data, size_t blobSize,
                                        size_t expectedUncompressedSize);

    // 禁用拷贝构造和赋值操作
    CompressionUtils(const CompressionUtils &) = delete;
    CompressionUtils &operator=(const CompressionUtils &) = delete;

  private:
    // 私有构造函数
    CompressionUtils() = default;

    // 私有析构函数
    ~CompressionUtils() = default;

    // 可选：用于保护压缩和解压缩过程中的线程安全
    std::mutex m_mutex;
};

#endif // COMPRESSION_UTILS_H