#ifndef WATCHDOG_H
#define WATCHDOG_H

#include <chrono>
#include <functional>
#include <map>
#include <mutex>
#include <string>

class Watchdog {
  public:
    explicit Watchdog(){};

    /// @brief 注册任务
    /// @param taskId 任务号
    /// @param taskName 任务名
    /// @param timeout 超时时间
    void registerTask(int taskId, const std::string &taskName, int timeout);

    void feed(int taskId);
    void start(std::function<void()> onTimeout);

  private:
    int m_timeout_;
    std::chrono::time_point<std::chrono::system_clock> m_lastFeed_;
    std::mutex m_mutex_;
    // 1.记录的时间点 2.超时时间 3.任务名
    std::map<int, std::tuple<std::chrono::time_point<std::chrono::system_clock>, int, std::string>> m_tasks_;
};

#endif // WATCHDOG_H
