#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include <mutex>
#include <yaml-cpp/yaml.h>

class ConfigManager {
  public:
    static ConfigManager &getInstance();

    inline const YAML::Node getConfig() const { return m_config;};

  private:
    ConfigManager();
    ~ConfigManager() = default;

    // 禁用拷贝构造和赋值操作
    ConfigManager(const ConfigManager &) = delete;
    ConfigManager &operator=(const ConfigManager &) = delete;

    YAML::Node m_config;
};

#endif // CONFIG_MANAGER_H