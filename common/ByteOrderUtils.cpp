#include "ByteOrderUtils.h"
#include <stdexcept>

ByteOrderUtils &ByteOrderUtils::getInstance() {
    static ByteOrderUtils instance;
    return instance;
}

bool ByteOrderUtils::isLittleEndian() const {
    // 通过检查一个16位整数在内存中的存储方式来判断系统字节序
    uint16_t value = 0x0102;
    uint8_t *ptr = reinterpret_cast<uint8_t *>(&value);
    return (ptr[0] == 0x02); // 如果第一个字节是02，说明是小端序
}

uint16_t ByteOrderUtils::hostToBigEndian16(uint16_t value) const {
    if (isLittleEndian()) {
        // 小端序系统需要转换字节序
        return ((value & 0xFF) << 8) | ((value >> 8) & 0xFF);
    }
    // 大端序系统不需要转换
    return value;
}

uint16_t ByteOrderUtils::bigEndianToHost16(uint16_t value) const {
    // 从大端序转换到主机字节序的逻辑与主机字节序转大端序相同
    return hostToBigEndian16(value);
}

uint32_t ByteOrderUtils::hostToBigEndian32(uint32_t value) const {
    if (isLittleEndian()) {
        // 小端序系统需要转换字节序
        return ((value & 0xFF) << 24) | ((value & 0xFF00) << 8) | ((value & 0xFF0000) >> 8) | ((value & 0xFF000000) >> 24);
    }
    // 大端序系统不需要转换
    return value;
}

uint32_t ByteOrderUtils::bigEndianToHost32(uint32_t value) const {
    // 从大端序转换到主机字节序的逻辑与主机字节序转大端序相同
    return hostToBigEndian32(value);
}

std::vector<uint8_t> ByteOrderUtils::registersToBytesBigEndian(const uint16_t *registers, size_t count) const {
    std::vector<uint8_t> bytes(count * 2); // 每个寄存器2字节

    for (size_t i = 0; i < count; i++) {
        uint16_t bigEndianValue = hostToBigEndian16(registers[i]);
        bytes[i * 2] = static_cast<uint8_t>((bigEndianValue >> 8) & 0xFF); // 高字节
        bytes[i * 2 + 1] = static_cast<uint8_t>(bigEndianValue & 0xFF);    // 低字节
    }

    return bytes;
}

std::vector<uint16_t> ByteOrderUtils::bytesToRegistersBigEndian(const uint8_t *bytes, size_t byteCount) const {
    // 确保字节数是偶数
    if (byteCount % 2 != 0) {
        throw std::runtime_error("字节数必须是2的倍数以转换为寄存器值");
    }

    size_t registerCount = byteCount / 2;
    std::vector<uint16_t> registers(registerCount);

    for (size_t i = 0; i < registerCount; i++) {
        // 从大端序字节创建寄存器值
        uint16_t value = (static_cast<uint16_t>(bytes[i * 2]) << 8) | static_cast<uint16_t>(bytes[i * 2 + 1]);
        registers[i] = bigEndianToHost16(value);
    }

    return registers;
}