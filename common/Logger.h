#ifndef LOGGER_H
#define LOGGER_H

#include <memory>
#include <sstream>
#include <string>
#include <vector>
#include <chrono>
#include <spdlog/spdlog.h>
#include <type_traits>
#include <unordered_map>
#include <mutex>

// 前向声明
class LogStream;

/**
 * 日志类 - 基于spdlog库实现的日志系统
 * 支持不同日志级别，可以输出到控制台和文件
 * 记录日志消息的同时包含源文件名和行号
 */
class Logger {
  public:
    // 使用spdlog的日志级别 - 避免使用容易冲突的名称
    enum LogLevel {
        TRACE = spdlog::level::trace,
        DEBUG = spdlog::level::debug,
        INFO = spdlog::level::info,
        WARNING = spdlog::level::warn,
        ERROR = spdlog::level::err,
        CRITICAL = spdlog::level::critical,
        LOG_OFF = spdlog::level::off
    };

    /**
     * @brief 获取单例实例
     * @return Logger单例对象的引用
     */
    static Logger &getInstance();

    std::shared_ptr<spdlog::logger> getLogger() {
        return m_logger;
    }

    // 友元类声明，允许LogStream访问私有方法
    friend class LogStream;

    /**
     * @brief 初始化日志系统
     * @param loggerName 日志记录器名称，默认为"BlackBox_logger"
     */
    void initialize(const std::string &loggerName = "BlackBox_logger");

    /**
     * @brief 设置日志级别
     * @param level 要设置的日志级别
     */
    void setLogLevel(LogLevel level);

    /**
     * @brief 添加控制台输出
     */
    void addConsoleOutput();

    /**
     * @brief 添加文件输出
     * @param filename 日志文件路径
     * @param rotate 是否启用循环日志，默认为true
     * @param maxFileSize 单个日志文件的最大大小，默认为5MB
     * @param maxFiles 最多保留的日志文件数量，默认为3个
     * @return 添加成功则返回true，否则返回false
     */
    bool addFileOutput(const std::string &filename, bool rotate = true, size_t maxFileSize = 5 * 1024 * 1024, size_t maxFiles = 3);

    /**
     * @brief 启用全局重复日志过滤（非连续去重）
     * @param timeWindow 时间窗口，在此期间相同的日志将被忽略
     */
    void enableGlobalDuplicateFiltering(std::chrono::seconds timeWindow = std::chrono::seconds(5));

    /**
     * @brief 禁用全局重复日志过滤
     */
    void disableGlobalDuplicateFiltering();

    /**
     * @brief 设置全局去重时间窗口
     * @param timeWindow 时间窗口
     */
    void setGlobalDuplicateFilterWindow(std::chrono::seconds timeWindow);

  private:
    // 私有构造函数和析构函数
    Logger();
    ~Logger();

    // 禁用拷贝构造和赋值操作
    Logger(const Logger &) = delete;
    Logger &operator=(const Logger &) = delete;

    // 确保日志系统已初始化
    void ensureInitialized();

    // 判断是否应该过滤重复日志
    bool shouldFilterDuplicate(const std::string &message);

    // 清理过期的缓存条目
    void cleanExpiredEntries();

    // 底层日志记录器
    std::shared_ptr<spdlog::logger> m_logger;
    bool m_initialized;

    // 全局重复日志过滤相关
    bool m_globalDupFilterEnabled;
    std::chrono::seconds m_globalDupFilterTimeWindow;
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> m_messageTimestamps;
    mutable std::mutex m_dupFilterMutex;
    size_t m_logCallCount; // 用于定期清理
    std::chrono::steady_clock::time_point m_lastCleanupTime;
};

/**
 * @brief 流式日志类 - 独立类实现
 * 提供流式操作符接口，支持类似std::cout的使用方式
 */
class LogStream {
  public:
    /**
     * @brief 构造函数，创建日志流对象
     * @param file 源文件名
     * @param line 行号
     * @param level 日志级别
     */
    LogStream(const char *file, int line, Logger::LogLevel level);

    /**
     * @brief 析构函数，在对象销毁时输出完整的日志消息
     */
    ~LogStream();

    /**
     * @brief 流式输出操作符 - 通用模板
     * @tparam T 要输出的数据类型
     * @param message 要输出的消息内容
     * @return LogStream对象的引用，支持链式调用
     */
    template <typename T> LogStream &operator<<(const T &message) {
        try {
            m_buffer << message;
        } catch (const std::exception &) {
            m_buffer << "[格式化错误]";
        }
        return *this;
    }

    /**
     * @brief 特殊处理流操作符（如std::endl）
     * @param manip 流操作符函数指针
     * @return LogStream对象的引用
     */
    LogStream &operator<<(std::ostream &(*manip)(std::ostream &)) {
        m_buffer << manip;
        return *this;
    }

    /**
     * @brief 特殊处理iOS基础流操作符
     * @param manip iOS基础流操作符函数指针
     * @return LogStream对象的引用
     */
    LogStream &operator<<(std::ios_base &(*manip)(std::ios_base &)) {
        m_buffer << manip;
        return *this;
    }

  private:
    const char *m_file;
    int m_line;
    Logger::LogLevel m_level;
    std::ostringstream m_buffer;
};

/**
 * @brief 日志宏定义 - 简化日志记录调用
 * 自动包含文件名和行号信息，提供不同级别的日志输出
 */
#define LOG_TRACE    LogStream(__FILE__, __LINE__, Logger::TRACE)    ///< 跟踪级别日志宏
#define LOG_DEBUG    LogStream(__FILE__, __LINE__, Logger::DEBUG)    ///< 调试级别日志宏
#define LOG_INFO     LogStream(__FILE__, __LINE__, Logger::INFO)     ///< 信息级别日志宏
#define LOG_WARNING  LogStream(__FILE__, __LINE__, Logger::WARNING)  ///< 警告级别日志宏
#define LOG_ERROR    LogStream(__FILE__, __LINE__, Logger::ERROR)    ///< 错误级别日志宏
#define LOG_CRITICAL LogStream(__FILE__, __LINE__, Logger::CRITICAL) ///< 严重错误级别日志宏

#endif // LOGGER_H
