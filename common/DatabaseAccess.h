#ifndef DATABASE_ACCESS_H
#define DATABASE_ACCESS_H

#include "IDatabaseInterface.h"
#include "ConnectionPool.h"
#include "DatabaseFactory.h"
#include "DatabaseMonitor.h"
#include <memory>
#include <string>
#include <vector>
#include <functional>
#include <chrono>
#include <stdexcept>

/**
 * @brief 数据库访问类，用于封装数据库操作，并使用连接池
 */
class DatabaseAccess {
  public:
    /**
     * @brief 构造函数
     *
     * @param connectionPool 连接池
     * @param enableMonitoring 是否启用性能监控
     */
    explicit DatabaseAccess(std::shared_ptr<IConnectionPool> connectionPool = nullptr, bool enableMonitoring = true)
        : m_connectionPool(connectionPool ? connectionPool : DatabaseFactory::getConnectionPool()),
          m_enableMonitoring(enableMonitoring) {
    }

    /**
     * @brief 执行数据库操作
     *
     * @param operation 数据库操作函数
     * @param timeout 获取连接的超时时间（毫秒）
     * @return 操作结果
     */
    template <typename Result>
    Result execute(std::function<Result(std::shared_ptr<IDatabaseInterface>)> operation, int timeout = 0) {
        // 获取连接
        auto conn = m_connectionPool->getConnection(timeout);
        if (!conn) {
            throw std::runtime_error("无法获取数据库连接");
        }

        try {
            // 记录开始时间
            auto start = std::chrono::high_resolution_clock::now();

            // 执行操作
            Result result = operation(conn);

            // 记录结束时间
            auto end = std::chrono::high_resolution_clock::now();

            // 计算执行时间（毫秒）
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();

            // 如果启用了性能监控，则记录查询执行时间
            if (m_enableMonitoring) {
                // 获取最后执行的SQL语句
                std::string lastSql = conn->getLastExecutedSql();
                if (!lastSql.empty()) {
                    DatabaseMonitor::getInstance().recordQueryTime(lastSql, duration);
                }
            }

            // 释放连接
            m_connectionPool->releaseConnection(conn);

            return result;
        } catch (const std::exception &e) {
            // 释放连接
            m_connectionPool->releaseConnection(conn);

            // 重新抛出异常
            throw;
        }
    }

    /**
     * @brief 执行无返回值的数据库操作
     *
     * @param operation 数据库操作函数
     * @param timeout 获取连接的超时时间（毫秒）
     */
    void execute(std::function<void(std::shared_ptr<IDatabaseInterface>)> operation, int timeout = 0) {
        // 获取连接
        auto conn = m_connectionPool->getConnection(timeout);
        if (!conn) {
            throw std::runtime_error("无法获取数据库连接");
        }

        try {
            // 记录开始时间
            auto start = std::chrono::high_resolution_clock::now();

            // 执行操作
            operation(conn);

            // 记录结束时间
            auto end = std::chrono::high_resolution_clock::now();

            // 计算执行时间（毫秒）
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();

            // 如果启用了性能监控，则记录查询执行时间
            if (m_enableMonitoring) {
                // 获取最后执行的SQL语句
                std::string lastSql = conn->getLastExecutedSql();
                if (!lastSql.empty()) {
                    DatabaseMonitor::getInstance().recordQueryTime(lastSql, duration);
                }
            }

            // 释放连接
            m_connectionPool->releaseConnection(conn);
        } catch (const std::exception &e) {
            // 释放连接
            m_connectionPool->releaseConnection(conn);

            // 重新抛出异常
            throw;
        }
    }

    /**
     * @brief 执行查询操作
     *
     * @param query SQL查询语句
     * @param timeout 获取连接的超时时间（毫秒）
     * @return 查询结果
     */
    std::vector<std::vector<std::string>> query(const std::string &query, int timeout = 0) {
        return execute<std::vector<std::vector<std::string>>>(
            [&query](std::shared_ptr<IDatabaseInterface> conn) {
                return conn->query(query);
            },
            timeout);
    }

    /**
     * @brief 执行非查询操作
     *
     * @param query SQL非查询语句
     * @param timeout 获取连接的超时时间（毫秒）
     */
    void executeWithLock(const std::string &query, int timeout = 0) {
        execute(
            [&query](std::shared_ptr<IDatabaseInterface> conn) {
                conn->executeWithLock(query);
            },
            timeout);
    }

    /**
     * @brief 插入数据
     *
     * @param table 表名
     * @param values 要插入的列-值对
     * @param timeout 获取连接的超时时间（毫秒）
     */
    void insert(const std::string &table, const std::vector<std::pair<std::string, std::string>> &values, int timeout = 0) {
        execute(
            [&table, &values](std::shared_ptr<IDatabaseInterface> conn) {
                conn->insert(table, values);
            },
            timeout);
    }

    /**
     * @brief 更新数据
     *
     * @param table 表名
     * @param values 要更新的列-值对
     * @param condition 更新条件
     * @param timeout 获取连接的超时时间（毫秒）
     */
    void update(const std::string &table,
                const std::vector<std::pair<std::string, std::string>> &values,
                const std::string &condition,
                int timeout = -1) {
        execute(
            [&table, &values, &condition](std::shared_ptr<IDatabaseInterface> conn) {
                conn->update(table, values, condition);
            },
            timeout);
    }

    /**
     * @brief 删除数据
     *
     * @param table 表名
     * @param condition 删除条件
     * @param timeout 获取连接的超时时间（毫秒）
     */
    void remove(const std::string &table, const std::string &condition, int timeout = 0) {
        execute(
            [&table, &condition](std::shared_ptr<IDatabaseInterface> conn) {
                conn->remove(table, condition);
            },
            timeout);
    }

    /**
     * @brief 启用性能监控
     */
    void enableMonitoring() {
        m_enableMonitoring = true;
    }

    /**
     * @brief 禁用性能监控
     */
    void disableMonitoring() {
        m_enableMonitoring = false;
    }

    /**
     * @brief 获取性能监控状态
     *
     * @return bool 是否启用性能监控
     */
    bool isMonitoringEnabled() const {
        return m_enableMonitoring;
    }

    /**
     * @brief 获取连接池
     *
     * @return std::shared_ptr<IConnectionPool> 连接池
     */
    std::shared_ptr<IConnectionPool> getConnectionPool() const {
        return m_connectionPool;
    }

  private:
    std::shared_ptr<IConnectionPool> m_connectionPool; // 连接池
    bool m_enableMonitoring;                           // 是否启用性能监控
};

#endif // DATABASE_ACCESS_H