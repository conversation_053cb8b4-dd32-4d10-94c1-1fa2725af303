#include "ModbusDataHandler.h"
#include "ConfigManager.h"
#include "Logger.h"
#include <algorithm>
#include <stdexcept>

ModbusDataHandler::ModbusDataHandler(uint32_t dbAddr) : m_dbAddress(dbAddr) {
}

void ModbusDataHandler::parseData(ModbusData &data) {
    try {
        const std::string &registerType = data.registerType;
        const int startAddress = data.startAddress;
        const int length = data.length;
        const uint32_t dbAddr = data.dbAddr;

        if (dbAddr != m_dbAddress && m_dbAddress != 0) {
            LOG_ERROR << "数据包的 dbAddr 与 数据管理器的 dbAddr 不匹配, 终止解析 ";
            return;
        }

        // // 调试输出: 打印 m_conditionalParseConfigs 的键
        // LOG_INFO << "调试信息 - m_conditionalParseConfigs 键列表:";
        // for (const auto &entry : m_conditionalParseConfigs) {
        //     LOG_INFO << "  键: " << entry.first;
        //     LOG_INFO << " 值 (地址): " << entry.second->registerAddr;
        // }

        // const auto &configs = m_configList["输入寄存器"]["系统数据"];
        // auto config = configs.front();
        // uint16_t num = 15;
        // config.data->setData(&num);

        // auto regDataIt = m_dataMap["保持寄存器"]["系统数据"].find(0x0210);
        // if (regDataIt != m_dataMap["保持寄存器"]["系统数据"].end()) {
        //     auto datas = regDataIt->second->getData();
        //     std::cerr << "Warn: datas = " << (std::string)datas << std::endl;
        // } else {
        //     std::cerr << "Warn: datas = null" << std::endl;
        // }

        int offset = 0;
        while (offset < length) {
            int currentAddress = startAddress + offset;
            int remainingLength = length - offset;
            // 根据 registerType/子类 计算条件解析配置
            const std::string category = findCategoryByAddress(registerType, currentAddress);
            const std::string categoryPath = registerType + "/" + category;
            ConditionalParseConfig *config = nullptr;
            bool conditionMet = false;
            auto configIt = m_conditionalParseConfigs.find(categoryPath);
            if (configIt != m_conditionalParseConfigs.end()) {
                config = configIt->second.get();
                if (currentAddress >= config->startAddr && currentAddress <= config->endAddr) {
                    conditionMet = true;
                }
            }

            if (conditionMet) {
                // 找到条件解析范围内的长度
                int parseLength = std::min(remainingLength, config->endAddr - currentAddress + 1);

                ModbusData partialData = data;
                partialData.startAddress = currentAddress;
                partialData.length = parseLength;
                partialData.dataBuffer = std::vector<uint16_t>(data.dataBuffer.begin() + offset,
                                                               data.dataBuffer.begin() + offset + parseLength);

                parseConditionalData(partialData, config);
                offset += parseLength;
            } else {
                // 找到不在条件解析范围内的长度
                int parseLength = remainingLength;
                for (const auto &entry : m_conditionalParseConfigs) {
                    config = entry.second.get();
                    if (currentAddress < config->startAddr && currentAddress + parseLength > config->startAddr) {
                        parseLength = config->startAddr - currentAddress;
                        break;
                    }
                }

                int endOffset = offset + parseLength;
                while (offset < endOffset) {
                    int addr = startAddress + offset;
                    auto regTypeIt = m_addrToStaticRegInfoMap.find(registerType);
                    if (regTypeIt == m_addrToStaticRegInfoMap.end()) {
                        offset++;
                        continue;
                    }

                    auto regInfoIt = regTypeIt->second.find(addr);
                    if (regInfoIt != regTypeIt->second.end()) {
                        const auto &config = regInfoIt->second;

                        const auto &category = findCategoryByAddress(registerType, addr);
                        setDataWithEndian({registerType, category}, config.address, data.dataBuffer.data() + offset);
                        offset += config.length;
                    } else {
                        offset++;
                    }
                }
            }
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "捕获到异常: " << e.what();
    }
}

void ModbusDataHandler::parseConditionalData(const ModbusData &data, const ConditionalParseConfig *conditionalConfig) {
    try {
        const std::string &registerType = data.registerType;
        const std::string &category = findCategoryByAddress(registerType, data.startAddress);
        const int &startAddress = data.startAddress;
        const int &length = data.length;
        const std::vector<uint16_t> &dataBuffer = data.dataBuffer;

        if (category.empty()) {
            LOG_ERROR << "未找到寄存器类型: " << registerType << " 和地址: " << startAddress << " 对应的分类";
            return;
        }

        if (conditionalConfig == nullptr) {
            return;
        }

        // 获取条件寄存器的值
        auto regDataIt = m_dataMap[registerType][category].find(conditionalConfig->registerAddr);
        if (regDataIt == m_dataMap[registerType][category].end()) {
            LOG_ERROR << "在地址 " << conditionalConfig->registerAddr << " (寄存器类型: " << registerType
                      << ", 分类: " << category << ") 未找到条件寄存器值";
            return;
        }
        int conditionValue = std::stoi(regDataIt->second->getData());

        // 检查该条件配置的上一次条件值
        std::string categoryPath = registerType + "/" + category;
        auto lastValueIt = m_lastConditionValues.find(categoryPath);
        int lastConditionValue = (lastValueIt != m_lastConditionValues.end()) ? lastValueIt->second : -1;

        // 如果 conditionValue 与上一次不一样，则删除地址范围内的所有元素
        if (conditionValue != lastConditionValue) {
            // m_dataMap : 删除地址范围在 conditionalConfig->startAddr 到
            // conditionalConfig->endAddr 的所有元素
            for (auto it = m_dataMap[registerType][category].begin(); it != m_dataMap[registerType][category].end();) {
                if (it->first >= conditionalConfig->startAddr && it->first <= conditionalConfig->endAddr) {
                    it = m_dataMap[registerType][category].erase(it); // erase 之后 it 指向下一个元素
                } else {
                    ++it;
                }
            }

            // 更新该条件配置的 lastConditionValue
            m_lastConditionValues[categoryPath] = conditionValue;
        }

        // 解析数据
        auto parseMapIt = conditionalConfig->parseMap.find(conditionValue);
        if (parseMapIt == conditionalConfig->parseMap.end()) {
            LOG_WARNING << "在寄存器类型 " << registerType << " 和分类 " << category << " 的解析映射中未找到条件值 "
                        << conditionValue << "。可用条件值: ";

            LOG_WARNING << "跳过此数据块的条件解析。";
            return;
        }

        const auto &registerInfos = parseMapIt->second;
        for (const auto &regInfo : registerInfos) {
            if (startAddress <= regInfo.address && regInfo.address < startAddress + length) {
                regInfo.data->setData(dataBuffer.data() + (regInfo.address - startAddress));
            }
            // 将 registerInfos 中的所有数据引用添加到 m_dataMap
            m_dataMap[registerType][category][regInfo.address] = regInfo.data;
        }
    } catch (const std::runtime_error &e) {
        LOG_ERROR << "捕获到异常: " << e.what();
    }
}

void ModbusDataHandler::addCacheData(ModbusData &mdbsData) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_dataCache.push_back(std::move(mdbsData));
}

bool ModbusDataHandler::getRegisterAttributes(const RegisterPath &path, const std::string &name, DataAttributes &outAttributes) {
    // 首先，在动态（条件）配置中查找
    std::string categoryPath = path.registerType + "/" + path.category;
    auto itCondConfig = m_conditionalParseConfigs.find(categoryPath);
    if (itCondConfig != m_conditionalParseConfigs.end()) {
        const auto &config = itCondConfig->second;
        // 获取当前的条件值
        auto typeIt = m_dataMap.find(path.registerType);
        if (typeIt != m_dataMap.end()) {
            auto categoryIt = typeIt->second.find(path.category);
            if (categoryIt != typeIt->second.end()) {
                auto regDataIt = categoryIt->second.find(config->registerAddr);
                if (regDataIt != categoryIt->second.end()) {
                    int conditionValue = std::stoi(regDataIt->second->getData());
                    // 查找此值的解析映射
                    auto parseMapIt = config->parseMap.find(conditionValue);
                    if (parseMapIt != config->parseMap.end()) {
                        const auto &registerInfos = parseMapIt->second;
                        // 在活动的条件寄存器中按名称搜索
                        for (const auto &regInfo : registerInfos) {
                            if (regInfo.name == name) {
                                outAttributes.offset = regInfo.data->getOffset();
                                outAttributes.conversionFactor = regInfo.data->getConversionFactor();
                                outAttributes.size = regInfo.data->getSize();
                                return true;
                            }
                        }
                    }
                }
            }
        }
    }

    // 然后，在静态配置中查找
    const auto &configs = m_configList[path.registerType][path.category];
    for (const auto &config : configs) {
        if (config.name == name) {
            outAttributes.offset = config.data->getOffset();
            outAttributes.conversionFactor = config.data->getConversionFactor();
            outAttributes.size = config.data->getSize();
            // 其他属性可以根据需要设置
            return true; // 找到对应的寄存器信息
        }
    }
    return false; // 未找到对应的寄存器信息
}

const std::vector<RegisterConfig> &ModbusDataHandler::getRegConfigMap(const std::string &registerType) const {
    auto it = m_registerConfigMap.find(registerType);
    if (it == m_registerConfigMap.end()) {
        throw std::runtime_error("未找到寄存器类型: " + registerType);
    }
    return it->second; // 返回配置的引用
}

std::vector<ModbusData> ModbusDataHandler::getCacheDataBatch(size_t batchSize) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    std::vector<ModbusData> dataBatch;
    for (size_t i = 0; i < batchSize && !m_dataCache.empty(); ++i) {
        dataBatch.push_back(std::move(m_dataCache.front()));
        m_dataCache.pop_front(); // 确保已移动的元素不会再次被处理
    }
    return dataBatch;
}

std::string ModbusDataHandler::getDataByCategory(const RegisterPath &path, const std::string &name) {
    // 先尝试获取条件解析的值
    std::string categoryPath = path.registerType + "/" + path.category;
    auto itCondConfig = m_conditionalParseConfigs.find(categoryPath);
    if (itCondConfig != m_conditionalParseConfigs.end()) {
        const auto config = itCondConfig->second;

        // 获取条件寄存器的值
        auto regDataIt = m_dataMap[path.registerType][path.category].find(config->registerAddr);
        if (regDataIt != m_dataMap[path.registerType][path.category].end()) {
            int conditionValue = std::stoi(regDataIt->second->getData());

            if (config->parseMap.find(conditionValue) != config->parseMap.end()) {
                const auto &registerInfos = config->parseMap.find(conditionValue)->second;

                for (const auto &regInfo : registerInfos) {
                    if (regInfo.name == name) {
                        return regInfo.data->getData();
                    }
                }
            }
        }
    }

    // 尝试从bit类型的值
    auto data = getBitByCategory(path, name);
    if (!data.empty()) {
        return data;
    }

    // 如果没有找到条件解析的值，再尝试获取普通寄存器的值
    const auto &configs = m_configList[path.registerType][path.category];
    for (const auto &config : configs) {
        if (config.name == name) {
            auto dataIt = m_dataMap[path.registerType][path.category].find(config.address);
            if (dataIt != m_dataMap[path.registerType][path.category].end()) {
                return dataIt->second->getData();
            }
        }
    }

    LOG_ERROR << "在分类 '" << path.category << "' (类型: '" << path.registerType << "') 中未找到名称为 '" << name
              << "' 的 RegisterData。";
    return std::string();
}

const std::vector<std::string> &ModbusDataHandler::getBmsBmuDataList() {
    return bmsBmuDataList;
}

void ModbusDataHandler::initialize() {
    ConfigManager &configManager = ConfigManager::getInstance();
    const YAML::Node &config = configManager.getConfig();

    if (config.IsNull()) {
        LOG_ERROR << "加载YAML文件失败或配置为空。";
        return;
    }

    loadConfigFromFile(config);
}

void ModbusDataHandler::addRegister(const RegisterPath &path, const RegisterInfo &config) {
    auto &configList = m_configList[path.registerType][path.category];
    auto it = std::find_if(configList.begin(), configList.end(), [&config](const RegisterInfo &existingConfig) {
        return existingConfig.address == config.address;
    });

    if (it != configList.end()) {
        // 如果已经存在相同地址的配置，则更新或跳过
        LOG_ERROR << "警告: 地址 0x" << std::hex << config.address << " | 名称: " << config.name << " 的寄存器已存在。跳过添加。";
        return;
    }

    // 添加新的寄存器配置
    m_dataMap[path.registerType][path.category][config.address] = config.data;
    configList.push_back(config);
    m_addressToCategoryMap[path.registerType][config.address] = path.category;
    m_addrToStaticRegInfoMap[path.registerType][config.address] = config;
}

void ModbusDataHandler::setDataWithEndian(const RegisterPath &path, int address, uint16_t *data) {
    auto registerTypeIt = m_dataMap.find(path.registerType);
    if (registerTypeIt != m_dataMap.end()) {
        auto categoryIt = registerTypeIt->second.find(path.category);
        if (categoryIt != registerTypeIt->second.end()) {
            auto addressIt = categoryIt->second.find(address);
            if (addressIt != categoryIt->second.end()) {
                if (m_dbAddress != 0) {
                    // 32位数据为中端模式，需要转换成大端模式
                    addressIt->second->setDataEndian(data);
                }
                addressIt->second->setData(data);
                return;
            }
        }
    }
    LOG_ERROR << "未找到地址: " << address;
}

void ModbusDataHandler::parseRegisters(const YAML::Node &registerDataList, const std::string &registerType, const std::string &subCategory) {
    // 用于存储普通寄存器的列表
    YAML::Node regularRegisters;

    if (registerDataList["多模块扩展"] && registerDataList["模板"]) {
        parseModuleTemplates(registerDataList["多模块扩展"], registerDataList["模板"], registerType);
    } else {
        // 检查是否有"条件解析"子项
        for (const auto &registerItem : registerDataList) {
            if (registerItem["条件解析"]) {
                // 解析条件寄存器
                std::shared_ptr<ConditionalParseConfig> conditionalConfig = std::make_shared<ConditionalParseConfig>();

                conditionalConfig->registerName = registerItem["条件解析"]["condition"]["register"].as<std::string>();
                conditionalConfig->registerAddr = registerItem["条件解析"]["condition"]["registerAddr"].as<uint16_t>();
                conditionalConfig->startAddr = registerItem["条件解析"]["condition"]["startAddr"].as<uint16_t>();
                conditionalConfig->endAddr = registerItem["条件解析"]["condition"]["endAddr"].as<uint16_t>();

                for (const auto &parseCase : registerItem["条件解析"]["parse"]) {
                    int conditionValue = parseCase.first.as<int>();
                    const auto &DataList = parseCase.second;

                    std::vector<RegisterInfo> registerInfos = parseRegisterConfig(
                        DataList, registerType, subCategory, true, conditionValue, conditionalConfig.get());
                    conditionalConfig->parseMap[conditionValue] = registerInfos;

                    // 填充地址到分类的映射缓存
                    for (const auto &regInfo : registerInfos) {
                        m_addressToCategoryMap[registerType][regInfo.address] = subCategory;
                    }
                }

                // 使用 categoryPath 作为键直接存储条件解析配置
                std::string categoryPath = registerType + "/" + subCategory;
                m_conditionalParseConfigs[categoryPath] = conditionalConfig;
            } else {
                // 将普通寄存器添加到临时节点
                regularRegisters.push_back(registerItem);
            }
        }

        // 解析普通寄存器
        std::vector<RegisterInfo> registerInfos = parseRegisterConfig(regularRegisters, registerType, subCategory, false, -1, nullptr);
        for (const auto &config : registerInfos) {
            addRegister({registerType, subCategory}, config);
        }
    }
}

void ModbusDataHandler::parseModuleTemplates(const YAML::Node &modules, const YAML::Node &templateRegisters, const std::string &registerType) {
    for (const auto &module : modules) {
        std::string moduleName = module["name"].as<std::string>();
        int offset = module["offset"].as<int>();
        bmsBmuDataList.push_back(moduleName);

        YAML::Node adjustedRegisters;
        for (const auto &reg : templateRegisters) {
            YAML::Node adjustedReg = YAML::Clone(reg); // 克隆节点以避免修改原始模板

            // 通过添加模块的偏移量来调整地址
            adjustedReg["address"] = reg["address"].as<int>() + offset;
            adjustedRegisters.push_back(adjustedReg);
        }

        // 解析调整后的寄存器
        auto registerInfos = parseRegisterConfig(adjustedRegisters, registerType, moduleName, false, -1, nullptr);
        for (const auto &config : registerInfos) {
            addRegister({registerType, moduleName}, config);
        }
    }
}

std::vector<RegisterInfo> ModbusDataHandler::parseRegisterConfig(const YAML::Node &registerDataList,
                                                                 const std::string &registerType,
                                                                 const std::string &subCategory,
                                                                 const bool isConditional,
                                                                 int conditionValue,
                                                                 const ConditionalParseConfig *conditionalConfig) {
    std::vector<RegisterInfo> registerInfos;

    for (const auto &item : registerDataList) {
        if (!item["name"] || !item["address"] || !item["length"] || !item["type"]) {
            LOG_ERROR << "寄存器信息缺少必要的字段。";
            continue;
        }

        std::string name = item["name"].as<std::string>();
        int address = item["address"].as<int>();
        int length = item["length"].as<int>(); // 寄存器个数
        std::string type = item["type"].as<std::string>();
        bool readOnly = item["readOnly"].as<bool>(true);
        double factor = item["conversionFactor"].as<double>(1.0);
        int offset = item["offset"].as<int>(0);

        std::shared_ptr<RegisterData> data;
        if (type == "UInt16") {
            data = std::make_shared<UInt16Data>(factor, offset);
        } else if (type == "Int32") {
            data = std::make_shared<Int32Data>(factor, offset);
        } else if (type == "Bit") {
            auto bitData = std::make_shared<BitData>(length, factor);
            data = bitData;
            if (item["bits"]) {
                for (const auto &bitItem : item["bits"]) {
                    std::string bitKey = bitItem.first.as<std::string>();
                    parseBitItem(bitItem.second, bitKey, bitData, registerType, subCategory, address, isConditional, conditionValue, conditionalConfig);
                }
            }
        } else if (type == "Int16") {
            data = std::make_shared<Int16Data>(factor, offset);
        } else if (type == "UInt32") {
            data = std::make_shared<UInt32Data>(factor, offset);
        } else if (type == "String") {
            data = std::make_shared<StringData>(length);
        } else if (type == "MultiUInt16") {
            data = std::make_shared<MultiUInt16Data>(length);
        } else if (type == "Float32") {
            data = std::make_shared<Float32Data>(factor, offset);
        } else {
            LOG_ERROR << "未知数据类型: " << type;
            continue;
        }

        RegisterInfo config = {name, address, length, readOnly, data};
        registerInfos.push_back(config);
    }

    return registerInfos;
}

void ModbusDataHandler::parseReadMap(const YAML::Node &readMap) {
    for (const auto &registerTypeNode : readMap) {
        const std::string &registerType = registerTypeNode.first.as<std::string>();
        const auto &registerConfigs = registerTypeNode.second;

        std::vector<RegisterConfig> configs;
        for (const auto &regConfig : registerConfigs) {
            if (!regConfig["addr"] || !regConfig["length"] || !regConfig["intervalTime"] || !regConfig["saveDbTime"]) {
                LOG_ERROR << "寄存器配置缺少必要的字段。";
                continue;
            }

            RegisterConfig config;
            config.addr = regConfig["addr"].as<uint16_t>();
            config.length = regConfig["length"].as<size_t>();
            config.intervalTime = regConfig["intervalTime"].as<int>();
            config.saveDbTime = regConfig["saveDbTime"].as<int>();

            configs.push_back(config);
        }

        m_registerConfigMap[registerType] = configs;
    }
}

std::string ModbusDataHandler::getBitByCategory(const RegisterPath &path, const std::string &bitName) {
    // 先查找通用 bit 类型的数据
    auto it = m_bitNameToAddress.find(bitName);
    if (it != m_bitNameToAddress.end()) {
        auto typeCategoryIt = it->second.find(path.registerType + "/" + path.category);
        if (typeCategoryIt != it->second.end()) {
            auto [address, bitIndex, bitLength] = typeCategoryIt->second;
            const auto &dataIt = m_dataMap[path.registerType][path.category].find(address);
            if (dataIt != m_dataMap[path.registerType][path.category].end()) {
                auto bitData = std::dynamic_pointer_cast<BitData>(dataIt->second);
                if (bitData) {
                    return bitData->getData(bitIndex, bitLength);
                }
            }
        }
    }

    // 查找条件解析的 bit 类型的数据
    std::string categoryPath = path.registerType + "/" + path.category;
    auto condIt = m_conditionalBitNameToAddress.find(categoryPath);
    if (condIt != m_conditionalBitNameToAddress.end()) {
        auto condParseConfigIt = m_conditionalParseConfigs.find(categoryPath);
        if (condParseConfigIt != m_conditionalParseConfigs.end()) {
            const auto &config = condParseConfigIt->second;

            // 获取条件寄存器的值
            auto condRegDataIt = m_dataMap[path.registerType][path.category].find(config->registerAddr);
            if (condRegDataIt != m_dataMap[path.registerType][path.category].end()) {
                int conditionValue = std::stoi(condRegDataIt->second->getData());

                for (const auto &condConfigPair : condIt->second) {
                    const std::string &conditionRegisterName = condConfigPair.first;
                    const auto &conditionMap = condConfigPair.second;

                    if (conditionMap.find(conditionValue) != conditionMap.end()) {
                        const auto &bitMap = conditionMap.find(conditionValue)->second;
                        if (bitMap.find(bitName) != bitMap.end()) {
                            auto [address, bitIndex, bitLength] = bitMap.find(bitName)->second;
                            if (config->parseMap.find(conditionValue) != config->parseMap.end()) {
                                const auto &registerInfos = config->parseMap.find(conditionValue)->second;

                                for (const auto &regInfo : registerInfos) {
                                    if (regInfo.address == address) {
                                        auto bitData = std::dynamic_pointer_cast<BitData>(regInfo.data);
                                        if (bitData) {
                                            return bitData->getData(bitIndex, bitLength);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    return std::string();
}

void ModbusDataHandler::parseBitItem(const YAML::Node &bitItem,
                                     const std::string &bitKey,
                                     std::shared_ptr<BitData> &bitData,
                                     const std::string &registerType,
                                     const std::string &subCategory,
                                     int address,
                                     bool isConditional,
                                     int conditionValue,
                                     const ConditionalParseConfig *conditionalConfig) {
    std::string bitName;
    int bitIndex = std::stoi(bitKey.substr(3));
    int bitLength = 1; // 默认长度为 1

    if (bitKey.find('-') != std::string::npos) {
        auto pos = bitKey.find('-');
        bitIndex = std::stoi(bitKey.substr(3, pos - 3));
        bitLength = std::stoi(bitKey.substr(pos + 1)) - bitIndex + 1;
    }

    // 判断 bitItem 是否为映射类型
    if (bitItem.IsMap()) {
        if (bitItem["name"]) {
            bitName = bitItem["name"].as<std::string>();
        } else {
            LOG_ERROR << "错误: bitItem 映射中缺少 'name' 键\n";
            return;
        }
        if (bitItem["offset"]) {
            int bitOffset = bitItem["offset"].as<int>();
            bitData->setOffset(bitIndex, bitOffset);
        }
    } else if (bitItem.IsScalar()) {
        bitName = bitItem.as<std::string>();
    } else {
        LOG_ERROR << "错误: 无法识别的 bit item 格式\n";
        return;
    }

    if (isConditional && conditionalConfig != nullptr) {
        std::string conditionType = conditionalConfig->registerName;
        m_conditionalBitNameToAddress[registerType + "/" + subCategory][conditionType][conditionValue][bitName] = {
            address, bitIndex, bitLength};
    } else {
        m_bitNameToAddress[bitName][registerType + "/" + subCategory] = {address, bitIndex, bitLength};
    }
}

void ModbusDataHandler::writeData(const RegisterPath &path, const std::string &name, const std::string &value) {
    auto writePair = prepareWriteData(path, name, value);
    if (writePair) {
        std::vector<std::pair<int, std::any>> writeBatch;
        writeBatch.push_back(*writePair);

        {
            std::lock_guard<std::mutex> lock(m_writeQueueMutex);
            m_writeQueue.push(writeBatch);
        }
    } else {
        LOG_ERROR << "写入数据失败， name: " << name;
    }
}

void ModbusDataHandler::writeMultipleData(const RegisterPath &path,
                                          const std::vector<std::pair<std::string, std::string>> &nameValuePairs) {
    std::vector<std::pair<int, std::any>> writeBatch;
    std::vector<int> addresses;

    for (const auto &[name, value] : nameValuePairs) {
        auto writePair = prepareWriteData(path, name, value);
        if (writePair) {
            writeBatch.push_back(*writePair);
            addresses.push_back(writePair->first);
        }
    }

    if (writeBatch.empty()) {
        LOG_ERROR << "在 writeMultipleData 中没有有效的寄存器可写。";
        return;
    }

    // 检查地址是否连续
    bool isConsecutive = true;
    for (size_t i = 1; i < addresses.size(); ++i) {
        if (addresses[i] != addresses[i - 1] + 1) {
            isConsecutive = false;
            break;
        }
    }

    if (!isConsecutive) {
        LOG_ERROR << "寄存器地址不连续。writeMultipleData 只支持写入连续的寄存器。";
        return;
    }

    // 封装为一个批次并推入队列
    {
        std::lock_guard<std::mutex> lock(m_writeQueueMutex);
        m_writeQueue.push(writeBatch);
    }
}

std::optional<std::pair<int, std::any>> ModbusDataHandler::prepareWriteData(const RegisterPath &path,
                                                                            const std::string &name,
                                                                            const std::string &value) {
    std::optional<RegisterInfo> foundConfig;

    // 首先，在动态（条件）配置中查找
    std::string categoryPath = path.registerType + "/" + path.category;
    auto itCondConfig = m_conditionalParseConfigs.find(categoryPath);
    if (itCondConfig != m_conditionalParseConfigs.end()) {
        const auto &config = itCondConfig->second;
        auto typeIt = m_dataMap.find(path.registerType);
        if (typeIt != m_dataMap.end()) {
            auto categoryIt = typeIt->second.find(path.category);
            if (categoryIt != typeIt->second.end()) {
                auto regDataIt = categoryIt->second.find(config->registerAddr);
                if (regDataIt != categoryIt->second.end()) {
                    int conditionValue = std::stoi(regDataIt->second->getData());
                    auto parseMapIt = config->parseMap.find(conditionValue);
                    if (parseMapIt != config->parseMap.end()) {
                        const auto &registerInfos = parseMapIt->second;
                        for (const auto &regInfo : registerInfos) {
                            if (regInfo.name == name) {
                                foundConfig = regInfo;
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    // 如果未找到，则在静态配置中查找
    if (!foundConfig) {
        const auto &configs = m_configList[path.registerType][path.category];
        // 查找寄存器
        auto configIt = std::find_if(configs.begin(), configs.end(), [&name](const RegisterInfo &config) {
            return config.name == name;
        });

        if (configIt != configs.end()) {
            foundConfig = *configIt;
        }
    }

    if (!foundConfig) {
        LOG_ERROR << "在分类 '" << path.category << "' (类型: '" << path.registerType << "') 中未找到名称为 '" << name
                  << "' 的 RegisterInfo。";
        return std::nullopt;
    }

    const RegisterInfo &config = *foundConfig;

    // 检查是否只读
    if (config.readOnly) {
        LOG_ERROR << "寄存器 '" << name << "' 是只读的。";
        return std::nullopt;
    }

    auto dataIt = m_dataMap[path.registerType][path.category].find(config.address);
    if (dataIt == m_dataMap[path.registerType][path.category].end()) {
        LOG_ERROR << "在分类 '" << path.category << "' (类型: '" << path.registerType << "') 中未找到地址 "
                  << config.address << "。";
        return std::nullopt;
    }

    std::shared_ptr<RegisterData> data = dataIt->second;
    std::any newValue;

    try {
        if (std::dynamic_pointer_cast<UInt16Data>(data)) {
            newValue = static_cast<uint16_t>(std::stoul(value));
        } else if (std::dynamic_pointer_cast<Int32Data>(data)) {
            int32_t temp = std::stoi(value);
            if (m_dbAddress != 0) {
                // 中端字节序
                temp = ((temp & 0x0000FFFF) << 16) | ((temp & 0xFFFF0000) >> 16);
            }
            newValue = temp;
        } else if (std::dynamic_pointer_cast<UInt32Data>(data)) {
            uint32_t temp = std::stoul(value);
            if (m_dbAddress != 0) {
                // 中端字节序
                temp = ((temp & 0x0000FFFF) << 16) | ((temp & 0xFFFF0000) >> 16);
            }
            newValue = temp;
        } else if (std::dynamic_pointer_cast<Int16Data>(data)) {
            newValue = static_cast<int16_t>(std::stoi(value));
        } else if (std::dynamic_pointer_cast<Float32Data>(data)) {
            float floatValue = std::stof(value);
            uint32_t temp = *reinterpret_cast<uint32_t *>(&floatValue);
            if (m_dbAddress != 0) {
                // 中端字节序
                temp = ((temp & 0x0000FFFF) << 16) | ((temp & 0xFFFF0000) >> 16);
            }
            newValue = temp;
        } else if (auto bitData = std::dynamic_pointer_cast<BitData>(data)) {
            // 处理 BitData
            auto it = m_bitNameToAddress.find(name);
            if (it != m_bitNameToAddress.end()) {
                auto bitIt = it->second.find(path.registerType + "/" + path.category);
                if (bitIt != it->second.end()) {
                    auto [address, bitIndex, bitLength] = bitIt->second;
                    bool bitValue = static_cast<bool>(std::stoi(value));

                    std::string dataString = bitData->getData(bitIndex, bitLength);
                    if (dataString.empty()) {
                        LOG_ERROR << "错误: 位值 '" << name << "' 为空";
                        return std::nullopt;
                    }

                    uint16_t dataValue = std::stoi(dataString);
                    uint16_t mask = (1 << bitLength) - 1;
                    dataValue = (dataValue & ~(mask << bitIndex)) | (bitValue << bitIndex);

                    newValue = dataValue;
                } else {
                    LOG_ERROR << "在分类 '" << path.category << "' (类型: '" << path.registerType << "') 中未找到位 '"
                              << name << "'。";
                    return std::nullopt;
                }
            } else {
                LOG_ERROR << "在 bitNameToAddress 映射中未找到位名称 '" << name << "'。";
                return std::nullopt;
            }
        } else {
            LOG_ERROR << "寄存器 '" << name << "' 的数据类型未知。";
            return std::nullopt;
        }

        return std::make_pair(config.address, newValue);

    } catch (const std::invalid_argument &e) {
        LOG_ERROR << "寄存器 '" << name << "' 的值 '" << value << "' 参数无效。";
        LOG_ERROR << "错误: " << e.what();
    } catch (const std::out_of_range &e) {
        LOG_ERROR << "寄存器 '" << name << "' 的值 '" << value << "' 超出范围。";
        LOG_ERROR << "错误: " << e.what();
    } catch (const std::exception &e) {
        LOG_ERROR << "向寄存器 '" << name << "' 写入数据时发生未知异常。";
        LOG_ERROR << "错误: " << e.what();
    }

    return std::nullopt;
}

std::optional<std::vector<std::pair<int, std::any>>> ModbusDataHandler::getPendingWrite() {
    std::lock_guard<std::mutex> lock(m_writeQueueMutex);
    if (!m_writeQueue.empty()) {
        auto writeBatch = m_writeQueue.front();
        m_writeQueue.pop();
        return writeBatch;
    }
    return std::nullopt;
}

std::string ModbusDataHandler::findCategoryByAddress(const std::string &registerType, int address) const {
    auto typeIt = m_addressToCategoryMap.find(registerType);
    if (typeIt != m_addressToCategoryMap.end()) {
        auto addrIt = typeIt->second.find(address);
        if (addrIt != typeIt->second.end()) {
            return addrIt->second;
        }
    }
    return std::string();
}
