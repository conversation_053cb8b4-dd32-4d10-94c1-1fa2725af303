-- SQLite

SELECT name 
FROM sqlite_master 
WHERE type = 'table' 
ORDER BY name;

-- SELECT * FROM device_info;

-- SELECT * FROM  offline_device;

-- SELECT * FROM  meter_data;

-- SELECT * FROM device_258608384_202412;

-- SELECT * FROM device_4918794;

-- CREATE TABLE IF NOT EXISTS device_1_202401 (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     data TEXT,
--     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
-- );

-- CREATE TABLE IF NOT EXISTS device_1_202407 (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     data TEXT,
--     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
-- );

-- CREATE TABLE IF NOT EXISTS device_1_202411 (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     data TEXT,
--     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
-- );



-- CREATE TABLE IF NOT EXISTS device_2_202403 (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     data TEXT,
--     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
-- );

-- CREATE TABLE IF NOT EXISTS device_2_202405 (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     data TEXT,
--     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
-- );

-- CREATE TABLE IF NOT EXISTS device_2_202410 (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     data TEXT,
--     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
-- );

-- CREATE TABLE IF NOT EXISTS device_3_202412 (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     data TEXT,
--     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
-- );

