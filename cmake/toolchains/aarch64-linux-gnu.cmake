# 交叉编译到 ARM64 的标准 Toolchain 文件
# 使用系统已安装的 aarch64-linux-gnu 工具链和多架构根文件系统

set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)

# 指定交叉编译器
set(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)
set(CMAKE_ASM_COMPILER aarch64-linux-gnu-gcc)

# 使用主机多架构目录作为 sysroot（/usr/include/aarch64-linux-gnu 等）
# 对于已启用 multiarch 的 Ubuntu，留空等同于根目录"/"，但明示便于理解
set(CMAKE_SYSROOT "/")

# 允许 CMake 在多架构路径中自动查找库/头文件
set(CMAKE_FIND_ROOT_PATH "/")

# 不强制 ONLY 模式，以便 CMake 同时搜索 host & target 多架构路径
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY BOTH)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE BOTH)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE BOTH)

# 配置 pkg-config 环境，仅使用目标架构 .pc 文件
set(ENV{PKG_CONFIG_LIBDIR} "/usr/lib/aarch64-linux-gnu/pkgconfig") 