CompileFlags:
  Add:
    # 指定目标架构和编译器
    - "--target=aarch64-linux-gnu"
    - "--gcc-toolchain=/usr/aarch64-linux-gnu"
    # C++标准
    - "-std=gnu++17"
    # 系统头文件路径
    - "-I/usr/aarch64-linux-gnu/include/c++/9"
    - "-I/usr/aarch64-linux-gnu/include/c++/9/aarch64-linux-gnu"
    - "-I/usr/aarch64-linux-gnu/include/c++/9/backward"
    - "-I/usr/lib/gcc-cross/aarch64-linux-gnu/9/include"
    - "-I/usr/aarch64-linux-gnu/include"
    - "-I/usr/include/aarch64-linux-gnu"
    # 项目特定包含路径
    - "-I/usr/local/include"
    # 定义预处理器宏
    - "-D__aarch64__"
    - "-D_GNU_SOURCE"
  Remove:
    # 移除可能冲突的x86_64标志
    - "-m64"
    - "-m32"
    - "-march=native"

# 诊断配置
Diagnostics:
  Suppress:
    - "unknown-warning-option"
    - "unused-command-line-argument"
    - "unknown_argument"
  UnusedIncludes: None
  MissingIncludes: Relaxed

# 索引配置
Index:
  Background: Build
