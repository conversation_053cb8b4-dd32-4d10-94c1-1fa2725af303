#!/bin/bash

echo "=== WSL VPN 最终修复方案 ==="

# 重置网络配置
sudo ip route flush table main
sudo ip route flush cache

# 重新添加基本路由
sudo ip route add ***********/24 dev eth0 proto kernel scope link
sudo ip route add ***********/24 dev eth4 proto kernel scope link  
sudo ip route add ********/16 dev eth1 proto kernel scope link

# 设置默认路由优先级
sudo ip route add default via *********** dev eth0 metric 100
sudo ip route add default via *********** dev eth4 metric 200

# 添加VPN特定路由（请根据实际需要的IP段修改）
sudo ip route add *********/24 via ******** dev eth1 metric 50
sudo ip route add *********/24 via ******** dev eth1 metric 50

# 配置DNS
sudo tee /etc/resolv.conf > /dev/null << EOF
nameserver *******
nameserver *******
nameserver ***********
EOF

echo "网络配置完成，当前路由表："
ip route show

echo ""
echo "请测试以下连接："
echo "1. ping *******  # 测试外网"
echo "2. ping ***********  # 测试网关"
echo "3. ping <你的VPN目标IP>  # 测试VPN网络"
