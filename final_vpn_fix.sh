#!/bin/bash

echo "=== WSL VPN 默认路由方案 ==="
echo "目标IP: *********"

# 重置网络配置
sudo ip route flush table main
sudo ip route flush cache

# 重新添加基本路由
sudo ip route add ***********/24 dev eth0 proto kernel scope link
sudo ip route add ***********/24 dev eth4 proto kernel scope link
sudo ip route add ********/16 dev eth1 proto kernel scope link

# 关键：将VPN接口设置为默认路由（最高优先级）
echo "设置VPN接口为默认路由..."
sudo ip route add default via ******** dev eth1 metric 10

# 添加备用路由（较低优先级）
sudo ip route add default via *********** dev eth0 metric 100
sudo ip route add default via *********** dev eth4 metric 200

# 确保VPN目标网段路由正确
sudo ip route add *********/16 via ******** dev eth1 metric 1

# 配置DNS
sudo tee /etc/resolv.conf > /dev/null << EOF
nameserver *******
nameserver *******
nameserver ***********
EOF

echo "网络配置完成，当前路由表："
ip route show

echo ""
echo "测试连通性："
echo "1. 测试VPN网关连通性..."
ping -c 3 ********

echo "2. 测试目标服务器连通性..."
ping -c 3 *********

echo "3. 测试外网连通性..."
ping -c 3 *******

echo ""
echo "如果VPN网关(********)能ping通，但目标IP(*********)不通，"
echo "说明VPN服务器端路由配置问题，需要检查VPN服务器的路由表。"
