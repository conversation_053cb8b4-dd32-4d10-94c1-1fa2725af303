#!/bin/bash

LOGFILE="/var/log/nm-dispatcher.log"
ACTIONFILE="/tmp/last_nm_action"
INTERFACE=$1
ACTION=$2

INTERFACE="net1"
TIMEOUT=15
FLAGFILE="/tmp/nm-dispatcher-net1-up.flag"
EXITFILE="/tmp/nm-dispatcher-net1-down.flag"
LED1_MODE="/sys/class/leds/led1/trigger"
LED1_STATUS="/sys/class/leds/led1/brightness"

# 检测文件大小大于2MB则重新创建
if [ -e "$LOGFILE" ]; then
    LOGSIZE=$(stat -c%s "$LOGFILE")
    if [ $LOGSIZE -gt 2097152 ]; then
        rm -f $LOGFILE
    fi
fi

# 记录脚本开始执行的时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 脚本开始执行 " >> $LOGFILE

# 读取上一次的 Action
if [ -e $ACTIONFILE ]; then
    LAST_ACTION=$(cat $ACTIONFILE)
else
    LAST_ACTION="none"
fi


if [ "$INTERFACE" = "net1" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 检查接口: $INTERFACE ,当前动作: $ACTION " >> $LOGFILE
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 上一次动作: $LAST_ACTION " >> $LOGFILE


    if [ "$ACTION" = "up" ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始处理 up 动作" >> $LOGFILE
        echo $ACTION > $ACTIONFILE
        
         # 检查物理连接状态
        LINK_STATUS=$(ethtool $INTERFACE | grep "Link detected" | awk '{print $3}')

        if [ "$LINK_STATUS" = "no" ]; then
            if [ -e $FLAGFILE ]; then
                echo -e "$(date '+%Y-%m-%d %H:%M:%S') - 开启（up）标志文件已存在，清除up标志位。" >> $LOGFILE
                rm -f $FLAGFILE
        	fi
        	echo -e "$(date '+%Y-%m-%d %H:%M:%S') - 接口 $INTERFACE 无物理连接，跳过开启流程。\n" >> $LOGFILE
            exit 0
        fi

        if [ -e $FLAGFILE ]; then
            echo -e "$(date '+%Y-%m-%d %H:%M:%S') - 开启（up）标志文件已存在，跳过 DHCP 配置。\n" >> $LOGFILE
            rm -f $FLAGFILE
            exit 0
        fi

        # 通过 NetworkManager 管理连接
        #nmcli con up id net1-dhcp

        dhclient -r net1
        dhclient net1

        # 如果未能获取到 IP 地址，启用本地 DHCP 服务器
       # sleep $TIMEOUT
        ip_addr=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
        if [ -z "$ip_addr" ]; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') - Failed: 在 $TIMEOUT 秒内未能从 DHCP 服务器获取 IP，启动本地 DHCP 服务器" >> $LOGFILE
            #nmcli con down id net1-dhcp

            # 创建标记文件以避免无限循环
            touch $FLAGFILE

            # 使能静态配置,启动本地 DHCP 服务器
            nmcli connection up net1-static
            sudo systemctl start isc-dhcp-server

            # 开启LED1提示灯
            echo "none" > $LED1_MODE
            echo 1 >  $LED1_STATUS
            echo "heartbeat" > $LED1_MODE

        else
	   # 设置DHCP自动连接
	  # nmcli connection modify net1-dhcp connection.autoconnect yes
           echo "$(date '+%Y-%m-%d %H:%M:%S') - Success: DHCP 服务器分配了 IP: $ip_addr" >> $LOGFILE
        fi
        #killall dhclient $INTERFACE

        rm -f $EXITFILE

    elif [ "$ACTION" = "down" ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 处理 down 动作" >> $LOGFILE

        echo $ACTION > $ACTIONFILE
        
        # 检查物理连接状态
        LINK_STATUS=$(ethtool $INTERFACE | grep "Link detected" | awk '{print $3}')

        if [ "$LINK_STATUS" = "no" ]; then
            if [ -e $EXITFILE ]; then
                echo "$(date '+%Y-%m-%d %H:%M:%S') - 退出（down）标志文件已存在，取消执行手动重启net1" >> $LOGFILE
                rm -f $EXITFILE
            else 
            	echo "$(date '+%Y-%m-%d %H:%M:%S') - 接口 $INTERFACE 没有物理连接，手动重启接口" >> $LOGFILE
            	# 前提：把 静态连接 和 动态连接 命令开启，把动态自动连接设置no，静态自动连接设置yes。
		        nmcli connection modify net1-dhcp connection.autoconnect no
            	nmcli device set net1 managed no
            	nmcli device set net1 managed yes

            	# 创建退出标记文件以避免无限循环
            	touch $EXITFILE
            fi
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 接口 $INTERFACE 被系统软关闭" >> $LOGFILE
        fi

        if [ -e $FLAGFILE ]; then
            echo -e "$(date '+%Y-%m-%d %H:%M:%S') - 开启（up）标志文件已存在，跳过处理 down 动作。\n" >> $LOGFILE
            exit 0
        fi

        # 设置 DHCP 优先级优先
        nmcli connection modify net1-dhcp connection.autoconnect-priority 0
        nmcli connection modify net1-static connection.autoconnect-priority 2
        # 超时时间
        nmcli con modify net1-dhcp ipv4.dhcp-timeout $TIMEOUT
        sudo systemctl stop isc-dhcp-server
	killall dhclient $INTERFACE

        #恢复 LED1 为默认状态
        echo "none" > $LED1_MODE
        echo 0 > $LED1_STATUS

    elif [ "$ACTION" = "connectivity-change" ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 处理 connectivity-change 动作" >> $LOGFILE
        echo $ACTION > $ACTIONFILE

       # if [ "$LED1_STATUS" = "down" ]; then
        #    rm -f $FLAGFILE

            # 设置 DHCP 优先级优先
         #   nmcli connection modify net1-dhcp connection.autoconnect-priority 0
          #  nmcli connection modify net1-static connection.autoconnect-priority 2
            # 超时时间
          #  nmcli con modify net1-dhcp ipv4.dhcp-timeout $TIMEOUT
           # sudo systemctl stop isc-dhcp-server

            #恢复 LED1 为默认状态
           # echo "none" > $LED1_MODE
        #    echo 0 > $LED1_STATUS
       # fi
    fi
    # 更新 LAST_ACTION 为当前的 ACTION
    echo $ACTION > $ACTIONFILE
fi

# 记录脚本结束的时间
echo -e "$(date '+%Y-%m-%d %H:%M:%S') - 脚本执行完毕: 接口= $INTERFACE, 动作= $ACTION \n" >> $LOGFILE

exit 0

