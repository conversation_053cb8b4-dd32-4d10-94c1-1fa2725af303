[Unit]
Description=Activate net2-static Connection
# Wait for the net2 device to appear before starting
BindsTo=sys-subsystem-net-devices-net2.device
After=sys-subsystem-net-devices-net2.device NetworkManager.service network-online.target
Wants=NetworkManager.service network-online.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/activate-net2-static.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target