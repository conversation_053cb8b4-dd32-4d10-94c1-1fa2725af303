#!/bin/bash
/usr/bin/nmcli con up net2-static

# 设置较大的 metric 值,降低优先级,目的防止网络选中该默认路由作为出口
/usr/bin/nmcli con modify net2-static ipv4.route-metric 300 

# 重新应用 net2 设备配置
/usr/bin/nmcli device reapply net2

# 存放路径 /usr/local/bin/activate-net2-static.sh
# 赋予脚本可执行权限: sudo chmod +x /usr/local/bin/activate-net2-static.sh

# 与 activate-net2-static.servic 配套使用
# activate-net2-static.servic 存放路径：/etc/systemd/system/activate-net2-static.service

# 赋予servic可执行权限: sudo chmod +x /etc/systemd/system/activate-net2-static.service

# 启用并启动 NetworkManager-wait-online.service： sudo systemctl enable NetworkManager-wait-online.service , sudo systemctl start NetworkManager-wait-online.service

# 重新加载 systemd 配置并启用服务:  sudo systemctl daemon-reload , sudo systemctl enable activate-net2-static.service

