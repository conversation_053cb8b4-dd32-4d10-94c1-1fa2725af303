cmake_minimum_required(VERSION 3.15)

# 设置系统名称和处理器架构（在 project() 函数之前）
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)

# 设置交叉编译的编译器（在 project() 函数之前）
set(CMAKE_C_COMPILER /usr/bin/aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER /usr/bin/aarch64-linux-gnu-g++)

# 设置查找路径
set(CMAKE_FIND_ROOT_PATH /usr)

# 设置环境变量以指向正确的 pkg-config 文件
set(ENV{PKG_CONFIG_LIBDIR} "/usr/lib/aarch64-linux-gnu/pkgconfig")

# 调用 project() 函数
project(demo)

set(CMAKE_CXX_STANDARD 17)

# 重新查找 OpenSSL 库
find_package(OpenSSL REQUIRED)

# 查找 SQLite3 库
find_package(SQLite3 REQUIRED)

# 查找 MySQL 库
find_package(PkgConfig REQUIRED)
pkg_check_modules(MYSQL REQUIRED mysqlclient)

# 查找 zlib 库
find_package(ZLIB REQUIRED)

# 查找 spdlog（使用编译库版本）
find_package(spdlog REQUIRED)

# 使用 pkg-config 查找 libmodbus
find_package(PkgConfig REQUIRED)
pkg_check_modules(MODBUS REQUIRED libmodbus)

# 手动设置 PahoMqttCpp 库路径（绕过find_package问题）
set(PAHO_MQTT_CPP_INCLUDE_DIR "/usr/local/include")
set(PAHO_MQTT_CPP_LIBRARIES "/usr/local/lib/libpaho-mqttpp3.a")
set(PAHO_MQTT_C_LIBRARIES "/usr/local/lib/libpaho-mqtt3as.a")

# 添加子目录
add_subdirectory(common)
add_subdirectory(samples)
add_subdirectory(src)

# 添加可执行文件
add_executable(main main.cpp)

# 设置 common_lib 的包含目录
target_include_directories(common_lib PUBLIC
  ${CMAKE_CURRENT_SOURCE_DIR}/common
  ${SQLite3_INCLUDE_DIRS}
  ${MYSQL_INCLUDE_DIRS}
)

# 设置 src_lib 的包含目录
target_include_directories(src_lib PUBLIC 
  ${CMAKE_CURRENT_SOURCE_DIR}/src
  ${CMAKE_CURRENT_SOURCE_DIR}/common
  ${SQLite3_INCLUDE_DIRS}
  ${MYSQL_INCLUDE_DIRS}
)

# 链接库
target_link_libraries(main PRIVATE 
  src_lib 
  common_lib 
  samples_lib 
  pthread 
  ${MODBUS_LIBRARIES} 
  sqlite3 
  ${MYSQL_LIBRARIES}
  OpenSSL::SSL 
  OpenSSL::Crypto 
  ZLIB::ZLIB
)

target_compile_options(main PRIVATE ${MODBUS_CFLAGS_OTHER})
target_link_options(main PRIVATE ${MODBUS_LDFLAGS})
