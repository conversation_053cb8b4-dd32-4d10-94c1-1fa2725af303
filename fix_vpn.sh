#!/bin/bash

# WSL VPN 修复脚本
echo "=== WSL VPN 连通性修复脚本 ==="

# 获取Windows主机IP
WIN_HOST=$(ip route show | grep default | awk '{print $3}' | head -1)
echo "Windows主机IP: $WIN_HOST"

# 备份当前路由表
echo "备份当前路由表..."
ip route show > /tmp/routes_backup.txt

# 删除冲突的默认路由
echo "清理路由表..."
sudo ip route del default via *********** dev eth4 2>/dev/null || true
sudo ip route del default via *********** dev eth0 2>/dev/null || true

# 设置通过Windows主机的默认路由
echo "设置默认路由通过Windows主机..."
sudo ip route add default via $WIN_HOST dev eth0 metric 1

# 添加VPN网络的特定路由
echo "添加VPN网络路由..."
sudo ip route add *********/24 via ******** dev eth1 2>/dev/null || true
sudo ip route add *********/24 via ******** dev eth1 2>/dev/null || true

# 配置DNS
echo "配置DNS..."
sudo tee /etc/resolv.conf > /dev/null << EOF
# VPN DNS配置
nameserver *******
nameserver *******
nameserver $WIN_HOST
EOF

# 显示新的路由表
echo "新的路由表:"
ip route show

echo "=== 修复完成 ==="
echo "请测试连通性: ping <远端VPN IP>"
