---
description: Used for developing and writing code
globs: 
alwaysApply: false
---
# BlockBox开发指南

## 开发环境设置

BlockBox项目基于C++开发，使用CMake构建系统。完整的构建过程在 [CMakeLists.txt](mdc:CMakeLists.txt) 中定义。

### 构建系统
- 主CMake文件: [CMakeLists.txt](mdc:CMakeLists.txt)
- Common库CMake文件: [common/CMakeLists.txt](mdc:common/CMakeLists.txt)
- Src目录CMake文件: [src/CMakeLists.txt](mdc:src/CMakeLists.txt)

### 代码格式
代码风格由 [.clang-format](mdc:.clang-format) 定义，确保一致的代码风格。

## 开发流程

### 添加新设备支持
1. 在 [config/configs.yaml](mdc:config/configs.yaml) 中添加设备的寄存器定义
2. 如果设备有特殊数据格式，创建对应的数据处理器
3. 修改 [common/RegisterData.h](mdc:common/RegisterData.h) 添加必要的数据类型转换支持
4. 测试与新设备的通信

### 添加新通信协议
1. 在 [src/](mdc:src) 目录创建新的任务类（参考现有的RTU/TCP/UDP任务实现）
2. 实现协议特定的通信逻辑
3. 创建对应的数据处理器
4. 在 [src/TaskStartup.cpp](mdc:src/TaskStartup.cpp) 中注册新任务

### 扩展数据库功能
1. 确定是需要修改现有数据库实现还是创建新的数据库接口
2. 对于SQLite修改，更新 [common/iSQLiteDB.cpp](mdc:common/iSQLiteDB.cpp) 或 [src/qSQLiteDB.cpp](mdc:src/qSQLiteDB.cpp)
3. 对于MySQL修改，更新 [common/IMySQLDB.cpp](mdc:common/IMySQLDB.cpp) 或 [src/qMySQLDB.cpp](mdc:src/qMySQLDB.cpp)
4. 更新数据库工厂 [common/DatabaseFactory.cpp](mdc:common/DatabaseFactory.cpp)

## 错误处理

### 日志记录
使用 [common/Logger.h](mdc:common/Logger.h) 提供的日志功能记录系统运行状态和错误：
```cpp

// 初始化 Logger（只需在程序入口调用一次）
Logger &logger = Logger::getInstance();
logger.initialize("BlockBox");
logger.setLogLevel(Logger::INFO);
logger.addConsoleOutput();
logger.addFileOutput("blockbox.log");

Logger::getInstance().debug("调试信息");
Logger::getInstance().info("一般信息");
Logger::getInstance().warn("警告信息");
Logger::getInstance().error("错误信息");
```

### 异常处理
采用错误代码和状态报告机制，避免在关键路径中使用C++异常。

### 日志系统规范

#### 禁用的标准输出函数
- `std::cout` / `std::cerr`
- `printf` / `fprintf`
- Qt 调试输出（`qDebug`, `qWarning`, `qCritical`）


#### 日志级别与宏用法
| 级别 | 宏           | 用途描述           |
|------|--------------|--------------------|
| TRACE| `LOG_TRACE`  | 细粒度跟踪         |
| DEBUG| `LOG_DEBUG`  | 调试信息           |
| INFO | `LOG_INFO`   | 关键运行信息       |
| WARNING | `LOG_WARNING` | 可恢复警告     |
| ERROR| `LOG_ERROR`  | 错误但仍可继续运行 |
| CRITICAL | `LOG_CRITICAL` | 致命错误，需要立即处理 |

## 性能优化

### 数据库优化
- 使用连接池 [common/ConnectionPool.h](mdc:common/ConnectionPool.h) 管理数据库连接
- 批量插入数据而非单条记录插入
- 定期维护数据库索引

### 通信优化
- 合理设置轮询间隔
- 对相同设备的请求进行批处理
- 使用异步通信减少等待时间

## 文档规范
- 所有公共接口应有清晰的注释
- 复杂算法需要详细说明
- 代码变更应更新相关文档
