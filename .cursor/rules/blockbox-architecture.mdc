---
description: 
globs: 
alwaysApply: true
---
# BlockBox项目架构指南

## 项目概述

BlockBox是一个模块化的工业数据采集和控制系统，专为能源存储系统设计。它提供了Modbus RTU（RS485）、Modbus TCP和UDP通信协议的接口，用于从各种设备（如BMS、PCS和冷却系统）收集数据，并将数据存储在数据库中。

## 系统入口点

系统的主入口点是 [main.cpp](mdc:main.cpp)，它负责初始化系统组件并启动任务管理器。

## 架构组件

### 核心控制层
- **主应用程序**: [main.cpp](mdc:main.cpp)
- **任务管理**: [common/TaskManager.h](mdc:common/TaskManager.h), [common/TaskManager.cpp](mdc:common/TaskManager.cpp), [src/TaskStartup.h](mdc:src/TaskStartup.h), [src/TaskStartup.cpp](mdc:src/TaskStartup.cpp)
- **监控系统**: [common/Watchdog.h](mdc:common/Watchdog.h), [common/Watchdog.cpp](mdc:common/Watchdog.cpp)
- **配置管理**: [common/ConfigManager.h](mdc:common/ConfigManager.h), [common/ConfigManager.cpp](mdc:common/ConfigManager.cpp)

### 通信层
- **Modbus RTU**: [common/ModbusRtuMaster.h](mdc:common/ModbusRtuMaster.h), [common/ModbusRtuMaster.cpp](mdc:common/ModbusRtuMaster.cpp), [src/RtuTask.h](mdc:src/RtuTask.h), [src/RtuTask.cpp](mdc:src/RtuTask.cpp)
- **Modbus TCP**: [common/ModbusTcpClient.h](mdc:common/ModbusTcpClient.h), [common/ModbusTcpClient.cpp](mdc:common/ModbusTcpClient.cpp), [src/TcpTask.h](mdc:src/TcpTask.h), [src/TcpTask.cpp](mdc:src/TcpTask.cpp)
- **UDP通信**: [src/UdpTask.h](mdc:src/UdpTask.h), [src/UdpTask.cpp](mdc:src/UdpTask.cpp), [src/UdpBroadcastReceiver.h](mdc:src/UdpBroadcastReceiver.h), [src/UdpBroadcastReceiver.cpp](mdc:src/UdpBroadcastReceiver.cpp)

### 数据处理层
- **通用Modbus数据处理**: [common/ModbusDataHandler.h](mdc:common/ModbusDataHandler.h), [common/ModbusDataHandler.cpp](mdc:common/ModbusDataHandler.cpp)
- **RTU数据处理**: [src/MdbsRtuDataHandler.h](mdc:src/MdbsRtuDataHandler.h), [src/MdbsRtuDataHandler.cpp](mdc:src/MdbsRtuDataHandler.cpp), [src/MdbsRtuDataManager.h](mdc:src/MdbsRtuDataManager.h), [src/MdbsRtuDataManager.cpp](mdc:src/MdbsRtuDataManager.cpp)
- **TCP数据处理**: [src/MdbsTcpDataHandler.h](mdc:src/MdbsTcpDataHandler.h), [src/MdbsTcpDataHandler.cpp](mdc:src/MdbsTcpDataHandler.cpp), [src/MdbsTcpDataManager.h](mdc:src/MdbsTcpDataManager.h), [src/MdbsTcpDataManager.cpp](mdc:src/MdbsTcpDataManager.cpp)
- **UDP数据处理**: [src/UdpClientDataHandler.h](mdc:src/UdpClientDataHandler.h), [src/UdpClientDataHandler.cpp](mdc:src/UdpClientDataHandler.cpp)

### 数据库层
- **数据库接口**: [common/DatabaseAccess.h](mdc:common/DatabaseAccess.h), [common/IDatabaseInterface.h](mdc:common/IDatabaseInterface.h)
- **SQLite实现**: [common/iSQLiteDB.h](mdc:common/iSQLiteDB.h), [common/iSQLiteDB.cpp](mdc:common/iSQLiteDB.cpp), [src/qSQLiteDB.h](mdc:src/qSQLiteDB.h), [src/qSQLiteDB.cpp](mdc:src/qSQLiteDB.cpp)
- **MySQL实现**: [common/IMySQLDB.h](mdc:common/IMySQLDB.h), [common/IMySQLDB.cpp](mdc:common/IMySQLDB.cpp), [src/qMySQLDB.h](mdc:src/qMySQLDB.h), [src/qMySQLDB.cpp](mdc:src/qMySQLDB.cpp)
- **数据库工厂**: [common/DatabaseFactory.h](mdc:common/DatabaseFactory.h), [common/DatabaseFactory.cpp](mdc:common/DatabaseFactory.cpp)
- **数据库监控**: [common/DatabaseMonitor.h](mdc:common/DatabaseMonitor.h), [common/DatabaseMonitor.cpp](mdc:common/DatabaseMonitor.cpp)

### 工具类
- **日志系统**: [common/Logger.h](mdc:common/Logger.h), [common/Logger.cpp](mdc:common/Logger.cpp)
- **字节序工具**: [common/ByteOrderUtils.h](mdc:common/ByteOrderUtils.h), [common/ByteOrderUtils.cpp](mdc:common/ByteOrderUtils.cpp)
- **压缩工具**: [common/CompressionUtils.h](mdc:common/CompressionUtils.h), [common/CompressionUtils.cpp](mdc:common/CompressionUtils.cpp)

### 数据结构
- **通用数据结构**: [common/StructDefs.h](mdc:common/StructDefs.h)
- **寄存器数据**: [common/RegisterData.h](mdc:common/RegisterData.h), [common/RegisterData.cpp](mdc:common/RegisterData.cpp)

## 配置文件
- **主配置文件**: [config/configs.yaml](mdc:config/configs.yaml)

## 数据流概览

BlockBox系统中的数据流遵循以下路径：

```
+---------------+      +---------------+      +---------------+
|    设备层     |----->|   通信协议层  |----->|  数据处理层   |
| (BMS/PCS/冷却)|      |(Modbus/UDP)   |      |(DataHandlers) |
+---------------+      +---------------+      +---------------+
                                                     |
                                                     v
                       +---------------+      +---------------+
                       |    用户界面   |<-----|   数据库层    |
                       |   (监控/控制) |      |(SQLite/MySQL) |
                       +---------------+      +---------------+
```

## 数据采集路径

### Modbus RTU数据流
1. [src/RtuTask.cpp](mdc:src/RtuTask.cpp) 通过循环轮询RS485设备
2. [common/ModbusRtuMaster.cpp](mdc:common/ModbusRtuMaster.cpp) 处理Modbus RTU协议通信
3. [src/MdbsRtuDataHandler.cpp](mdc:src/MdbsRtuDataHandler.cpp) 处理接收到的数据
4. [src/MdbsRtuDataManager.cpp](mdc:src/MdbsRtuDataManager.cpp) 管理数据并准备存储
5. 通过数据库接口将数据存入数据库

### Modbus TCP数据流
1. [src/TcpTask.cpp](mdc:src/TcpTask.cpp) 管理TCP连接和设备通信
2. [common/ModbusTcpClient.cpp](mdc:common/ModbusTcpClient.cpp) 处理Modbus TCP协议
3. [src/MdbsTcpDataHandler.cpp](mdc:src/MdbsTcpDataHandler.cpp) 处理接收到的TCP数据
4. [src/MdbsTcpDataManager.cpp](mdc:src/MdbsTcpDataManager.cpp) 管理数据并准备存储
5. 通过数据库接口将数据存入数据库

### UDP数据流
1. [src/UdpTask.cpp](mdc:src/UdpTask.cpp) 管理UDP通信任务
2. [src/UdpBroadcastReceiver.cpp](mdc:src/UdpBroadcastReceiver.cpp) 接收广播数据
3. [src/UdpClientDataHandler.cpp](mdc:src/UdpClientDataHandler.cpp) 处理UDP数据
4. 通过数据库接口将数据存入数据库

## 数据库操作流

### 数据存储
1. 数据处理器将处理好的数据传递给数据库适配器 [src/DatabaseAdapter.cpp](mdc:src/DatabaseAdapter.cpp)
2. 数据库工厂 [common/DatabaseFactory.cpp](mdc:common/DatabaseFactory.cpp) 创建适当的数据库连接
3. 根据配置，使用SQLite [common/iSQLiteDB.cpp](mdc:common/iSQLiteDB.cpp) 或 MySQL [common/IMySQLDB.cpp](mdc:common/IMySQLDB.cpp) 实现存储数据

### 数据监控
1. [common/DatabaseMonitor.cpp](mdc:common/DatabaseMonitor.cpp) 监控数据库操作和性能
2. 记录数据库统计信息和潜在问题

## 关键数据结构

- **RegisterInfo**: 在 [common/RegisterData.h](mdc:common/RegisterData.h) 中定义，描述Modbus寄存器
- **ModbusData**: 在 [common/ModbusDataHandler.h](mdc:common/ModbusDataHandler.h) 中定义，存储Modbus数据
- **DataRecord**: 在 [common/StructDefs.h](mdc:common/StructDefs.h) 中定义，用于数据库记录
- **EnergyData**: 在 [common/StructDefs.h](mdc:common/StructDefs.h) 中定义，包含能源系统数据

## 数据转换和处理

- 字节序处理: [common/ByteOrderUtils.cpp](mdc:common/ByteOrderUtils.cpp)
- 数据压缩: [common/CompressionUtils.cpp](mdc:common/CompressionUtils.cpp)
- 寄存器数据转换: [common/RegisterData.cpp](mdc:common/RegisterData.cpp)

## 日志系统原则

BlockBox 采用集中式日志架构。所有模块必须通过 [Logger](mdc:common/Logger.h) 进行日志输出，禁止以下标准输出方式：`std::cout`, `std::cerr`, `printf`, `fprintf` 以及任何等价调试输出。此原则确保日志格式统一、便于筛查与持久化。

## 代码风格约定

- **命名规范**:
  - 类名: PascalCase
  - 方法和函数: camelCase
  - 成员变量: m_camelCase
  - 常量: UPPER_CASE

- **扩展规则**:
  - 新设备支持: 添加寄存器定义到配置文件，必要时创建数据处理器
  - 新通信协议: 在src目录创建新任务类，实现协议通信逻辑，创建数据处理器，在TaskStartup中注册
