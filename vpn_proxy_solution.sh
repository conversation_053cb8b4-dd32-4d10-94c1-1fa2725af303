#!/bin/bash

echo "=== WSL VPN 代理解决方案 ==="
echo "目标: 通过Windows主机访问VPN网络 *********"

# 获取Windows主机IP
WIN_HOST=$(ip route | grep "***********/24" | awk '{print $3}' | head -1)
if [ -z "$WIN_HOST" ]; then
    WIN_HOST="***********"
fi

echo "Windows主机IP: $WIN_HOST"

# 重置并配置路由
echo "配置路由表..."
sudo ip route flush table main
sudo ip route flush cache

# 基础网络路由
sudo ip route add ***********/24 dev eth0 proto kernel scope link
sudo ip route add ***********/24 dev eth4 proto kernel scope link

# 关键：将VPN目标网段路由到Windows主机
# Windows主机会通过VPN转发这些请求
sudo ip route add *********/16 via $WIN_HOST dev eth0 metric 1
sudo ip route add ********/16 via $WIN_HOST dev eth0 metric 1

# 设置默认路由
sudo ip route add default via $WIN_HOST dev eth0 metric 100

# 配置DNS
sudo tee /etc/resolv.conf > /dev/null << EOF
nameserver *******
nameserver *******
nameserver $WIN_HOST
EOF

echo "路由配置完成："
ip route show

echo ""
echo "测试连通性："

echo "1. 测试Windows主机连通性..."
ping -c 2 $WIN_HOST

echo "2. 测试外网连通性..."
ping -c 2 *******

echo "3. 测试VPN目标服务器..."
ping -c 3 *********

echo ""
echo "=== 说明 ==="
echo "此方案依赖Windows主机的VPN连接来转发流量"
echo "如果*********仍不通，请检查："
echo "1. Windows上的VPN是否正常工作"
echo "2. Windows防火墙是否阻止了转发"
echo "3. VPN服务器是否允许来自Windows主机的转发请求"
