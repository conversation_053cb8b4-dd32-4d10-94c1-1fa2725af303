# 基于LLVM风格
BasedOnStyle: LLVM

# 缩进设置
IndentWidth: 4
TabWidth: 4
UseTab: Never
ContinuationIndentWidth: 4

# 行宽限制
ColumnLimit: 120

# lambda表达式格式化
AllowShortLambdasOnASingleLine: None

# 函数参数格式化
BinPackArguments: false
BinPackParameters: false
AllowAllParametersOfDeclarationOnNextLine: false
AlignAfterOpenBracket: Align
AllowShortFunctionsOnASingleLine: None
AlwaysBreakAfterReturnType: None

# 对齐设置
AlignOperands: true
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignConsecutiveMacros: true
AlignTrailingComments: true

# 空格设置
SpaceAfterTemplateKeyword: true
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesInAngles: false
SpacesInParentheses: false
SpacesInSquareBrackets: false
SpaceBeforeAssignmentOperators: true

# 换行设置
BreakBeforeBraces: Custom
BraceWrapping:
  AfterClass: false
  AfterControlStatement: false
  AfterEnum: false
  AfterFunction: false
  AfterNamespace: false
  AfterStruct: false
  AfterUnion: false
  BeforeCatch: false
  BeforeElse: false
  IndentBraces: false

# 其他设置
PointerAlignment: Right
MaxEmptyLinesToKeep: 1
KeepEmptyLinesAtTheStartOfBlocks: false
AllowShortBlocksOnASingleLine: Never
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false
SortIncludes: false

# 函数调用换行设置
PenaltyBreakBeforeFirstCallParameter: 1
PenaltyReturnTypeOnItsOwnLine: 200
PenaltyBreakAssignment: 100
PenaltyExcessCharacter: 1
