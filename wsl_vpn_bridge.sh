#!/bin/bash

# WSL VPN Bridge 解决方案
# 解决WSL2无法访问Windows OpenVPN网络的问题

echo "=== WSL VPN Bridge 配置脚本 ==="

# 检查是否为root权限
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 1. 获取网络信息
echo "1. 分析网络配置..."
WIN_HOST=$(ip route | grep default | awk '{print $3}' | head -1)
VPN_INTERFACE=$(ip addr | grep "10\.8\." | awk '{print $NF}' | head -1)
VPN_IP=$(ip addr show $VPN_INTERFACE 2>/dev/null | grep "inet 10\.8\." | awk '{print $2}' | cut -d'/' -f1)

echo "Windows主机: $WIN_HOST"
echo "VPN接口: $VPN_INTERFACE"  
echo "VPN IP: $VPN_IP"

# 2. 配置iptables NAT规则
echo "2. 配置NAT规则..."
iptables -t nat -F
iptables -t nat -A POSTROUTING -o $VPN_INTERFACE -j MASQUERADE
iptables -A FORWARD -i eth0 -o $VPN_INTERFACE -j ACCEPT
iptables -A FORWARD -i $VPN_INTERFACE -o eth0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# 3. 启用IP转发
echo "3. 启用IP转发..."
echo 1 > /proc/sys/net/ipv4/ip_forward

# 4. 配置路由表
echo "4. 配置路由表..."
# 保持默认路由通过Windows主机
ip route del default 2>/dev/null || true
ip route add default via $WIN_HOST dev eth0 metric 1

# 添加VPN网络的特定路由
if [ ! -z "$VPN_IP" ]; then
    ip route add *********/16 via ******** dev $VPN_INTERFACE 2>/dev/null || true
    ip route add **********/12 via ******** dev $VPN_INTERFACE 2>/dev/null || true
    ip route add ***********/16 via ******** dev $VPN_INTERFACE 2>/dev/null || true
fi

# 5. 配置DNS
echo "5. 配置DNS..."
cat > /etc/resolv.conf << EOF
# WSL VPN DNS配置
nameserver *******
nameserver *******
nameserver *******
EOF

# 6. 测试连通性
echo "6. 测试连通性..."
echo "测试外网连接:"
ping -c 2 *******

if [ ! -z "$VPN_IP" ]; then
    echo "测试VPN网关:"
    ping -c 2 ********
fi

echo "=== 配置完成 ==="
echo "当前路由表:"
ip route show
echo ""
echo "如果仍无法访问VPN网络，请提供要访问的具体IP地址"
