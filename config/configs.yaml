network:
  udp_port: 6666

dbPath: data.db

#GMAX数据库表前缀名
modbusDataTablePreName: gmax_

# 设备信息表名
deviceInfoTableName: device_info

# 离线设备表名
offlineDeviceTableName: offline_device

# 电表数据库表名
meterDataTableName: meter_data

# 数据库配置
database:
  type: mysql
  sqlite:
    filename: data.db
  mysql:
    host: localhost
    user: testuser
    password: StrongPassword123!
    database: BlackBox
    port: 3306
    charset: utf8mb4
    max_connections: 10
    timeout: 30
    config_file: "/etc/mysql/mysql.conf.d/mysqld.cnf"

serialPort:
  RS485-1:
    Path: /dev/ttyRS485-1
    slave_id: 1
    baudRate: 9600
    parity: "N"
    dataBit: 8
    stopBit: 1

    RS485-2:
      Path: /dev/ttyRS485-2
      slave_id: 1
      baudRate: 9600
      parity: "N"
      dataBit: 8
      stopBit: 1

    RS485-3:
      Path: /dev/ttyRS485-3
      slave_id: 1
      baudRate: 9600
      parity: "N"
      dataBit: 8
      stopBit: 1

    RS485-4:
      Path: /dev/ttyRS485-4
      slave_id: 1
      baudRate: 9600
      parity: "N"
      dataBit: 8
      stopBit: 1

    RS485-5:
      Path: /dev/ttyRS485-5
      slave_id: 1
      baudRate: 9600
      parity: "N"
      dataBit: 8
      stopBit: 1

    RS485-6:
      Path: /dev/ttyRS485-6
      slave_id: 1
      baudRate: 9600
      parity: "N"
      dataBit: 8
      stopBit: 1

    RS485-7:
      Path: /dev/ttyRS485-7
      slave_id: 1
      baudRate: 9600
      parity: "N"
      dataBit: 8
      stopBit: 1

    RS485-8:
      Path: /dev/ttyRS485-8
      slave_id: 1
      baudRate: 9600
      parity: "N"
      dataBit: 8
      stopBit: 1

#当前电表协议号
currMetertProtId: 1

# 设备连接配置
deviceConnection:
  # 连接模式: "fixed" (固定设备), "udp" (动态发现), "hybrid" (两者都支持)
  mode: "udp"

  # 固定设备配置
  fixedDevices:
    - ip: "*************"
      dbAddr: 1
      port: 502
    - ip: "*************"
      dbAddr: 2
      port: 502
    - ip: "*************"
      dbAddr: 3
      port: 502
    - ip: "*************"
      dbAddr: 4
      port: 502

  # UDP设置 (当模式为"udp"或"hybrid"时使用)
  udpSettings:
    enabled: false # 是否启用UDP监听

  # 固定设备重连间隔（秒）
  reconnectInterval: 10

X5SysRegisterData:
  输入寄存器:
    系统数据:
      - name: 系统状态
        address: 0x0010
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 系统功率
        address: 0x0011
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 系统允许充电功率
        address: 0x0014
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 系统允许放电功率
        address: 0x0015
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 系统告警1
        address: 0x0016
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: BMS-CAN通信
          bit1: BMS-485通信
          bit2: 液冷通信
          bit3: BMS
          bit4: 液冷
          bit5: 液冷开机
          bit6: PCS
          bit7: PCS故障

      - name: 系统告警2
        address: 0x0017
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: EntherNet
          bit1: Flash
          bit2: 本地检修
          bit3: 门禁
          bit4: 浪涌

      - name: 系统故障1
        address: 0x0018
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: EMS通信
          bit1: PCS通信
          bit2: BMS通信
          bit3: BMS
          bit4: PCS
          bit15: 急停

      - name: 系统故障2
        address: 0x0019
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: 水浸
          bit1: 消防
          bit2: 温感
          bit3: 可燃气体
          bit4: 烟感

    液冷数据:
      - name: 设备类型
        address: 0x0020
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 设备状态
        address: 0x0021
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: 停止
          bit1: 制冷
          bit2: 制热
          bit3: 循环
          bit8: 故障代码1
          bit9: 故障代码2
          bit10: 故障代码3
          bit11: 故障代码4
          bit12: 故障代码5
          bit13: 故障代码6
          bit14: 故障代码7
          bit15: 故障代码8

      - name: 环境温度
        address: 0x0022
        length: 1
        type: Int16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: 出水口温度
        address: 0x0023
        length: 1
        type: Int16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: 进水口温度
        address: 0x0024
        length: 1
        type: Int16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: 出水口压强
        address: 0x0025
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: 进水口压强
        address: 0x0026
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - 条件解析:
          condition:
            register: 设备类型
            registerAddr: 0x0020
            startAddr: 0x0028
            endAddr: 0x002F
          parse:
            0: # 未知

            1: # 同飞液冷机故障字定义
              - name: 故障字1
                address: 0x0028
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 供水泵故障
                  bit1: 泵室整保护
                  bit2: 泵结转保护
                  bit3: 泵过流故障
                  bit4: 泵欠压故障
                  bit5: 水泵故障锁定
                  bit6: 泵过压故障
                  bit7: 泵缺相/电流差作偏移/失步/预充故障
                  bit8: 高压压力传感器故障
                  bit9: 泵驱动过热保护故障
                  bit10: 供水压力传感器故障
                  bit11: 供水压力过高
                  bit12: 电压过高
                  bit13: 电压过低

              - name: 故障字2
                address: 0x0029
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 系统低压
                  bit1: 电加热保护
                  bit2: 风机过载
                  bit3: 系统高压故障
                  bit4: 蒸发进口保头故障
                  bit5: 蒸发出口保头故障
                  bit6: 系统高压报警锁定
                  bit7: 排气压力传感器故障
                  bit8: 压缩机变频器通讯故障告警

              - name: 故障字3
                address: 0x002A
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 液温探头故障
                  bit1: 回液探头故障
                  bit2: 环境探头故障
                  bit3: 液温过高
                  bit4: 回水压力传感器故障
                  bit5: 屏蔽与主板通讯超时
                  bit6: 液温过低
                  bit7: 供水压力过高预警
                  bit8: 拆电报警
                  bit9: 从板通讯故障

              - name: 故障字4
                address: 0x002B
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 压机启动瞬间过流
                  bit1: 压机加速运行过流
                  bit2: 压机减速运行过流
                  bit3: 压机恒速运行过流
                  bit4: 压机加速运行过压
                  bit5: 压机减速运行过压
                  bit6: 压机恒速运行过压
                  bit7: 压机停机间过压
                  bit8: 压机运行中欠
                  bit9: 压机输出缺相
                  bit10: 压机启动中转子动过大
                  bit11: 压机运行中转子动过大
                  bit12: PFC过流
                  bit13: PFC峰值电流过大
                  bit14: PFC有效值电流过大

              - name: 故障字5
                address: 0x002C
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 压机D轴电流过大
                  bit1: 压机Q轴电流过大
                  bit2: 压机参数序品失数
                  bit3: 压机通讯异常
                  bit4: 压机电流检测故障
                  bit5: 压机启动中电机堵转
                  bit6: 压机运行中电机堵转
                  bit7: 压机失速故障
                  bit8: 压机中断溢出1
                  bit9: 压机中断溢出2
                  bit10: 压机启动中转子动过大
                  bit11: 压机运行中转子动过大
                  bit12: PFC过流
                  bit13: PFC峰值电流过大
                  bit14: PFC有效值电流过大

              - name: 故障字6
                address: 0x002D
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 风机启动瞬间过流
                  bit1: 风机加速运行过流
                  bit2: 风机减速运行过流
                  bit3: 风机恒速运行过流
                  bit4: 风机加速运行过压
                  bit5: 风机减速运行过压
                  bit6: 风机恒速运行过压
                  bit7: 风机停机间过压
                  bit8: 风机运行中欠
                  bit9: 风机输出缺相
                  bit10: 风机输出缺相
                  bit11: 风机功率器件保护
                  bit12: 风机过热
                  bit13: 变频器过载
                  bit14: 风机过载
                  bit15: 外部故障

              - name: 故障字7
                address: 0x002E
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 电机负载过重
                  bit1: 变频器欠载
                  bit2: 风扇故障
                  bit3: 用户使用期限禁止时间已到
                  bit4: 参数存储失败
                  bit5: 通讯异常
                  bit6: 电流检测故障
                  bit7: 自整定不良
                  bit8: 模拟输入掉线
                  bit9: PG断线
                  bit10: 热敏电阻开路
                  bit11: 异常停机故障

            2: # 英维克液冷机故障字定义
              - name: 故障字1
                address: 0x0028
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 出水压力传感器故障
                  bit1: 交流过压告警
                  bit2: 交流欠压告警
                  bit3: 水箱缺水告警
                  bit4: 水泵故障
                  bit5: 水泵故障锁定
                  bit7: 出水压力过高告警
                  bit8: 高压压力传感器故障
                  bit9: 环境温感故障
                  bit10: 回水温感故障
                  bit11: 排气温感故障
                  bit12: 冷凝温感故障
                  bit13: 异常掉电告警
                  bit14: 出水低温告警
                  bit15: 出水高温告警

              - name: 故障字2
                address: 0x0029
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 出水压力传感器故障
                  bit1: 回水压力传感器故障
                  bit2: 低压传感器故障
                  bit3: 出水温感故障
                  bit4: 系统低压告警
                  bit5: 系统高压开关告警
                  bit6: 排气温度过高告警
                  bit7: CAN通讯故障告警
                  bit8: 吸气温感故障
                  bit9: 制冷系统异常
                  bit10: 低吸气过热度告警
                  bit11: 压缩机变频器过压告警
                  bit12: 压缩机变频器欠压告警
                  bit13: 压缩机变频器过流告警
                  bit14: 压缩机变频器过温告警
                  bit15: 压缩机变频器通讯故障告警

              - name: 故障字3
                address: 0x002A
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 压缩机变频器缺相告警
                  bit1: 压缩机变频器其他故障告警
                  bit2: 水泵压差低告警
                  bit3: 排气温度过高锁定
                  bit4: 系统高压锁定
                  bit5: 系统低压锁定
                  bit6: 压缩机变频器过流锁定
                  bit7: 压缩机变频器过压锁定
                  bit8: 压缩机变频器欠压锁定
                  bit9: 压缩机变频器过温锁定
                  bit10: 压缩机变频器缺相锁定
                  bit11: 压缩机变频器其他故障锁定

    除湿机数据:
      - name: 设备类型
        address: 0x0030
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 环境温度
        address: 0x0031
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 环境湿度
        address: 0x0032
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 制冷器温度
        address: 0x0033
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 设置湿度
        address: 0x0034
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - name: 设置温度
        address: 0x0035
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - 条件解析:
          condition:
            register: 设备类型
            registerAddr: 0x0030
            startAddr: 0x0036
            endAddr: 0x0036
          parse:
            0: # 未知

            1: # ADSBD-500ML
              - name: 状态字
                address: 0x0036
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 风机状态
                  bit1: 制冷块状态
                  bit2: 手-自动状态
                  bit3: 风机故障
                  bit4: 制冷块故障
                  bit5: 传感器故障
                  bit6: 泵过压故障
                  bit8: 湿度报警
                  bit9: 温度报警

    PCS数据:
      - name: PCS类型
        address: 0x0040
        length: 1
        type: UInt16
        conversionFactor: 1.0
        readOnly: true

      - 条件解析:
          condition:
            register: PCS类型
            registerAddr: 0x0040
            startAddr: 0x0041
            endAddr: 0x0077
          parse:
            0: # X5_PCS-100
              - name: 母线电压
                address: 0x0041
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 母线电流
                address: 0x0042
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 直流功率
                address: 0x0043
                length: 1
                type: Int16
                conversionFactor: 1.0
                readOnly: true

              - name: 电网A相电压
                address: 0x0044
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网B相电压
                address: 0x0045
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网C相电压
                address: 0x0046
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网A相电流
                address: 0x0047
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网B相电流
                address: 0x0048
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网C相电流
                address: 0x0049
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 功率因数
                address: 0x004A
                length: 1
                type: Int16
                conversionFactor: 0.001
                readOnly: true

              - name: 电网频率
                address: 0x004B
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 总-交流有功功率
                address: 0x004C
                length: 1
                type: Int16
                conversionFactor: 1.0
                readOnly: true

              - name: 总-电网无功功率
                address: 0x004D
                length: 1
                type: Int16
                conversionFactor: 1.0
                readOnly: true

              - name: 电网视在功率
                address: 0x004E
                length: 1
                type: Int16
                conversionFactor: 1.0
                readOnly: true

              - name: 电网平均功率
                address: 0x004F
                length: 1
                type: Int16
                conversionFactor: 1.0
                readOnly: true

              - name: 正对地绝缘阻抗
                address: 0x0050
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 负对地绝缘阻抗
                address: 0x0051
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 环境温度
                address: 0x0052
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: IGBT-A温度
                address: 0x0054
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: IGBT-B温度
                address: 0x0055
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: IGBT-C温度
                address: 0x0056
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: A相直流分量
                address: 0x005C
                length: 1
                type: Int16
                conversionFactor: 0.001
                readOnly: true

              - name: B相直流分量
                address: 0x005D
                length: 1
                type: Int16
                conversionFactor: 0.001
                readOnly: true

              - name: C相直流分量
                address: 0x005E
                length: 1
                type: Int16
                conversionFactor: 0.001
                readOnly: true

              - name: 逆变A相电压
                address: 0x005F
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变B相电压
                address: 0x0060
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变C相电压
                address: 0x0061
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 正母线电压
                address: 0x0062
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 负母线电压
                address: 0x0063
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 直流输入电压
                address: 0x0064
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 直流输入电流
                address: 0x0065
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: PCS运行状态
                address: 0x006E
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 停机
                  bit1: 自检
                  bit2: 待机
                  bit4: 启动
                  bit6: 故障
                  bit14: 运行

              - name: PCS运行模式
                address: 0x006F
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 独立逆变
                  bit1: 并网模式
                  bit4: 整流模式
                  bit5: 离网模式
                  bit6: 测试模式

              - name: PCS告警
                address: 0x0070
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: AC防雷故障
                  bit1: 风扇检测故障

              - name: PCS故障字1
                address: 0x0071
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 继电器检测故障
                  bit1: 逆变侧继电器检测故障
                  bit2: 电网侧继电器检测故障
                  bit3: N相继电器检测故障
                  bit4: 逆变侧N相继电器检测故障
                  bit5: 相对地短路故障
                  bit6: 电网侧N相继电器检测故障
                  bit7: 机型-系列号-版本信息参数异常
                  bit8: 硬件交流过流-TZ1
                  bit9: 硬件直流过流-TZ2
                  bit10: 硬件母线过压-TZ3
                  bit11: 接地故障
                  bit12: 软启继电器故障

              - name: PCS故障字2
                address: 0x0072
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 逆变相序故障
                  bit1: 逆变幅值锁定故障
                  bit2: 绝缘阻抗故障
                  bit3: AC电网电压采样故障
                  bit4: DC电压采样故障
                  bit5: AC电流传感器故障
                  bit6: DC电流采样故障
                  bit7: 电池反接
                  bit8: 电网相位缺失故障
                  bit9: 电网相序故障
                  bit10: 电池过压
                  bit11: 电池欠压
                  bit12: CAN总线通信故障
                  bit13: 内部通信故障
                  bit14: CPU1与CPLD通信故障
                  bit15: 主机授权关机

              - name: PCS故障字3
                address: 0x0073
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: 逆变输出三相电流不平衡
                  bit1: 系统存在地址冲突
                  bit2: 模块地址或机型设置异常
                  bit3: 电网电压10min过压
                  bit4: 直流分量过高
                  bit5: 电网有效值高
                  bit6: 电网有效值低
                  bit7: 电网频率高
                  bit8: 电网频率低
                  bit9: DCBus过高
                  bit10: DCBus过低
                  bit11: 母线电压软启故障
                  bit12: 电池电压采样故障
                  bit13: 母线电压不平衡
                  bit14: DC正极接地
                  bit15: DC负极接地

              - name: PCS故障字4
                address: 0x0074
                length: 1
                type: Bit
                readOnly: true
                bits:
                  bit0: BATT1电流传感器故障
                  bit1: 直流软启故障
                  bit2: LCD开关机
                  bit3: PCC点快关机故障
                  bit4: 从机检测主机停机
                  bit5: 主机检测并机系统预同步失败故障
                  bit6: 环境温度过高
                  bit7: IGBTA温度过高
                  bit8: IGBTB温度过高
                  bit9: IGBTC温度过高
                  bit10: 环境温度过低
                  bit11: IGBT温度不平衡

            1: # Metis-100
              - name: 电网侧A相采样电压
                address: 0x0041
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变器侧A相采样电流
                address: 0x0042
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网侧A相采样电流
                address: 0x0043
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变器侧A相功率
                address: 0x0044
                length: 2
                type: Int32
                conversionFactor: 1.0
                readOnly: true

              - name: 电网侧B相采样电压
                address: 0x0046
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变器侧B相采样电流
                address: 0x0047
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网侧B相采样电流
                address: 0x0048
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变器侧B相功率
                address: 0x0049
                length: 2
                type: Int32
                conversionFactor: 1.0
                readOnly: true

              - name: 电网侧C相采样电压
                address: 0x004B
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变器侧C相采样电流
                address: 0x004C
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网侧C相采样电流
                address: 0x004D
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 逆变器侧C相有功功率
                address: 0x004E
                length: 2
                type: Int32
                conversionFactor: 1.0
                readOnly: true

              - name: 平衡桥电流
                address: 0x0050
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: AB线电压
                address: 0x0051
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: BC线电压
                address: 0x0052
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: CA线电压
                address: 0x0053
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 频率
                address: 0x0054
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 功率因数
                address: 0x0055
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 母线电压
                address: 0x0056
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 正母线电压
                address: 0x0057
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 负母线电压
                address: 0x0058
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 总-交流有功功率
                address: 0x0059
                length: 2
                type: Int32
                conversionFactor: 1.0
                readOnly: true

              - name: 总-电网无功功率
                address: 0x005B
                length: 2
                type: Int32
                conversionFactor: 1.0
                readOnly: true

              - name: 逆变器侧总视在功率
                address: 0x005D
                length: 2
                type: Int32
                conversionFactor: 1.0
                readOnly: true

              - name: 电池侧电压
                address: 0x005F
                length: 1
                type: Int16
                conversionFactor: 1.0
                readOnly: true

              - name: 电池侧电流
                address: 0x0060
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电池侧功率
                address: 0x0061
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 模块1温度
                address: 0x0062
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 模块2温度
                address: 0x0063
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 模块3温度
                address: 0x0064
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 散热器温度
                address: 0x0065
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: BOOST2温度
                address: 0x0066
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 环境温度
                address: 0x0067
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 滤波器温度
                address: 0x0068
                length: 1
                type: Int16
                conversionFactor: 0.01
                readOnly: true

              - name: 直流主正继电器状态
                address: 0x0069
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 直流主负继电器状态
                address: 0x006A
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 交流继电器状态
                address: 0x006B
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: 电网掉电状态
                address: 0x006C
                length: 1
                type: Int16
                conversionFactor: 0.1
                readOnly: true

              - name: PCS运行状态
                address: 0x006E
                length: 1
                type: Int16
                conversionFactor: 1.0
                readOnly: true

              - name: PCS故障字1
                address: 0x0070
                length: 2
                type: Bit
                readOnly: true
                bits:
                  bit0: 硬件-逆变侧A相过流
                  bit1: 硬件-逆变侧B相过流
                  bit2: 硬件-逆变侧C相过流
                  bit3: 硬件-母线过压
                  bit4: 硬件-正母线过压
                  bit5: 硬件-负母线过压
                  bit8: 硬件-平衡桥过流
                  bit9: 硬件-辅助电源过压
                  bit10: 软件-逆变侧A相过流
                  bit11: 软件-逆变侧B相过流
                  bit12: 软件-逆变侧C相过流
                  bit13: 软件-母线过压
                  bit14: 软件-母线欠压
                  bit15: 软件-正母线过压
                  bit16: 软件-负母线过压
                  bit17: 软件-母线电压不均
                  bit18: 软件-电网电压欠压
                  bit19: 软件-孤岛运行
                  bit28: 软件-电压穿越故障-并网
                  bit29: 软件-平衡桥过流

              - name: PCS故障字2
                address: 0x0072
                length: 2
                type: Bit
                readOnly: true
                bits:
                  bit0: L1电压1级故障
                  bit1: L2电压1级故障
                  bit2: L3电压1级故障
                  bit3: L1电压2级故障
                  bit4: L2电压2级故障
                  bit5: L3电压2级故障
                  bit6: L1电压3级故障
                  bit7: L2电压3级故障
                  bit8: L3电压3级故障
                  bit9: L1瞬态电压过高故障
                  bit10: L2瞬态电压过高故障
                  bit11: L3瞬态电压过高故障
                  bit12: L1短路故障
                  bit13: L2短路故障
                  bit14: L3短路故障
                  bit15: L12线电压1级故障
                  bit16: L23线电压1级故障
                  bit17: L31线电压1级故障
                  bit18: L12线电压2级故障
                  bit19: L23线电压2级故障
                  bit20: L31线电压2级故障
                  bit21: L12线电压3级故障
                  bit22: L23线电压3级故障
                  bit23: L31线电压3级故障
                  bit24: L12瞬态线电压过高故障
                  bit25: L23瞬态线电压过高故障
                  bit26: L31瞬态线电压过高故障
                  bit27: L12线电压短路故障
                  bit28: L23线电压短路故障
                  bit29: L31线电压短路故障
                  bit30: 频率1级故障
                  bit31: 频率2级故障

              - name: PCS故障字3
                address: 0x0074
                length: 2
                type: Bit
                readOnly: true
                bits:
                  bit0: 频率重连故障
                  bit1: L1相重连故障
                  bit2: L2相重连故障
                  bit3: L3相重连故障
                  bit4: L12线电压重连故障
                  bit5: L23线电压重连故障
                  bit6: L31线电压重连故障
                  bit7: 温度过低故障
                  bit8: 温度过高故障
                  bit9: ISO故障
                  bit10: RCD故障
                  bit11: ResCurt300Ma
                  bit12: ResCurtStep30Ma
                  bit13: ResCurtStep60Ma
                  bit14: ResCurtStep150Ma
                  bit15: ApsBad
                  bit16: Cpu1Config
                  bit17: NlineCheck
                  bit18: Offset
                  bit19: ArcGenBad
                  bit20: ArcMistrig
                  bit21: AfciFault
                  bit22: 继电器A1C2故障
                  bit23: 继电器B1A2故障
                  bit24: 继电器C1B2故障
                  bit25: 继电器D1D2故障
                  bit26: 继电器浮空故障
                  bit27: 继电器断开故障

              - name: PCS故障字4
                address: 0x0076
                length: 2
                type: Bit
                readOnly: true
                bits:
                  bit8: E2PROM读写错误
                  bit9: EXTFLASH读写错误
                  bit10: INTFLASH读写错误
                  bit11: DCP通讯错误
                  bit12: FSP通讯错误
                  bit18: CAN通讯错误
                  bit19: 风扇异常
                  bit20: 继电器异常
                  bit21: ATE参数校准异常
                  bit29: E2prom参数存储异常

  保持寄存器:
    系统数据:
      - name: 系统ID
        address: 0x0200
        length: 2
        type: UInt32
        readOnly: true

      - name: 系统型号
        address: 0x0202
        length: 1
        type: UInt16
        readOnly: true

      - name: 系统版本
        address: 0x0203
        length: 1
        type: UInt16
        readOnly: true

      - name: CPLD版本
        address: 0x0204
        length: 1
        type: UInt16
        readOnly: true

      - name: DSP1版本
        address: 0x0205
        length: 1
        type: UInt16
        readOnly: true

      - name: 系统序列号
        address: 0x0210
        length: 16
        type: String
        readOnly: true

      - name: PCS序列号
        address: 0x0220
        length: 16
        type: String
        readOnly: true

      - name: PCS类型
        address: 0x0230
        length: 16
        type: String
        readOnly: true

      - name: 系统运行模式
        address: 0x0281
        length: 1
        type: UInt16
        readOnly: false

      - name: 开关机
        address: 0x0282
        length: 1
        type: UInt16
        readOnly: false

      - name: 有功功率
        address: 0x0283
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 1.0

      - name: 无功功率
        address: 0x0284
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 1.0

      - name: 系统时间
        address: 0x0288
        length: 2
        type: UInt32
        readOnly: false

      - name: 通信超时时间
        address: 0x028A
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 1.0

      - name: 设备地址
        address: 0x028B
        length: 1
        type: UInt16
        readOnly: false

      - name: 系统保护后进入停机时间
        address: 0x028C
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 1.0

      - name: 除湿机通信地址
        address: 0x028E
        length: 1
        type: UInt16
        readOnly: false

      - name: 除湿机湿度控制差
        address: 0x028F
        length: 1
        type: UInt16
        readOnly: false

      - name: 电池最大电压
        address: 0x0290
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 0.1

      - name: 电池最小电压
        address: 0x0291
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 0.1

      - name: 电池最大SOC
        address: 0x0292
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 0.1

      - name: 电池最小SOC
        address: 0x0293
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 0.1

      - name: 电池最大充电功率
        address: 0x0294
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 1.0

      - name: 电池最大放电功率
        address: 0x0295
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 1.0

      - name: 液冷制热最低温度
        address: 0x0296
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷制热平均温度
        address: 0x0297
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷制热停止温度
        address: 0x0298
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷制冷最高温度
        address: 0x0299
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷制冷平均温度
        address: 0x029A
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷制冷停止温度
        address: 0x029B
        length: 1
        type: Int16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷循环温差值
        address: 0x029C
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷制热出水口温度
        address: 0x029D
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 0.1

      - name: 液冷制冷出水口温度
        address: 0x029E
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 0.1

      - name: 系统停机制冷停止时间
        address: 0x029F
        length: 1
        type: UInt16
        readOnly: false
        conversionFactor: 2

      # - name: 时间点1
      #   address: 0x02A0
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率1
      #   address: 0x02A1
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点2
      #   address: 0x02A2
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率2
      #   address: 0x02A3
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点3
      #   address: 0x02A4
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率3
      #   address: 0x02A5
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点4
      #   address: 0x02A6
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率4
      #   address: 0x02A7
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点5
      #   address: 0x02A8
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率5
      #   address: 0x02A9
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点6
      #   address: 0x02AA
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率6
      #   address: 0x02AB
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点7
      #   address: 0x02AC
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率7
      #   address: 0x02AD
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点8
      #   address: 0x02AE
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率8
      #   address: 0x02AF
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点9
      #   address: 0x02B0
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率9
      #   address: 0x02B1
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点10
      #   address: 0x02B2
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率10
      #   address: 0x02B3
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点11
      #   address: 0x02B4
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率11
      #   address: 0x02B5
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点12
      #   address: 0x02B6
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率12
      #   address: 0x02B7
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点13
      #   address: 0x02B8
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率13
      #   address: 0x02B9
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点14
      #   address: 0x02BA
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率14
      #   address: 0x02BB
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点15
      #   address: 0x02BC
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率15
      #   address: 0x02BD
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点16
      #   address: 0x02BE
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率16
      #   address: 0x02BF
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点17
      #   address: 0x02C0
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率17
      #   address: 0x02C1
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点18
      #   address: 0x02C2
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率18
      #   address: 0x02C3
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点19
      #   address: 0x02C4
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率19
      #   address: 0x02C5
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点20
      #   address: 0x02C6
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率20
      #   address: 0x02C7
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点21
      #   address: 0x02C8
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率21
      #   address: 0x02C9
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点22
      #   address: 0x02CA
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率22
      #   address: 0x02CB
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点23
      #   address: 0x02CC
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率23
      #   address: 0x02CD
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 时间点24
      #   address: 0x02CE
      #   length: 1
      #   type: UInt16
      #   readOnly: false
      #   conversionFactor: 1.0

      # - name: 输出功率24
      #   address: 0x02CF
      #   length: 1
      #   type: Int16
      #   readOnly: false
      #   conversionFactor: 1.0

    BmsIdentification:
      - name: Manufacturer-Name1
        address: 1
        length: 1
        type: UInt16
        readOnly: true

      - name: Manufacturer-Name2
        address: 2
        length: 1
        type: UInt16
        readOnly: true

      - name: Manufacturer-Name3
        address: 3
        length: 1
        type: UInt16
        readOnly: true

      - name: Manufacturer-Name4
        address: 4
        length: 1
        type: UInt16
        readOnly: true

      - name: HV-BOX-SN-Information
        address: 5
        length: 10
        type: MultiUInt16
        readOnly: true

      - name: PACK1-SN-Information
        address: 15
        length: 10
        type: MultiUInt16
        readOnly: true

      - name: PACK2-SN-Information
        address: 25
        length: 10
        type: MultiUInt16
        readOnly: true

      - name: PACK3-SN-Information
        address: 35
        length: 10
        type: MultiUInt16
        readOnly: true

      - name: PACK4-SN-Information
        address: 45
        length: 10
        type: MultiUInt16
        readOnly: true

    BmsBcuData1:
      - name: BCU-BMS-Software-Version
        address: 10000
        length: 1
        type: UInt16
        readOnly: true

      - name: BCU-BMS-Hardware-Version
        address: 10001
        length: 1
        type: UInt16
        readOnly: true

      - name: BCU-System-Status
        address: 10002
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: Battery-Idle
          bit1: Battery-Charge
          bit2: Battery-Discharge
          bit3: Battery-Error
          bit4: Battery-Startup
          bit9: Battery-Normal
          bit10: Battery-Charge-Prohibited
          bit11: Battery-Discharge-Prohibited
          bit12: Battery-Charge-And-Discharge-Prohibited

      - name: SOC
        address: 10003
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: SOH
        address: 10004
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: BCU-Internal-Sum-Voltage
        address: 10005
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: BCU-Current
        address: 10006
        length: 1
        type: Int16
        conversionFactor: 0.1
        readOnly: true

      - name: Max-Cell-Voltage
        address: 10007
        length: 1
        type: UInt16
        conversionFactor: 0.001
        readOnly: true

      - name: Max-Cell-Voltage-Position
        address: 10008
        length: 1
        type: UInt16
        readOnly: true

      - name: Min-Cell-Voltage
        address: 10009
        length: 1
        type: UInt16
        conversionFactor: 0.001
        readOnly: true

      - name: Min-Cell-Voltage-Position
        address: 10010
        length: 1
        type: UInt16
        readOnly: true

      - name: MaxTemperature
        address: 10011
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: MaxTemperature-Position
        address: 10012
        length: 1
        type: UInt16
        readOnly: true

      - name: MinTemperature
        address: 10013
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: MinTemperature-Position
        address: 10014
        length: 1
        type: UInt16
        readOnly: true

      - name: BCU-Dc-Charge-Current-Limit
        address: 10015
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: BCU-Dc-DisCharge-Current-Limit
        address: 10016
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Max-CellVoltage-Limit
        address: 10017
        length: 1
        type: UInt16
        conversionFactor: 0.001
        readOnly: true

      - name: Min-CellVoltage-Limit
        address: 10018
        length: 1
        type: UInt16
        conversionFactor: 0.001
        readOnly: true

      - name: Max-Cut-Charge-Voltage
        address: 10019
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Max-Cut-DisCharge-Voltage
        address: 10020
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: User-SOC
        address: 10021
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Number-Of-Cycles
        address: 10022
        length: 1
        type: UInt16
        conversionFactor: 1
        readOnly: true

      - name: Qmax
        address: 10023
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Useable-Capacity
        address: 10024
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Remaining-Capacity
        address: 10025
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: BMU-Number
        address: 10026
        length: 1
        type: UInt16
        conversionFactor: 1
        readOnly: true

      - name: Cell-Number
        address: 10027
        length: 1
        type: UInt16
        readOnly: true

      - name: Temperture-Number
        address: 10028
        length: 1
        type: UInt16
        readOnly: true

      - name: Cell-Capcity
        address: 10029
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-Charge-Capcity
        address: 10030
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-Charge-Capcity
        address: 10031
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-DisCharge-Capcity
        address: 10032
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-DisCharge-Capcity
        address: 10033
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Sigle-Charge-Capcity
        address: 10034
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Sigle-DisCharge-Capcity
        address: 10035
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: BMS-Work-Mode
        address: 10036
        length: 1
        type: UInt16
        readOnly: true

      - name: External-Sum-Voltage
        address: 10037
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Hardware-State0
        address: 10038
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: 主正继电器状态
          bit1: 主负继电器状态
          bit2: 预充继电器状态
          bit7: 主接触器状态

      - name: Hardware-State1
        address: 10039
        length: 1
        type: UInt16
        readOnly: true

      - name: RUN-Mode
        address: 10040
        length: 1
        type: UInt16
        readOnly: true

      - name: PreChg-Res-Temperature
        address: 10041
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: Shunt-Temperature
        address: 10042
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: IVU-Power-Temperature
        address: 10043
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: 3.3V-Power-Temperature
        address: 10044
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: BCU-PCB-Temperature
        address: 10045
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-Charge-Energy-Hi
        address: 10047
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-Charge-Energy-Low
        address: 10048
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-DisCharge-Energy-Hi
        address: 10049
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Acc-DisCharge-Energy-Low
        address: 10050
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Sigle-Charge-Energy
        address: 10051
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Sigle-DisCharge-Energy
        address: 10052
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Fuse-Voltage
        address: 10053
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Protect-Alarm-Code0~1
        address: 10054
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: IntSumVHigh
          bit2-3: IntSumVLow
          bit4-5: CellVHigh
          bit6-7: CellVLow
          bit8-9: DeltaVSingle
          bit12-13: DeltaTSingle

      - name: Protect-Alarm-Code2~3
        address: 10055
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: ChgTemperHigh
          bit2-3: ChgTemperLow
          bit4-5: DchTemperHigh
          bit6-7: DchTemperLow
          bit10-11: SocLow
          bit14-15: SohLow

      - name: Protect-Alarm-Code4~5
        address: 10056
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: DchPowerHigh
          bit2-3: ChgPowerHigh
          bit8-9: ChgCurrHigh
          bit10-11: DchCurrHigh

      - name: Protect-Alarm-Code6~7
        address: 10057
        length: 1
        type: Bit
        readOnly: true
        bits:

      - name: System-Fault-Code0~1
        address: 10058
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit4-5: ChgOrPosNotOpen
          bit6-7: ChgOrPosNotClose
          bit8-9: DchOrNegNotPpen
          bit10-11: DchOrNegNotClose

      - name: System-Fault-Code2~3
        address: 10059
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: PreChg
          bit6-7: ShortCircuit
          bit8-9: DeltVinvalid
          bit10-11: CellVLowInvalid
          bit12-13: SupplyVLow
          bit14-15: SupplyVHigh

      - name: System-Fault-Code4~5
        address: 10060
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: PreTempHigh
          bit2-3: MosTempHigh
          bit4-5: BalTempHigh
          bit6-7: SpecDchCurrHigh
          bit8-9: AfeTempLow
          bit10-11: AfeTempHigh
          bit12-13: AfeVoltLow
          bit14-15: AfeVoltHigh

      - name: System-Fault-Code6~7
        address: 10061
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit8-9: ChargerOV
          bit14-15: SbmsuseFault

      - name: Hardware-Fault-Code0~1
        address: 10062
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: DetectlsoVolt
          bit2-3: DetctCurr
          bit4-5: DetctTemper
          bit8-9: DetctFuse
          bit14-15: DetctintVolt

      - name: Hardware-Fault-Code2~3
        address: 10063
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: DetctConnectTemper
          bit2-3: ConnectTemperHigh
          bit8-9: BmuAddrFault
          bit12-13: IVUComm
          bit14-15: BmuComm

      - name: Hardware-Fault-Code4~5
        address: 10064
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: EMSComm
          bit4-5: ExtCanComm
          bit8-9: CoolFault
          bit10-11: CoolComm
          bit12-13: ExtAdcCom
          bit14-15: HallCom

      - name: Hardware-Fault-Code6~7
        address: 10065
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: ActorDriver
          bit4-5: RTCFault
          bit6-7: AfeComm
          bit8-9: AfeWireOpen
          bit10-11: MSDOpen
          bit12-13: FireTriger

      - name: Other-Fault-Code0~1
        address: 10066
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: PreNotOpen
          bit2-3: PreNotClose
          bit4-5: PreDriverFault
          bit6-7: NegNotOpen
          bit8-9: NegNotClose
          bit10-11: NegDriverFault
          bit12-13: PosNotOpen
          bit14-15: PosNotClose

      - name: Other-Fault-Code2~3
        address: 10067
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-1: PosDriverFault
          bit2-3: BreakNotOpen
          bit4-5: BreakNotClose
          bit8-9: ActorNotClose
          bit10-11: ActorNotOpen

      - name: Other-Fault-Code4~5
        address: 10068
        length: 1
        type: Bit
        readOnly: true
        bits:

      - name: Other-Fault-Code6~7
        address: 10069
        length: 1
        type: Bit
        readOnly: true
        bits:

      - name: BPlus-Connector-Temperture
        address: 10070
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: BMinus-Connector-Temperture
        address: 10071
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: FusePlus-Connector-Temperture
        address: 10072
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: FuseMinus-Connector-Temperture
        address: 10073
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: RelayPlus-Connector-Temperture
        address: 10074
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: RelayMinus-Connector-Temperture
        address: 10075
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Insulation-resistance
        address: 10077
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: MAX-Connector-Temperture
        address: 10078
        length: 1
        type: UInt16
        conversionFactor: 0.1
        readOnly: true

      - name: Liquid-Cooling-Unit-Fault-Information
        address: 10079
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-7: FaultCode
          bit8-15: FaultLevel

      - name: BMS-Status-Request
        address: 10080
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0: ForceChargeRequest

      - name: liquid-Cooling-Unit-Pressure
        address: 10081
        length: 1
        type: Bit
        conversionFactor: 0.1
        readOnly: true
        bits:
          bit0-7: Water-Outle-tPressure
          bit8-15: Water-Inlet-Pressure

      - name: History-Fault-Number
        address: 10082
        length: 1
        type: UInt16
        readOnly: true

      - name: Run-Event-Number
        address: 10083
        length: 1
        type: UInt16
        readOnly: true

      - name: Run-Information-Number
        address: 10084
        length: 1
        type: UInt16
        readOnly: true

      - name: liquid-Cooling-Control-Mode
        address: 10085
        length: 1
        readOnly: true
        type: UInt16

      - name: liquid-Cooling-Unit-Out-Water-Temperture
        address: 10086
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: liquid-Cooling-unit-In-water-Temperture
        address: 10087
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: MIN-Connector-Temperture
        address: 10088
        length: 1
        type: UInt16
        conversionFactor: 0.1
        offset: 400
        readOnly: true

      - name: Connector-Temperture-Postion
        address: 10089
        length: 1
        type: Bit
        readOnly: true
        bits:
          bit0-7: MlN-Connector-Temperture-Postion
          bit8-15: MAX-Connector-Temperture-Postion

    BmsBmuData:
      多模块扩展:
        - name: BmsBmuData1
          offset: 0
        - name: BmsBmuData2
          offset: 200
        - name: BmsBmuData3
          offset: 400
        - name: BmsBmuData4
          offset: 600

      模板:
        - name: BMU-Cell-Number
          address: 10101
          length: 1
          type: UInt16
          readOnly: true

        - name: BMU-Temperature-Number
          address: 10102
          length: 1
          type: UInt16
          readOnly: true

        - name: Max-Cell-Voltage
          address: 10103
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Min-Cell-Voltage
          address: 10104
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Max-Cell-Voltage-Position
          address: 10105
          length: 1
          type: UInt16
          readOnly: true

        - name: Min-Cell-Voltage-Position
          address: 10106
          length: 1
          type: UInt16
          readOnly: true

        - name: Max-Temperature
          address: 10107
          length: 1
          type: UInt16
          conversionFactor: 0.1
          offset: 400
          readOnly: true

        - name: Min-Temperature
          address: 10108
          length: 1
          type: UInt16
          conversionFactor: 0.1
          offset: 400
          readOnly: true

        - name: Max-Temperature-Position
          address: 10109
          length: 1
          type: UInt16
          readOnly: true

        - name: Min-Temperature-Position
          address: 10110
          length: 1
          type: UInt16
          readOnly: true

        - name: Sum-Voltage
          address: 10111
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Max-CellRes
          address: 10112
          length: 1
          type: UInt16
          readOnly: true

        - name: Max-Cell-Res-Position
          address: 10113
          length: 1
          type: UInt16
          readOnly: true

        - name: Cell-Voltage1
          address: 10114
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage2
          address: 10115
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage3
          address: 10116
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage4
          address: 10117
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage5
          address: 10118
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage6
          address: 10119
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage7
          address: 10120
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage8
          address: 10121
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage9
          address: 10122
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage10
          address: 10123
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage11
          address: 10124
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage12
          address: 10125
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage13
          address: 10126
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage14
          address: 10127
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage15
          address: 10128
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage16
          address: 10129
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage17
          address: 10130
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage18
          address: 10131
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage19
          address: 10132
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage20
          address: 10133
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage21
          address: 10134
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage22
          address: 10135
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage23
          address: 10136
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage24
          address: 10137
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage25
          address: 10138
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage26
          address: 10139
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage27
          address: 10140
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage28
          address: 10141
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage29
          address: 10142
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage30
          address: 10143
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage31
          address: 10144
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage32
          address: 10145
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage33
          address: 10146
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage34
          address: 10147
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage35
          address: 10148
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage36
          address: 10149
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage37
          address: 10150
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage38
          address: 10151
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage39
          address: 10152
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage40
          address: 10153
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage41
          address: 10154
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage42
          address: 10155
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage43
          address: 10156
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage44
          address: 10157
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage45
          address: 10158
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage46
          address: 10159
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage47
          address: 10160
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage48
          address: 10161
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage49
          address: 10162
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage50
          address: 10163
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage51
          address: 10164
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage52
          address: 10165
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage53
          address: 10166
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage54
          address: 10167
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage55
          address: 10168
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage56
          address: 10169
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage57
          address: 10170
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage58
          address: 10171
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage59
          address: 10172
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage60
          address: 10173
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage61
          address: 10174
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage62
          address: 10175
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage63
          address: 10176
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Cell-Voltage64
          address: 10177
          length: 1
          type: UInt16
          conversionFactor: 0.001
          readOnly: true

        - name: Temperature1
          address: 10178
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature2
          address: 10179
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature3
          address: 10180
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature4
          address: 10181
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature5
          address: 10182
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature6
          address: 10183
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature7
          address: 10184
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature8
          address: 10185
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature9
          address: 10186
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature10
          address: 10187
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature11
          address: 10188
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature12
          address: 10189
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature13
          address: 10190
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature14
          address: 10191
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature15
          address: 10192
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature16
          address: 10193
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature17
          address: 10194
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature18
          address: 10195
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature19
          address: 10196
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature20
          address: 10197
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature21
          address: 10198
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature22
          address: 10199
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature23
          address: 10200
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature24
          address: 10201
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature25
          address: 10202
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature26
          address: 10203
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature27
          address: 10204
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature28
          address: 10205
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature29
          address: 10206
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature30
          address: 10207
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature31
          address: 10208
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Temperature32
          address: 10209
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: BalancingFlag1
          address: 10210
          length: 1
          type: UInt16
          readOnly: true

        - name: BalancingFlag2
          address: 10211
          length: 1
          type: UInt16
          readOnly: true

        - name: BalancingFlag3
          address: 10212
          length: 1
          type: UInt16
          readOnly: true

        - name: BalancingFlag4
          address: 10213
          length: 1
          type: UInt16
          readOnly: true

        - name: Cell-DCR1
          address: 10214
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR2
          address: 10215
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR3
          address: 10216
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR4
          address: 10217
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR5
          address: 10218
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR6
          address: 10219
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR7
          address: 10220
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR8
          address: 10221
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR9
          address: 10222
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR10
          address: 10223
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR11
          address: 10224
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR12
          address: 10225
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR13
          address: 10226
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR14
          address: 10227
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR15
          address: 10228
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR16
          address: 10229
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR17
          address: 10230
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR18
          address: 10231
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR19
          address: 10232
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR20
          address: 10233
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR21
          address: 10234
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR22
          address: 10235
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR23
          address: 10236
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR24
          address: 10237
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR25
          address: 10238
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR26
          address: 10239
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR27
          address: 10240
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR28
          address: 10241
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR29
          address: 10242
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR30
          address: 10243
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR31
          address: 10244
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR32
          address: 10245
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR33
          address: 10246
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR34
          address: 10247
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR35
          address: 10248
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR36
          address: 10249
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR37
          address: 10250
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR38
          address: 10251
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR39
          address: 10252
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR40
          address: 10253
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR41
          address: 10254
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR42
          address: 10255
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR43
          address: 10256
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR44
          address: 10257
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR45
          address: 10258
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR46
          address: 10259
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR47
          address: 10260
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR48
          address: 10261
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR49
          address: 10262
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR50
          address: 10263
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR51
          address: 10264
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR52
          address: 10265
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR53
          address: 10266
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR54
          address: 10267
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR55
          address: 10268
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR56
          address: 10269
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR57
          address: 10270
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR58
          address: 10271
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR59
          address: 10272
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR60
          address: 10273
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR61
          address: 10274
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR62
          address: 10275
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR63
          address: 10276
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Cell-DCR64
          address: 10277
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Module-Fuse-Vol
          address: 10278
          length: 1
          type: UInt16
          conversionFactor: 0.1
          readOnly: true

        - name: Module-Fuse-Temperature
          address: 10279
          length: 1
          type: UInt16
          conversionFactor: 0.1
          offset: 400
          readOnly: true

        - name: Max-Balance-Resistance-Temperature
          address: 10280
          length: 1
          type: UInt16
          conversionFactor: 0.1
          offset: 400
          readOnly: true

    BmsController:
      - name: BMS-RTC1
        address: 44000
        length: 1
        type: Bit
        readOnly: false
        bits:
          bit0-5:
            name: year
            offset: 2000
          bit6-9: month
          bit10-15: day

      - name: BMS-RTC2
        address: 44001
        length: 1
        type: Bit
        readOnly: false
        bits:
          bit0-7: hour
          bit8-15: minute

      - name: EMS-Check-System-Fault-State
        address: 44002
        length: 1
        type: UInt16
        readOnly: false

# intervalTime 单位 ms
# saveDbTime 单位 min
X5SysReadMap:
  输入寄存器:
    - addr: 0x0010
      length: 10
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x0020
      length: 16
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x0030
      length: 7
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x0040
      length: 56
      intervalTime: 1000
      saveDbTime: 5

  保持寄存器:
    - addr: 0x0200
      length: 6
      intervalTime: 60000
      saveDbTime: 5

    - addr: 0x0210
      length: 48
      intervalTime: 60000
      saveDbTime: 5

    - addr: 0x0281
      length: 4
      intervalTime: 60000
      saveDbTime: 5

    - addr: 0x0288
      length: 2
      intervalTime: 60000
      saveDbTime: 5

    - addr: 0x028A
      length: 22
      intervalTime: 60000
      saveDbTime: 5

    - addr: 1
      length: 54
      intervalTime: 60000
      saveDbTime: 5

    - addr: 10000
      length: 54
      intervalTime: 60000
      saveDbTime: 5

    - addr: 10054
      length: 16
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10070
      length: 20
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10101
      length: 13
      intervalTime: 60000
      saveDbTime: 5

    - addr: 10114
      length: 64
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10178
      length: 36
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10214
      length: 67
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10301
      length: 13
      intervalTime: 60000
      saveDbTime: 5

    - addr: 10314
      length: 64
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10378
      length: 36
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10314
      length: 67
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10501
      length: 13
      intervalTime: 60000
      saveDbTime: 5

    - addr: 10514
      length: 64
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10578
      length: 36
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10514
      length: 67
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10701
      length: 13
      intervalTime: 60000
      saveDbTime: 5

    - addr: 10714
      length: 64
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10778
      length: 36
      intervalTime: 1000
      saveDbTime: 5

    - addr: 10714
      length: 67
      intervalTime: 1000
      saveDbTime: 5

    - addr: 44000
      length: 3
      intervalTime: 1000
      saveDbTime: 5

meterRegisterData:
  Protocol_1:
    保持寄存器:
      键盘参数:
        - name: REV
          address: 0x0000 # 版本号
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: UCode
          address: 0x0001 # 编程密码
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: ClrE
          address: 0x0002 # 电能清零
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: net
          address: 0x0003 # 网络选择
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: CT
          address: 0x0006 # 电流互感器倍率IrAt
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: PT
          address: 0x0007 # 电压互感器倍率UrAt
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 0.1 # 实际值乘以0.1

        - name: Disp
          address: 0x000A # 轮显时间（秒）
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: B_LCD
          address: 0x000B # 背光时间控制（秒）
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Protocol
          address: 0x002C # 协议切换
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: bAud
          address: 0x002D # 通讯波特率
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Addr
          address: 0x002E # 通讯地址
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Second
          address: 0x002F # 时间 - 秒
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Minute
          address: 0x0030 # 时间 - 分
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Hour
          address: 0x0031 # 时间 - 时
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Day
          address: 0x0032 # 时间 - 日
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Month
          address: 0x0033 # 时间 - 月
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

        - name: Year
          address: 0x0034 # 时间 - 年
          length: 1
          type: Int16
          readOnly: false
          conversionFactor: 1.0

      二次侧电参量数据:
        - name: Uab
          address: 0x2000
          length: 2
          type: Float32 # 单精度浮点数
          readOnly: true
          conversionFactor: 0.1 # 单位 V (×0.1V)

        - name: Ubc
          address: 0x2002
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 单位 V (×0.1V)

        - name: Uca
          address: 0x2004
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 单位 V (×0.1V)

        - name: Ua
          address: 0x2006
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 单位 V (×0.1V)

        - name: Ub
          address: 0x2008
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 单位 V (×0.1V)

        - name: Uc
          address: 0x200A
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 单位 V (×0.1V)

        - name: Ia
          address: 0x200C
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.001 # 单位 A (×0.001A)

        - name: Ib
          address: 0x200E
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.001 # 单位 A (×0.001A)

        - name: Ic
          address: 0x2010
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.001 # 单位 A (×0.001A)

        - name: Pt
          address: 0x2012
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 合相有功功率，单位 W (×0.1W)

        - name: Pa
          address: 0x2014
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # A相有功功率，单位 W (×0.1W)

        - name: Pb
          address: 0x2016
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # B相有功功率，单位 W (×0.1W)

        - name: Pc
          address: 0x2018
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # C相有功功率，单位 W (×0.1W)

        - name: Qt
          address: 0x201A
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 合相无功功率，单位 var (×0.1var)

        - name: Qa
          address: 0x201C
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # A相无功功率，单位 var (×0.1var)

        - name: Qb
          address: 0x201E
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # B相无功功率，单位 var (×0.1var)

        - name: Qc
          address: 0x2020
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # C相无功功率，单位 var (×0.1var)

        - name: PFt
          address: 0x202A
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.001 # 合相功率因素 (×0.001)

        - name: PFa
          address: 0x202C
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.001 # A相功率因素 (×0.001)

        - name: PFb
          address: 0x202E
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.001 # B相功率因素 (×0.001)

        - name: PFc
          address: 0x2030
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.001 # C相功率因素 (×0.001)

        - name: Freq
          address: 0x2044
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.01 # 频率，单位 Hz (×0.01Hz)

        - name: Demand
          address: 0x2050
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 0.1 # 当前有功需求，单位 W (×0.1W)

        - name: ImpEp
          address: 0x101E
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 正向有功总电能

        - name: ImpEpT1
          address: 0x1020
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 正向有功费率1电能 (尖)

        - name: ImpEpT2
          address: 0x1022
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 正向有功费率2电能 (峰)

        - name: ImpEpT3
          address: 0x1024
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 正向有功费率3电能 (平)

        - name: ImpEpT4
          address: 0x1026
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 正向有功费率4电能 (谷)

        - name: ExpEp
          address: 0x1028
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 反向有功总电能

        - name: ExpEpT1
          address: 0x102A
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 反向有功费率1电能 (尖)

        - name: ExpEpT2
          address: 0x102C
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 反向有功费率2电能 (峰)

        - name: ExpEpT3
          address: 0x102E
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 反向有功费率3电能 (平)

        - name: ExpEpT4
          address: 0x1030
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 反向有功费率4电能 (谷)

        - name: Q1Eq
          address: 0x1032
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第一象限无功总电能

        - name: Q1EqT1
          address: 0x1034
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第一象限无功费率1电能 (尖)

        - name: Q1EqT2
          address: 0x1036
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第一象限无功费率2电能 (峰)

        - name: Q1EqT3
          address: 0x1038
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第一象限无功费率3电能 (平)

        - name: Q1EqT4
          address: 0x103A
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第一象限无功费率4电能 (谷)

        - name: Q2Eq
          address: 0x103C
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第二象限无功总电能

        - name: Q2EqT1
          address: 0x103E
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第二象限无功费率1电能 (尖)

        - name: Q2EqT2
          address: 0x1040
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第二象限无功费率2电能 (峰)

        - name: Q2EqT3
          address: 0x1042
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第二象限无功费率3电能 (平)

        - name: Q2EqT4
          address: 0x1044
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第二象限无功费率4电能 (谷)

        - name: Q3Eq
          address: 0x1046
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第三象限无功总电能

        - name: Q3EqT1
          address: 0x1048
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第三象限无功费率1电能 (尖)

        - name: Q3EqT2
          address: 0x104A
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第三象限无功费率2电能 (峰)

        - name: Q3EqT3
          address: 0x104C
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第三象限无功费率3电能 (平)

        - name: Q3EqT4
          address: 0x104E
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第三象限无功费率4电能 (谷)

        - name: Q4Eq
          address: 0x1050
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第四象限无功总电能

        - name: Q4EqT1
          address: 0x1052
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第四象限无功费率1电能 (尖)

        - name: Q4EqT2
          address: 0x1054
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第四象限无功费率2电能 (峰)

        - name: Q4EqT3
          address: 0x1056
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第四象限无功费率3电能 (平)

        - name: Q4EqT4
          address: 0x1058
          length: 2
          type: Float32
          readOnly: true
          conversionFactor: 1.0 # 第四象限无功费率4电能 (谷)

      复费率参数:
        # 第一套 01~14 时区起始日期及日时段表号
        - name: First-Start-Date-01
          address: 0x6000
          length: 1
          type: Int16
          readOnly: true # 第一套 01 时区起始日期

        - name: First-Timezone-Table-id-01
          address: 0x6001
          length: 1
          type: Int16
          readOnly: true # 第一套 01 时区日时段表号

        - name: First-Start-Date-02
          address: 0x6002
          length: 1
          type: Int16
          readOnly: true # 第一套 02 时区起始日期

        - name: First-Timezone-Table-id-02
          address: 0x6003
          length: 1
          type: Int16
          readOnly: true # 第一套 02 时区日时段表号

        - name: First-Start-Date-03
          address: 0x6004
          length: 1
          type: Int16
          readOnly: true # 第一套 03 时区起始日期

        - name: First-Timezone-Table-id-03
          address: 0x6005
          length: 1
          type: Int16
          readOnly: true # 第一套 03 时区日时段表号

        - name: First-Start-Date-04
          address: 0x6006
          length: 1
          type: Int16
          readOnly: true # 第一套 04 时区起始日期

        - name: First-Timezone-Table-id-04
          address: 0x6007
          length: 1
          type: Int16
          readOnly: true # 第一套 04 时区日时段表号

        - name: First-Start-Date-05
          address: 0x6008
          length: 1
          type: Int16
          readOnly: true # 第一套 05 时区起始日期

        - name: First-Timezone-Table-id-05
          address: 0x6009
          length: 1
          type: Int16
          readOnly: true # 第一套 05 时区日时段表号

        - name: First-Start-Date-06
          address: 0x600A
          length: 1
          type: Int16
          readOnly: true # 第一套 06 时区起始日期

        - name: First-Timezone-Table-id-06
          address: 0x600B
          length: 1
          type: Int16
          readOnly: true # 第一套 06 时区日时段表号

        - name: First-Start-Date-07
          address: 0x600C
          length: 1
          type: Int16
          readOnly: true # 第一套 07 时区起始日期

        - name: First-Timezone-Table-id-07
          address: 0x600D
          length: 1
          type: Int16
          readOnly: true # 第一套 07 时区日时段表号

        - name: First-Start-Date-08
          address: 0x600E
          length: 1
          type: Int16
          readOnly: true # 第一套 08 时区起始日期

        - name: First-Timezone-Table-id-08
          address: 0x600F
          length: 1
          type: Int16
          readOnly: true # 第一套 08 时区日时段表号

        - name: First-Start-Date-09
          address: 0x6010
          length: 1
          type: Int16
          readOnly: true # 第一套 09 时区起始日期

        - name: First-Timezone-Table-id-09
          address: 0x6011
          length: 1
          type: Int16
          readOnly: true # 第一套 09 时区日时段表号

        - name: First-Start-Date-10
          address: 0x6012
          length: 1
          type: Int16
          readOnly: true # 第一套 10 时区起始日期

        - name: First-Timezone-Table-id-10
          address: 0x6013
          length: 1
          type: Int16
          readOnly: true # 第一套 10 时区日时段表号

        - name: First-Start-Date-11
          address: 0x6014
          length: 1
          type: Int16
          readOnly: true # 第一套 11 时区起始日期

        - name: First-Timezone-Table-id-11
          address: 0x6015
          length: 1
          type: Int16
          readOnly: true # 第一套 11 时区日时段表号

        - name: First-Start-Date-12
          address: 0x6016
          length: 1
          type: Int16
          readOnly: true # 第一套 12 时区起始日期

        - name: First-Timezone-Table-id-12
          address: 0x6017
          length: 1
          type: Int16
          readOnly: true # 第一套 12 时区日时段表号

        - name: First-Start-Date-13
          address: 0x6018
          length: 1
          type: Int16
          readOnly: true # 第一套 13 时区起始日期

        - name: First-Timezone-Table-id-13
          address: 0x6019
          length: 1
          type: Int16
          readOnly: true # 第一套 13 时区日时段表号

        - name: First-Start-Date-14
          address: 0x601A
          length: 1
          type: Int16
          readOnly: true # 第一套 14 时区起始日期

        - name: First-Timezone-Table-id-14
          address: 0x601B
          length: 1
          type: Int16
          readOnly: true # 第一套 14 时区日时段表号

        # 第一套第 1 日 02～14 时段起始时间 (偶数地址)
        - name: First-Day-01-Start-Time-02
          address: 0x601E
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 02 时段起始时间

        - name: First-Day-01-Start-Time-03
          address: 0x6020
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 03 时段起始时间

        - name: First-Day-01-Start-Time-04
          address: 0x6022
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 04 时段起始时间

        - name: First-Day-01-Start-Time-05
          address: 0x6024
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 05 时段起始时间

        - name: First-Day-01-Start-Time-06
          address: 0x6026
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 06 时段起始时间

        - name: First-Day-01-Start-Time-07
          address: 0x6028
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 07 时段起始时间

        - name: First-Day-01-Start-Time-08
          address: 0x602A
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 08 时段起始时间

        - name: First-Day-01-Start-Time-09
          address: 0x602C
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 09 时段起始时间

        - name: First-Day-01-Start-Time-10
          address: 0x602E
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 10 时段起始时间

        - name: First-Day-01-Start-Time-11
          address: 0x6030
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 11 时段起始时间

        - name: First-Day-01-Start-Time-12
          address: 0x6032
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 12 时段起始时间

        - name: First-Day-01-Start-Time-13
          address: 0x6034
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 13 时段起始时间

        - name: First-Day-01-Start-Time-14
          address: 0x6036
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 14 时段起始时间

        # 第一套第 1 日 02～14 时段费率号 (奇数地址)
        - name: First-Day-01-Tariff-02
          address: 0x601F
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 02 时段费率号

        - name: First-Day-01-Tariff-03
          address: 0x6021
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 03 时段费率号

        - name: First-Day-01-Tariff-04
          address: 0x6023
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 04 时段费率号

        - name: First-Day-01-Tariff-05
          address: 0x6025
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 05 时段费率号

        - name: First-Day-01-Tariff-06
          address: 0x6027
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 06 时段费率号

        - name: First-Day-01-Tariff-07
          address: 0x6029
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 07 时段费率号

        - name: First-Day-01-Tariff-08
          address: 0x602B
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 08 时段费率号

        - name: First-Day-01-Tariff-09
          address: 0x602D
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 09 时段费率号

        - name: First-Day-01-Tariff-10
          address: 0x602F
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 10 时段费率号

        - name: First-Day-01-Tariff-11
          address: 0x6031
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 11 时段费率号

        - name: First-Day-01-Tariff-12
          address: 0x6033
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 12 时段费率号

        - name: First-Day-01-Tariff-13
          address: 0x6035
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 13 时段费率号

        - name: First-Day-01-Tariff-14
          address: 0x6037
          length: 1
          type: Int16
          readOnly: true # 第一套第 1 日 14 时段费率号

# intervalTime 单位 ms
# saveDbTime 单位 min
meterReadMap:
  保持寄存器:
    - addr: 0x0000
      length: 4 # 0000H-0003H
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x0006
      length: 2 # 0006H-0007H
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x000A
      length: 2 # 000AH-000BH
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x002C
      length: 9 # 002CH-0034H
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x2000
      length: 50 # 2000H-2031AH
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x2044
      length: 2 # 2044H-2045H
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x2050
      length: 2 # 2050H-2051H
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x101E
      length: 60 # 101EH-1059H
      intervalTime: 1000
      saveDbTime: 5

    - addr: 0x6000
      length: 56 # 6000H-6037H
      intervalTime: 1000
      saveDbTime: 5

mqtt:
  # serverAddress: "tcp://mqtt.example.com:1883" # MQTT 服务器地址
  serverAddress: "tcp://***********:1883" # MQTT 服务器地址
  clientId: "EMS_Client" # 客户端 ID
  username: "EMS_Client" # 用户名
  password: "123456" # 密码
  defaultQos: 1 # 默认 QoS 级别
  topics:
    topics:
    upWard:
      dataTopic:
        topic: "EMS/PowerMeter/123456/Data"
        qos: 1
      commandTopic:
        topic: "EMS/PowerMeter/123456/Command"
        qos: 2
    downWard:
      statusTopic:
        topic: "EMS/PowerMeter/123456/Status"
        qos: 0
      alertTopic:
        topic: "EMS/PowerMeter/123456/Alert"
        qos: 1
